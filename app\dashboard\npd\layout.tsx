"use client";

import { useEffect, useState } from "react";
import { NPDSidebar } from "@/components/dashboard/npd-sidebar";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";

export default function ProductsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarWidth, setSidebarWidth] = useState<number | null>(null);
  const [minSidebarPercentage, setMinSidebarPercentage] = useState<number>(20);
  const [maxSidebarPercentage, setMaxSidebarPercentage] = useState<number>(50);

  // Calculate sidebar min/max percentages based on window size
  const calculateSidebarPercentages = () => {
    const totalWidth = window.innerWidth;
    const minWidthPercentage = (240 / totalWidth) * 100;
    const maxWidthPercentage = (600 / totalWidth) * 100;
    setMinSidebarPercentage(Math.max(minWidthPercentage, 12));
    setMaxSidebarPercentage(Math.min(maxWidthPercentage, 50));
  };

  // Initialize sidebar width and handle window resize
  useEffect(() => {
    if (sidebarWidth === null) {
      const savedWidth = localStorage.getItem("npd_sidebar_width");
      if (savedWidth) {
        setSidebarWidth(parseFloat(savedWidth));
      } else {
        setSidebarWidth(30); // Default to 30% if no saved width
      }
    }

    calculateSidebarPercentages();

    const handleResize = () => {
      calculateSidebarPercentages();
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [sidebarWidth]);

  // Save sidebar width to localStorage
  const handleLayoutChange = (sizes: number[]) => {
    const newSidebarWidth = sizes[0];
    setSidebarWidth(newSidebarWidth);
    localStorage.setItem("npd_sidebar_width", newSidebarWidth.toString());
  };

  // Wait until sidebarWidth is loaded from localStorage
  if (sidebarWidth === null) {
    return null;
  }

  return (
    <div className="-mx-3 -mr-4 -my-1 h-full">
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full"
        onLayout={handleLayoutChange}
      >
        {/* Sidebar Panel */}
        <ResizablePanel
          defaultSize={sidebarWidth}
          minSize={minSidebarPercentage}
          maxSize={maxSidebarPercentage}
          className="h-full"
        >
          <NPDSidebar />
        </ResizablePanel>
        {/* Resizable Handle */}
        <ResizableHandle withHandle />
        {/* Main Content Panel */}
        <ResizablePanel className="flex-1 h-full overflow-hidden w-full">
          {children}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
