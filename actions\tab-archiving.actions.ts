"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";
import { createProductActivityLog } from "@/actions/activity-log.actions";

/**
 * Archive a product tab (soft delete) - only for tab creator, admin, or super admin
 */
export async function archiveTab(npdProductTabId: string, reason?: string) {
  const user = await validateServerAction();

  try {
    // Get tab details with product and user info
    const tab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
            userId: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!tab) {
      throw new Error("Tab not found");
    }

    if (tab.archived) {
      throw new Error("Tab is already archived");
    }

    // Check permissions
    const hasAdminRole =
      user.roles.includes("ADMIN") || user.roles.includes("SUPER_ADMIN");
    const isTabCreator = tab.userId === user.userId;
    const isProductOwner = tab.npdProduct.userId === user.userId;

    if (!isTabCreator && !isProductOwner && !hasAdminRole) {
      throw new Error("You don't have permission to archive this tab");
    }

    // Archive the tab
    await prisma.nPDProductTab.update({
      where: { id: npdProductTabId },
      data: {
        archived: true,
        archivedAt: new Date(),
        archivedBy: user.userId,
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId: tab.npdProduct.id,
      userId: user.userId,
      action: "tab_archived",
      description: `Archived tab "${tab.tabName}"${
        reason ? `: ${reason}` : ""
      }`,
      metadata: {
        field: "tab_archived",
        npdProductTabId: tab.id,
        tabName: tab.tabName,
        archivedAt: new Date().toISOString(),
        archivedBy: user.userId,
        reason: reason || null,
      },
    });

    return {
      success: true,
      message: `Tab "${tab.tabName}" has been archived`,
      tabName: tab.tabName,
    };
  } catch (error) {
    console.error("Error archiving tab:", error);
    throw error;
  }
}

/**
 * Restore archived tab - only for admins
 */
export async function restoreTab(npdProductTabId: string) {
  const user = await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    // Get tab details
    const tab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!tab) {
      throw new Error("Tab not found");
    }

    if (!tab.archived) {
      throw new Error("Tab is not archived");
    }

    // Restore the tab
    await prisma.nPDProductTab.update({
      where: { id: npdProductTabId },
      data: {
        archived: false,
        archivedAt: null,
        archivedBy: null,
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId: tab.npdProduct.id,
      userId: user.userId,
      action: "tab_restored",
      description: `Restored tab "${tab.tabName}" from archive`,
      metadata: {
        field: "tab_restored",
        npdProductTabId: tab.id,
        tabName: tab.tabName,
        restoredAt: new Date().toISOString(),
        restoredBy: user.userId,
      },
    });

    return {
      success: true,
      message: `Tab "${tab.tabName}" has been restored`,
      tabName: tab.tabName,
    };
  } catch (error) {
    console.error("Error restoring tab:", error);
    throw error;
  }
}

/**
 * Permanently delete tab - only for super admins
 */
export async function permanentlyDeleteTab(npdProductTabId: string) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    // Get tab details
    const tab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!tab) {
      throw new Error("Tab not found");
    }

    // Use transaction to delete all related data
    await prisma.$transaction(async (tx) => {
      // Delete tab completion records
      await tx.nPDProductTabCompletion.deleteMany({
        where: { npdProductTabId: tab.id },
      });

      // Delete tab history
      await tx.nPDProductTabHistory.deleteMany({
        where: { npdProductTabId: tab.id },
      });

      // Delete activity logs related to this tab
      await tx.nPDProductActivityLog.deleteMany({
        where: {
          npdProductTabId: tab.id,
        },
      });

      // Delete the tab itself
      await tx.nPDProductTab.delete({
        where: { id: tab.id },
      });
    });

    return {
      success: true,
      message: `Tab "${tab.tabName}" has been permanently deleted`,
      tabName: tab.tabName,
    };
  } catch (error) {
    console.error("Error permanently deleting tab:", error);
    throw error;
  }
}

/**
 * Request tab deletion - sends notification to admins and product owner
 */
export async function requestTabDeletion(
  npdProductTabId: string,
  reason?: string
) {
  const user = await validateServerAction();

  try {
    // Get tab details with product and user info
    const tab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
            userId: true,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!tab) {
      throw new Error("Tab not found");
    }

    if (tab.archived) {
      throw new Error("Tab is already archived");
    }

    // Get all admins and super admins
    const admins = await prisma.user.findMany({
      where: {
        roles: {
          some: {
            name: {
              in: ["ADMIN", "SUPER_ADMIN"],
            },
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    // Create notifications for admins and product owner
    const notificationRecipients = [...admins];

    // Add product owner if they exist and aren't already in the list
    if (
      tab.npdProduct.user &&
      !admins.some((admin) => admin.id === tab.npdProduct.user!.id)
    ) {
      notificationRecipients.push(tab.npdProduct.user);
    }

    // Add tab creator if they exist and aren't already in the list
    if (
      tab.user &&
      !notificationRecipients.some((recipient) => recipient.id === tab.user!.id)
    ) {
      notificationRecipients.push(tab.user);
    }

    // Remove the requesting user from recipients
    const filteredRecipients = notificationRecipients.filter(
      (recipient) => recipient.id !== user.userId
    );

    // Create notifications
    const notifications = filteredRecipients.map((recipient) => ({
      userId: recipient.id,
      type: "TAB_DELETE_REQUEST",
      title: "Tab Deletion Request",
      message: `${user.name} has requested deletion of tab "${
        tab.tabName
      }" in product "${tab.npdProduct.name}"${reason ? `: ${reason}` : ""}`,
      data: {
        npdProductTabId: tab.id,
        tabName: tab.tabName,
        npdProductId: tab.npdProduct.id,
        productName: tab.npdProduct.name,
        requestedBy: user.userId,
        requestedByName: user.name,
        reason: reason || null,
      },
    }));

    if (notifications.length > 0) {
      await prisma.notification.createMany({
        data: notifications,
      });
    }

    // Create activity log
    await createProductActivityLog({
      npdProductId: tab.npdProduct.id,
      userId: user.userId,
      action: "tab_deletion_requested",
      description: `Requested deletion of tab "${tab.tabName}"${
        reason ? `: ${reason}` : ""
      }`,
      metadata: {
        field: "tab_deletion_request",
        npdProductTabId: tab.id,
        tabName: tab.tabName,
        reason: reason || null,
        notificationsSent: notifications.length,
      },
    });

    return {
      success: true,
      message: "Tab deletion request sent to administrators and product owner",
      notificationsSent: notifications.length,
    };
  } catch (error) {
    console.error("Error requesting tab deletion:", error);
    throw error;
  }
}
