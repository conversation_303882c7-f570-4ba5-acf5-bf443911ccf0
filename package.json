{"name": "quickt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed-reset-all": "tsx prisma/seed.ts --reset-all", "db:seed-sync": "tsx prisma/seed.ts --sync-only", "db:seed-nuclear": "tsx prisma/seed.ts --nuclear", "db:seed": "npm run db:seed-sync", "postinstall": "prisma generate"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@prisma/adapter-neon": "^6.11.1", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@types/papaparse": "^5.3.16", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.11", "lucide-react": "^0.525.0", "next": "15.3.4", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "react-spinners": "^0.17.0", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "ws": "^8.18.3", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "bufferutil": "^4.0.9", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}