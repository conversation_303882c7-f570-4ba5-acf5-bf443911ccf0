// Product types for context
export interface ProductSummary {
  id: string;
  name: string;
  brand: string;
  slug: string;
  stage: string;
  description?: string | null;
  imageUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
  isSubscribed: boolean;
}

export interface ProductWithTabs {
  id: string;
  name: string;
  brand: string;
  slug: string;
  stage: string;
  description?: string | null;
  imageUrl?: string | null;
  userId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  isSubscribed?: boolean;
  tabs: ProductTab[];
}

export interface ProductTab {
  id: string;
  tabName: string;
  order: number;
  fields?: Record<string, unknown> | null;
  createdAt: Date;
  updatedAt: Date;
  productId: string;
}

// Form types
export interface CreateProductData {
  name: string;
  brand: string;
  stage: string;
  description?: string;
  imageUrl?: string;
  subscribedUserIds?: string[];
}

export interface UpdateProductData {
  name?: string;
  brand?: string;
  stage?: string;
  description?: string;
  imageUrl?: string;
}

// Tab data for adding new tabs
export interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

// User types
export interface User {
  id: string;
  name: string;
  email: string;
  imageUrl?: string;
}

// Message types for notes/activity
export interface Message {
  id: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
  user: User;
  isRead: boolean;
}

export interface AppContextType {
  _placeholder?: boolean;
}
