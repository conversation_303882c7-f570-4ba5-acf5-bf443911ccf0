"use client";

import React, { useState } from "react";
import { Star, StarOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import Spinner from "@/components/spinner";
import { useNPDContext } from "@/context/npd-context";
import { toast } from "sonner";

interface NPDSubscriptionToggleProps {
  productId: string;
  productName: string;
  isSubscribed: boolean;
  size?: "sm" | "md" | "lg";
  showText?: boolean;
  variant?: "default" | "outline" | "ghost";
}

export function NPDSubscriptionToggle({
  productId,
  productName,
  isSubscribed,
  size = "md",
  showText = false,
  variant = "ghost",
}: NPDSubscriptionToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { subscribeToNPD, unsubscribeFromNPD } = useNPDContext();

  const handleToggle = async () => {
    setIsLoading(true);
    try {
      if (isSubscribed) {
        await unsubscribeFromNPD(productId);
        toast.success(`Unsubscribed from ${productName}`, {
          description: `You will no longer see ${productName} in your dashboard`,
        });
      } else {
        await subscribeToNPD(productId);
        toast.success(`Subscribed to ${productName}`, {
          description: `${productName} will now appear in your dashboard`,
        });
      }
    } catch (error) {
      console.error("Error toggling subscription:", error);
      toast.error("Failed to update subscription", {
        description: "Please try again or contact support",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      button: "h-7 px-2",
      icon: "h-3 w-3",
      text: "text-xs",
    },
    md: {
      button: "h-8 px-3",
      icon: "h-4 w-4",
      text: "text-sm",
    },
    lg: {
      button: "h-9 px-4",
      icon: "h-5 w-5",
      text: "text-base",
    },
  };

  const config = sizeConfig[size];

  return (
    <Button
      variant={variant}
      size="sm"
      onClick={(event) => {
        event.stopPropagation();
        handleToggle();
      }}
      disabled={isLoading}
      className={`${config.button} ${
        isSubscribed
          ? "text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
          : "text-muted-foreground hover:text-foreground"
      }`}
      title={
        isSubscribed
          ? `Unsubscribe from ${productName}`
          : `Subscribe to ${productName}`
      }
    >
      {isLoading ? (
        <>
          <Spinner loading={true} size={8} />
          {showText && <span className={`ml-2 ${config.text}`}>...</span>}
        </>
      ) : (
        <>
          {isSubscribed ? (
            <Star className={`${config.icon} fill-current`} />
          ) : (
            <StarOff className={config.icon} />
          )}
          {showText && (
            <span className={`ml-2 ${config.text}`}>
              {isSubscribed ? "Subscribed" : "Subscribe"}
            </span>
          )}
        </>
      )}
    </Button>
  );
}
