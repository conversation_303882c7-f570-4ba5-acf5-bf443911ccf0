"use client";

import React, { useState, useEffect, useRef } from "react";
import { useNotifications } from "@/hooks/use-notifications";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Calendar,
  Edit2,
  Eye,
  EyeOff,
  Save,
  Star,
  User,
  X,
  ExternalLink,
  Activity,
  Bell,
  Package,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { updateUserName, type UserProfileData } from "@/actions/user.actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface ProfileContentProps {
  userProfile: UserProfileData;
}

export function ProfileContent({ userProfile }: ProfileContentProps) {
  const notificationsSectionRef = useRef<HTMLDivElement>(null);
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedName, setEditedName] = useState(userProfile.name);
  const [isUpdating, setIsUpdating] = useState(false);
  const router = useRouter();

  const handleNameEdit = async () => {
    if (!isEditingName) {
      setIsEditingName(true);
      return;
    }

    if (editedName.trim() === userProfile.name) {
      setIsEditingName(false);
      return;
    }

    setIsUpdating(true);
    try {
      await updateUserName(editedName.trim());
      toast.success("Name updated successfully");
      setIsEditingName(false);
      router.refresh(); // Refresh to show updated name
    } catch (error) {
      toast.error("Failed to update name");
      console.error("Error updating name:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const cancelEdit = () => {
    setEditedName(userProfile.name);
    setIsEditingName(false);
  };

  const navigateToProduct = (productSlug: string) => {
    router.push(`/dashboard/npd/${productSlug}`);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const getRoleColor = (roleName: string) => {
    switch (roleName.toLowerCase()) {
      case "super_admin":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "admin":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "manager":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "user":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  // Notification actions using real hook

  const {
    notifications: allNotifications,
    markAsRead,
    markAsUnread,
    hideNotification,
    refresh,
  } = useNotifications({ enabled: true, limit: 100, includeHidden: true });

  // Show all notifications (including hidden) in profile page
  const [allLocalNotifications, setAllLocalNotifications] =
    useState(allNotifications);
  useEffect(() => {
    setAllLocalNotifications(allNotifications);
  }, [allNotifications]);

  const handleToggleRead = async (id: string, read: boolean) => {
    if (read) {
      await markAsUnread([id]);
    } else {
      await markAsRead([id]);
    }
    refresh();
  };

  const handleHide = async (id: string) => {
    // Optimistically mark as hidden in local state
    setAllLocalNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, hidden: true } : n))
    );
    await hideNotification(id);
    refresh();
  };
  // Scroll to notifications section if hash is present
  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      window.location.hash === "#notifications-section"
    ) {
      setTimeout(() => {
        notificationsSectionRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100); // slight delay to ensure DOM is ready
    }
  }, []);

  return (
    <div className="h-full w-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div>
            <h1 className="text-2xl font-bold">Profile</h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Basic Information Card */}
          <Card className="md:col-span-2 lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={userProfile.image || ""} />
                  <AvatarFallback className="text-lg">
                    {getInitials(userProfile.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    {isEditingName ? (
                      <div className="flex items-center gap-2">
                        <Input
                          value={editedName}
                          onChange={(e) => setEditedName(e.target.value)}
                          className="h-7 text-sm"
                          disabled={isUpdating}
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleNameEdit}
                          disabled={isUpdating}
                          className="h-7 w-7 p-0"
                        >
                          <Save className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={cancelEdit}
                          disabled={isUpdating}
                          className="h-7 w-7 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        <h3 className="font-medium">{userProfile.name}</h3>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={handleNameEdit}
                          className="h-6 w-6 p-0"
                        >
                          <Edit2 className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.email}
                  </p>
                  {userProfile.provider && (
                    <Badge variant="outline" className="text-xs">
                      {userProfile.provider}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Details Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Account Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-xs text-muted-foreground">Joined</Label>
                <p className="text-sm font-medium">
                  {formatDate(userProfile.createdAt)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(userProfile.createdAt, {
                    addSuffix: true,
                  })}
                </p>
              </div>
              {userProfile.lastLoginAt && (
                <div>
                  <Label className="text-xs text-muted-foreground">
                    Last Login
                  </Label>
                  <p className="text-sm font-medium">
                    {formatDate(userProfile.lastLoginAt)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(userProfile.lastLoginAt, {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Roles Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Badge className="h-5 w-5" />
                Roles & Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {userProfile.roles.map((role) => (
                  <Badge
                    key={role.id}
                    className={getRoleColor(role.name)}
                    variant="secondary"
                  >
                    {role.name.replace("_", " ")}
                  </Badge>
                ))}
                {userProfile.roles.length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    No roles assigned
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Subscribed Products Card */}
          <Card className="md:col-span-2 lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Subscribed Products ({userProfile.subscriptions.length})
              </CardTitle>
              <CardDescription>
                Products you&apos;re currently subscribed to
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userProfile.subscriptions.length > 0 ? (
                <div className="grid gap-3 sm:grid-cols-2">
                  {userProfile.subscriptions.map((subscription) => (
                    <div
                      key={subscription.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() =>
                        navigateToProduct(subscription.npdProduct.slug)
                      }
                    >
                      <div className="flex items-center gap-3">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <div>
                          <p className="font-medium text-sm">
                            {subscription.npdProduct.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {subscription.npdProduct.brand}
                          </p>
                        </div>
                      </div>
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No product subscriptions yet
                </p>
              )}
            </CardContent>
          </Card>

          {/* Recent Activity Card */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>Your recent actions</CardDescription>
            </CardHeader>
            <CardContent>
              {userProfile.recentActivity.length > 0 ? (
                <div className="space-y-3">
                  {userProfile.recentActivity.slice(0, 5).map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{activity.action}</p>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {activity.description}
                        </p>
                        {activity.npdProduct && (
                          <p className="text-xs text-blue-600 dark:text-blue-400">
                            {activity.npdProduct.name}
                            {activity.npdProductTab &&
                              ` • ${activity.npdProductTab.tabName}`}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(activity.createdAt, {
                            addSuffix: true,
                          })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No recent activity
                </p>
              )}
            </CardContent>
          </Card>

          {/* Notifications Summary Card */}
          <Card className="md:col-span-2 lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Overview of your notification activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-3">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Bell className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="font-medium">
                      {userProfile.notifications.total}
                    </p>
                    <p className="text-sm text-muted-foreground">Total</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <Eye className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <p className="font-medium">
                      {userProfile.notifications.unread}
                    </p>
                    <p className="text-sm text-muted-foreground">Unread</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-900 rounded-lg flex items-center justify-center">
                    <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <p className="font-medium">
                      {userProfile.notifications.hidden}
                    </p>
                    <p className="text-sm text-muted-foreground">Hidden</p>
                  </div>
                </div>
              </div>

              {/* All Notifications Section */}
              <div
                className="mt-4 space-y-2"
                id="notifications-section"
                ref={notificationsSectionRef}
              >
                {allLocalNotifications.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    No notifications found
                  </p>
                ) : (
                  allLocalNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border flex items-center justify-between gap-3 cursor-pointer hover:bg-accent/50 transition-colors ${
                        notification.read
                          ? "opacity-60 bg-muted/50"
                          : "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800"
                      }`}
                    >
                      <div className="flex items-center gap-3 flex-1">
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        )}
                        <div>
                          <p className="text-sm font-medium">
                            {notification.title}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(notification.createdAt).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() =>
                            handleToggleRead(notification.id, notification.read)
                          }
                          title={
                            notification.read
                              ? "Mark as unread"
                              : "Mark as read"
                          }
                        >
                          {notification.read ? (
                            <EyeOff className="h-3 w-3" />
                          ) : (
                            <Eye className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => handleHide(notification.id)}
                          title="Hide notification"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
