generator client {
  provider        = "prisma-client-js"
  output          = "../lib/generated/prisma"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                           String                         @id @default(cuid())
  name                         String
  email                        String                         @unique
  image                        String?
  provider                     String?
  providerId                   String?
  npdProducts                  NPDProduct[]                   @relation("UserNPDProducts")
  archivedNPDProducts          NPDProduct[]                   @relation("ArchivedNPDProducts")
  roles                        Role[]                         @relation("UserRoles")
  notifications                Notification[]
  npdProductActivityLogs       NPDProductActivityLog[]
  npdProductActivityReadStatus NPDProductActivityReadStatus[]
  npdProductChats              NPDProductChat[]
  npdProductChatReadStatus     NPDProductChatReadStatus[]
  npdProductSubscriptions      NPDProductSubscription[]
  npdProductTabs               NPDProductTab[]                @relation("UserNPDProductTabs")
  archivedNPDProductTabs       NPDProductTab[]                @relation("ArchivedNPDProductTabs")
  lastLoginAt                  DateTime?
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
}

model Role {
  id          String       @id @default(cuid())
  name        String       @unique
  users       User[]       @relation("UserRoles")
  permissions Permission[] @relation("RolePermissions")
}

model Permission {
  id    String @id @default(cuid())
  code  String @unique
  roles Role[] @relation("RolePermissions")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  type      String // "role_update", "system", etc.
  title     String
  message   String
  data      Json? // Additional data (roles, etc.)
  read      Boolean  @default(false)
  hidden    Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@index([userId, read])
  @@index([userId, hidden])
}

model NPDProduct {
  id                 String                         @id @default(cuid())
  name               String
  slug               String                         @unique
  brand              String
  stage              String                         @default("Ideation")
  description        String?
  imageUrl           String?
  archived           Boolean                        @default(false)
  archivedAt         DateTime?
  archivedBy         String?
  archivedByUser     User?                          @relation("ArchivedNPDProducts", fields: [archivedBy], references: [id], onDelete: SetNull)
  tabs               NPDProductTab[]
  userId             String?
  user               User?                          @relation("UserNPDProducts", fields: [userId], references: [id], onDelete: SetNull)
  activityReadStatus NPDProductActivityReadStatus[]
  activityLogs       NPDProductActivityLog[]
  chats              NPDProductChat[]
  subscriptions      NPDProductSubscription[]
  tabCompletions     NPDProductTabCompletion[]
  createdAt          DateTime                       @default(now())
  updatedAt          DateTime                       @updatedAt

  @@index([archived])
  @@index([userId, archived])
}

model NPDProductTab {
  id             String                   @id @default(cuid())
  tabName        String
  order          Int                      @default(0)
  npdProductId   String
  npdProduct     NPDProduct               @relation(fields: [npdProductId], references: [id])
  fields         Json
  archived       Boolean                  @default(false)
  archivedAt     DateTime?
  archivedBy     String?
  archivedByUser User?                    @relation("ArchivedNPDProductTabs", fields: [archivedBy], references: [id], onDelete: SetNull)
  userId         String?
  user           User?                    @relation("UserNPDProductTabs", fields: [userId], references: [id], onDelete: SetNull)
  history        NPDProductTabHistory[]
  activityLogs   NPDProductActivityLog[]
  completion     NPDProductTabCompletion?
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt

  @@index([archived])
  @@index([npdProductId, archived])
  @@index([userId, archived])
}

model NPDProductTabHistory {
  id              String        @id @default(cuid())
  npdProductTabId String
  npdProductTab   NPDProductTab @relation(fields: [npdProductTabId], references: [id])
  fields          Json
  createdAt       DateTime      @default(now())
}

model NPDProductTabCompletion {
  id              String        @id @default(cuid())
  npdProductId    String
  npdProductTabId String        @unique
  completion      Int           @default(0)
  npdProduct      NPDProduct    @relation(fields: [npdProductId], references: [id], onDelete: Cascade)
  npdProductTab   NPDProductTab @relation(fields: [npdProductTabId], references: [id], onDelete: Cascade)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@unique([npdProductId, npdProductTabId])
}

model NPDProductActivityLog {
  id              String                         @id @default(cuid())
  npdProductTabId String?
  npdProductTab   NPDProductTab?                 @relation(fields: [npdProductTabId], references: [id], onDelete: Cascade)
  npdProductId    String
  npdProduct      NPDProduct                     @relation(fields: [npdProductId], references: [id], onDelete: Cascade)
  userId          String?
  user            User?                          @relation(fields: [userId], references: [id], onDelete: SetNull)
  action          String // "created", "updated", "deleted", "imported_csv", etc.
  description     String // Human readable description
  changes         Json? // What changed (before/after for updates)
  metadata        Json? // Additional context (e.g., number of rows imported)
  readStatus      NPDProductActivityReadStatus[]
  createdAt       DateTime                       @default(now())

  @@index([npdProductTabId, createdAt])
  @@index([npdProductId, createdAt])
}

// NPD Product Activity read status tracking
model NPDProductActivityReadStatus {
  id           String                @id @default(cuid())
  userId       String
  activityId   String
  npdProductId String
  readAt       DateTime              @default(now())
  user         User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  activity     NPDProductActivityLog @relation(fields: [activityId], references: [id], onDelete: Cascade)
  npdProduct   NPDProduct            @relation(fields: [npdProductId], references: [id], onDelete: Cascade)

  @@unique([userId, activityId])
  @@index([userId, npdProductId])
  @@index([npdProductId, readAt])
}

// NPD Product chat system
model NPDProductChat {
  id           String                     @id @default(cuid())
  npdProductId String
  userId       String
  message      String
  mentions     String[]
  createdAt    DateTime                   @default(now())
  updatedAt    DateTime                   @updatedAt
  user         User                       @relation(fields: [userId], references: [id])
  npdProduct   NPDProduct                 @relation(fields: [npdProductId], references: [id])
  readStatus   NPDProductChatReadStatus[]

  @@index([npdProductId, createdAt])
  @@index([userId])
}

// NPD Product Chat read status tracking
model NPDProductChatReadStatus {
  id           String         @id @default(cuid())
  userId       String
  chatId       String
  npdProductId String
  readAt       DateTime       @default(now())
  user         User           @relation(fields: [userId], references: [id])
  chat         NPDProductChat @relation(fields: [chatId], references: [id])

  @@unique([userId, chatId])
  @@index([userId, npdProductId])
}

// NPD Product subscription model for many-to-many user-NPD Product subscriptions
model NPDProductSubscription {
  id           String     @id @default(cuid())
  userId       String
  npdProductId String
  createdAt    DateTime   @default(now())
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  npdProduct   NPDProduct @relation(fields: [npdProductId], references: [id], onDelete: Cascade)

  @@unique([userId, npdProductId])
  @@index([userId])
  @@index([npdProductId])
}
