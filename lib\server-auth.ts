import { getCurrentUser, AuthPayload } from "@/lib/auth";
import { redirect } from "next/navigation";

// Helper function to check if user has any of the required roles
function hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
  return requiredRoles.some((role) => userRoles.includes(role));
}

/**
 * Validates that a user is authenticated for server actions
 * @param requiredRoles - Optional array of roles required for this action
 * @returns AuthPayload if user is authenticated and authorized
 * @throws Will redirect to home if not authenticated
 */
export async function validateServerAction(
  requiredRoles?: string[]
): Promise<AuthPayload> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      redirect("/");
    }

    // Check if specific roles are required
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRequiredRole = hasAnyRole(user.roles, requiredRoles);

      if (!hasRequiredRole) {
        throw new Error("Insufficient permissions");
      }
    }

    return user;
  } catch (error) {
    console.error("Server action validation failed:", error);
    if (error instanceof Error && error.message.includes("NEXT_REDIRECT")) {
      throw error; // Re-throw redirect errors
    }
    redirect("/");
  }
}

/**
 * Validates that a user has admin role for admin-only server actions
 * @returns AuthPayload if user is admin
 */
export async function validateAdminAction(): Promise<AuthPayload> {
  return await validateServerAction(["ADMIN", "SUPER_ADMIN"]);
}

/**
 * Validates that a user has super admin role for super admin-only server actions
 * @returns AuthPayload if user is super admin
 */
export async function validateSuperAdminAction(): Promise<AuthPayload> {
  return await validateServerAction(["SUPER_ADMIN"]);
}

/**
 * Creates a wrapper for server actions that automatically validates authentication
 */
export function withAuth<T extends unknown[], R>(
  action: (user: AuthPayload, ...args: T) => Promise<R>,
  requiredRoles?: string[]
) {
  return async (...args: T): Promise<R> => {
    const user = await validateServerAction(requiredRoles);
    return action(user, ...args);
  };
}

/**
 * Creates a wrapper for admin-only server actions
 */
export function withAdminAuth<T extends unknown[], R>(
  action: (user: AuthPayload, ...args: T) => Promise<R>
) {
  return withAuth(action, ["ADMIN", "SUPER_ADMIN"]);
}

/**
 * Creates a wrapper for super admin-only server actions
 */
export function withSuperAdminAuth<T extends unknown[], R>(
  action: (user: AuthPayload, ...args: T) => Promise<R>
) {
  return withAuth(action, ["SUPER_ADMIN"]);
}
