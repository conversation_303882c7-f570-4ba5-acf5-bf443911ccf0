"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Edit,
  Trash2,
  Upload,
  Settings,
  Tag,
  AlertCircle,
  CheckCheck,
  Eye,
  EyeOff,
  Activity,
} from "lucide-react";
import { useState, useEffect, useRef, useCallback } from "react";
import { useActivityData } from "@/hooks/use-activity-data";
import { useActivityActions } from "@/hooks/use-activity-actions";
import type { ActivityLog } from "@/actions/activity.actions";

interface ProductActivityPanelProps {
  productId: string;
}

export function ProductActivityPanel({ productId }: ProductActivityPanelProps) {
  const [filter, setFilter] = useState<string>("all");

  // Optimistic update state
  const [optimisticLogs, setOptimisticLogs] = useState<ActivityLog[] | null>(
    null
  );
  const [optimisticUnreadCount, setOptimisticUnreadCount] = useState<
    number | null
  >(null);

  // Pending sync operations (for batching)
  const pendingSyncRef = useRef<Map<string, boolean>>(new Map());
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use the new hooks for activity data and actions
  const {
    activityLogs: serverLogs,
    isLoading,
    error,
    unreadCount: serverUnreadCount,
    refresh,
  } = useActivityData(productId);

  // Use optimistic data when available, fallback to server data
  const logs = optimisticLogs || serverLogs;
  const unreadCount =
    optimisticUnreadCount !== null ? optimisticUnreadCount : serverUnreadCount;

  const { toggleReadStatus: toggleActivityReadStatus, markAllAsRead } =
    useActivityActions(productId);

  // Reset optimistic state when server data changes (e.g., new activities added)
  useEffect(() => {
    if (optimisticLogs && serverLogs.length !== optimisticLogs.length) {
      setOptimisticLogs(null);
      setOptimisticUnreadCount(null);
    }
  }, [serverLogs.length, optimisticLogs]);

  // Debounced sync function to batch multiple rapid operations
  const debouncedSync = useCallback(async () => {
    if (pendingSyncRef.current.size === 0) return;

    const operations = Array.from(pendingSyncRef.current.entries());
    pendingSyncRef.current.clear();

    // Execute all pending operations
    for (const [activityId, newReadStatus] of operations) {
      try {
        await toggleActivityReadStatus(activityId, !newReadStatus);
      } catch (error) {
        console.error(
          `Failed to sync read status for activity ${activityId}:`,
          error
        );
        // On error, trigger a refresh to get correct server state
        refresh();
        return;
      }
    }
  }, [toggleActivityReadStatus, refresh]);

  // Optimistic update functions for better UX
  const handleToggleReadStatus = useCallback(
    (activityId: string, isCurrentlyRead: boolean) => {
      // Optimistic update: immediately update UI
      const newOptimisticLogs = (optimisticLogs || serverLogs).map((log) =>
        log.id === activityId ? { ...log, isRead: !isCurrentlyRead } : log
      );

      // Update local state immediately for instant feedback
      setOptimisticLogs(newOptimisticLogs);

      // Update unread count optimistically
      const currentUnreadCount =
        optimisticUnreadCount !== null
          ? optimisticUnreadCount
          : serverUnreadCount;
      const newUnreadCount = !isCurrentlyRead
        ? Math.max(0, currentUnreadCount - 1) // Mark as read: decrease count
        : currentUnreadCount + 1; // Mark as unread: increase count
      setOptimisticUnreadCount(newUnreadCount);

      // Add to pending sync operations
      pendingSyncRef.current.set(activityId, !isCurrentlyRead);

      // Clear existing timeout and set new one for debounced sync
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      syncTimeoutRef.current = setTimeout(() => {
        debouncedSync();
      }, 500); // 500ms debounce delay
    },
    [
      optimisticLogs,
      serverLogs,
      optimisticUnreadCount,
      serverUnreadCount,
      debouncedSync,
    ]
  );

  const handleMarkAllAsRead = useCallback(async () => {
    // Clear any pending individual sync operations since we're marking all as read
    pendingSyncRef.current.clear();
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    // Optimistic update: mark all as read immediately
    const newOptimisticLogs = (optimisticLogs || serverLogs).map((log) => ({
      ...log,
      isRead: true,
    }));
    setOptimisticLogs(newOptimisticLogs);
    setOptimisticUnreadCount(0);

    // Sync with server in background
    try {
      await markAllAsRead();
      // Success - optimistic update was correct
    } catch (error) {
      // Revert optimistic update on error
      console.error("Failed to mark all as read:", error);
      setOptimisticLogs(null);
      setOptimisticUnreadCount(null);
      refresh(); // Refresh to get correct server state
    }
  }, [optimisticLogs, serverLogs, markAllAsRead, refresh]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);
  const getActionIcon = (
    action: string,
    metadata?: Record<string, unknown> | null
  ) => {
    // Check if this is a product-level update (stage or brand change)
    if (action === "updated" && metadata?.field) {
      if (metadata.field === "stage") {
        return <Tag className="h-4 w-4 text-blue-600" />;
      }
      if (metadata.field === "brand") {
        return <Tag className="h-4 w-4 text-purple-600" />;
      }
    }

    switch (action) {
      case "created":
        return <Plus className="h-4 w-4 text-green-600" />;
      case "updated":
        return <Edit className="h-4 w-4 text-blue-600" />;
      case "deleted":
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case "uploaded":
        return <Upload className="h-4 w-4 text-indigo-600" />;
      case "settings_updated":
        return <Settings className="h-4 w-4 text-gray-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionColor = (
    action: string,
    metadata?: Record<string, unknown> | null
  ) => {
    // Check if this is a product-level update (stage or brand change)
    if (action === "updated" && metadata?.field) {
      if (metadata.field === "stage") {
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800";
      }
      if (metadata.field === "brand") {
        return "bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800";
      }
    }

    switch (action) {
      case "created":
        return "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800";
      case "updated":
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800";
      case "deleted":
        return "bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800";
      case "uploaded":
        return "bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800";
      case "settings_updated":
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800";
    }
  };

  const formatTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Filter logs based on selected filter
  const filteredLogs = logs.filter((log) => {
    if (filter === "all") return true;
    return log.action === filter;
  });

  // Get unique actions for filter buttons
  const uniqueActions = Array.from(new Set(logs.map((log) => log.action)));

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        {/* Header skeleton */}
        <div className="p-4 border-b space-y-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-8 w-20" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-6 w-12" />
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-14" />
          </div>
        </div>

        {/* Content skeleton */}
        <div className="flex-1 p-4 space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-start gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-4 border-b">
          <h3 className="font-medium text-sm">Activity Log</h3>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Failed to load activity
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-sm">Activity Log</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                {unreadCount} new
              </Badge>
            )}
          </div>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="text-xs h-7 px-2"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </div>

        {/* Filter buttons */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant={filter === "all" ? "default" : "ghost"}
            size="sm"
            onClick={() => setFilter("all")}
            className="text-xs h-6 px-2"
          >
            All ({logs.length})
          </Button>
          {uniqueActions.map((action) => {
            const count = logs.filter((log) => log.action === action).length;
            return (
              <Button
                key={action}
                variant={filter === action ? "default" : "ghost"}
                size="sm"
                onClick={() => setFilter(action)}
                className="text-xs h-6 px-2 capitalize"
              >
                {action} ({count})
              </Button>
            );
          })}
        </div>
      </div>

      {/* Activity List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {filteredLogs.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-xs text-muted-foreground">
              {filter === "all"
                ? "No activity yet."
                : `No ${filter} activities yet.`}
            </div>
          </div>
        ) : (
          filteredLogs.map((log: ActivityLog) => {
            const isUnread = !log.isRead;
            return (
              <div
                key={log.id}
                className={`rounded-lg p-3 space-y-2 border ${getActionColor(
                  log.action,
                  log.metadata
                )} ${
                  isUnread
                    ? "ring-2 ring-blue-200 dark:ring-blue-800 bg-blue-50/50 dark:bg-blue-950/20"
                    : ""
                } transition-all hover:shadow-md hover:ring-1 hover:ring-gray-300 dark:hover:ring-gray-600 relative`}
              >
                {/* Read/Unread indicator and toggle button */}
                <div className="absolute top-2 right-2 flex items-center gap-1">
                  {isUnread && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleReadStatus(log.id, log.isRead)}
                    className="h-6 w-6 p-0 hover:bg-white/50 dark:hover:bg-black/20"
                    title={isUnread ? "Mark as read" : "Mark as unread"}
                  >
                    {isUnread ? (
                      <Eye className="h-3 w-3" />
                    ) : (
                      <EyeOff className="h-3 w-3" />
                    )}
                  </Button>
                </div>

                <div className="flex items-start gap-3 pr-8">
                  <div className="flex-shrink-0 mt-0.5">
                    {getActionIcon(log.action, log.metadata)}
                  </div>
                  <div className="flex-1 min-w-0 space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="secondary"
                        className={`text-xs border ${getActionColor(
                          log.action,
                          log.metadata
                        )}`}
                      >
                        {log.action}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(log.createdAt)}
                      </span>
                    </div>
                    <p className="text-sm leading-relaxed">{log.description}</p>
                    {log.user && (
                      <div className="flex items-center gap-2 pt-1">
                        <Avatar className="h-5 w-5">
                          <AvatarImage
                            src={log.user.image || undefined}
                            alt={log.user.name}
                          />
                          <AvatarFallback className="text-xs">
                            {getInitials(log.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          {log.user.name}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}
