"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { UserIcon } from "lucide-react";
import { getProductStageColor } from "@/lib/constants/product-stages";

// Helper function to get role colors
const getRoleColor = (roleName: string) => {
  switch (roleName.toLowerCase()) {
    case "super_admin":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
    case "admin":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
    case "manager":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "user":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }
};

interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  className?: string;
}

interface TextFieldProps extends FormFieldProps {
  value: string | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: "text" | "email" | "url";
}

interface TextAreaFieldProps extends FormFieldProps {
  value: string | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
}

interface SelectFieldProps extends FormFieldProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  options: Array<{ value: string; label: string }>;
}

interface User {
  id: string;
  name: string;
  email: string;
  roles: Array<{ name: string }>;
}

interface UserSelectionFieldProps extends FormFieldProps {
  selectedUserIds: string[];
  onChange: (userIds: string[]) => void;
  users: User[];
  isLoading?: boolean;
}

export function TextField({
  label,
  value,
  onChange,
  error,
  required = false,
  placeholder,
  type = "text",
  className,
}: TextFieldProps) {
  return (
    <div className={className}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, "-")}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        id={label.toLowerCase().replace(/\s+/g, "-")}
        type={type}
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={error ? "border-red-500" : ""}
      />
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

export function TextAreaField({
  label,
  value,
  onChange,
  error,
  required = false,
  placeholder,
  rows = 3,
  className,
}: TextAreaFieldProps) {
  return (
    <div className={className}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, "-")}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Textarea
        id={label.toLowerCase().replace(/\s+/g, "-")}
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        rows={rows}
        className={error ? "border-red-500" : ""}
      />
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

export function SelectField({
  label,
  value,
  onChange,
  error,
  required = false,
  placeholder,
  options,
  className,
}: SelectFieldProps) {
  return (
    <div className={className}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, "-")}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className={error ? "border-red-500" : ""}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

// Specialized SelectField for product stages with color dots
export function StageSelectField({
  label,
  value,
  onChange,
  error,
  required = false,
  placeholder,
  options,
  className,
}: SelectFieldProps) {
  return (
    <div className={className}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, "-")}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className={error ? "border-red-500" : ""}>
          <SelectValue placeholder={placeholder}>
            {value && (
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${getProductStageColor(
                    value
                  )}`}
                />
                {options.find((opt) => opt.value === value)?.label}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${getProductStageColor(
                    option.value
                  )}`}
                />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

export function UserSelectionField({
  label,
  selectedUserIds,
  onChange,
  users,
  isLoading = false,
  error,
  className,
}: UserSelectionFieldProps) {
  const handleUserToggle = (userId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedUserIds, userId]);
    } else {
      onChange(selectedUserIds.filter((id) => id !== userId));
    }
  };

  return (
    <div className={className}>
      <Label>{label}</Label>
      <div className="border rounded-lg p-4 max-h-64 overflow-y-auto">
        {isLoading ? (
          <p className="text-sm text-muted-foreground">Loading users...</p>
        ) : users.length === 0 ? (
          <p className="text-sm text-muted-foreground">No users available</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {users.map((user) => (
              <div
                key={user.id}
                className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors"
              >
                <Checkbox
                  id={`user-${user.id}`}
                  checked={selectedUserIds.includes(user.id)}
                  onCheckedChange={(checked) =>
                    handleUserToggle(user.id, checked as boolean)
                  }
                  className="mt-1"
                />
                <Label
                  htmlFor={`user-${user.id}`}
                  className="cursor-pointer flex-1 min-w-0"
                >
                  <div className="flex items-center justify-between gap-2 mb-1">
                    <div className="flex items-center gap-2 min-w-0">
                      <UserIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="font-medium truncate">{user.name}</span>
                    </div>
                    <div className="flex items-center gap-1 flex-shrink-0">
                      {user.roles.map((role) => (
                        <Badge
                          key={role.name}
                          variant="secondary"
                          className={`text-xs px-1.5 py-0.5 ${getRoleColor(
                            role.name
                          )}`}
                        >
                          {role.name.replace("_", " ")}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground truncate">
                    {user.email}
                  </div>
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}
