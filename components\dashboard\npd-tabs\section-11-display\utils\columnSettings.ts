// Column settings utilities for localStorage persistence
import type { SortingState } from "@tanstack/react-table";

export interface ColumnSettings {
  visibility: Record<string, boolean>;
  widths: Record<string, number>;
  sorting: SortingState;
}

const STORAGE_KEY = "section11-column-settings";

// Default sorting state (empty by default)
export const defaultSortingState: SortingState = [];

// Default column visibility (all visible by default)
export const defaultColumnVisibility: Record<string, boolean> = {
  productDetails: true,
  brand: true,
  asin: true,
  url: true,
  imageUrl: true,
  annualSales: true,
  annualRevenue: true,
  marketShare: true,
  salesToReviewRatio: true,
  ratings: true,
  reviewCount: true,
  price: true,
  asinSales: true,
  asinRevenue: true,
  bsr: true,
  fees: true,
  grossMargin: true,
  sellerCountry: true,
  fulfillment: true,
  creationDate: true,
  listingAge: true,
  dimensions: true,
  weight: true,
};

// Default column widths (from competitorColumns.tsx)
export const defaultColumnWidths: Record<string, number> = {
  productDetails: 180,
  brand: 100,
  asin: 95,
  url: 45,
  imageUrl: 56,
  annualSales: 76,
  annualRevenue: 82,
  marketShare: 76,
  salesToReviewRatio: 116,
  ratings: 81,
  reviewCount: 77,
  price: 66,
  asinSales: 70,
  asinRevenue: 76,
  bsr: 61,
  fees: 65,
  grossMargin: 76,
  sellerCountry: 83,
  fulfillment: 100,
  creationDate: 120,
  listingAge: 76,
  dimensions: 135,
  weight: 82,
  actions: 80,
};

// Column display names for the UI
export const columnDisplayNames: Record<string, string> = {
  productDetails: "Product Title",
  brand: "Brand",
  asin: "ASIN",
  url: "Link",
  imageUrl: "Image",
  annualSales: "Annual Sales",
  annualRevenue: "Annual Revenue",
  marketShare: "Market Share",
  salesToReviewRatio: "Sales/Review Ratio",
  ratings: "Ratings",
  reviewCount: "Review Count",
  price: "Price",
  asinSales: "ASIN Sales",
  asinRevenue: "ASIN Revenue",
  bsr: "BSR",
  fees: "Fees",
  grossMargin: "Gross Margin",
  sellerCountry: "Seller Country",
  fulfillment: "Fulfillment",
  creationDate: "Creation Date",
  listingAge: "Listing Age",
  dimensions: "Dimensions",
  weight: "Weight",
  actions: "Actions",
};

// Save column settings to localStorage
export function saveColumnSettings(settings: ColumnSettings): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch {
    // Silently handle localStorage errors
  }
}

// Load column settings from localStorage
export function loadColumnSettings(): ColumnSettings {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored) as ColumnSettings;
      return {
        visibility: { ...defaultColumnVisibility, ...parsed.visibility },
        widths: { ...defaultColumnWidths, ...parsed.widths },
        sorting: parsed.sorting || defaultSortingState,
      };
    }
  } catch (error) {
    console.warn("Failed to load column settings from localStorage:", error);
  }

  return {
    visibility: defaultColumnVisibility,
    widths: defaultColumnWidths,
    sorting: defaultSortingState,
  };
}

// Reset column settings to defaults
export function resetColumnSettings(): ColumnSettings {
  const defaultSettings = {
    visibility: defaultColumnVisibility,
    widths: defaultColumnWidths,
    sorting: defaultSortingState,
  };
  saveColumnSettings(defaultSettings);
  return defaultSettings;
}

// Update only column visibility
export function updateColumnVisibility(
  visibility: Record<string, boolean>
): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    visibility,
  };
  saveColumnSettings(newSettings);
}

// Update only column widths
export function updateColumnWidths(widths: Record<string, number>): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    widths,
  };
  saveColumnSettings(newSettings);
}

// Update sorting state
export function updateSortingState(sorting: SortingState): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    sorting,
  };
  saveColumnSettings(newSettings);
}

// Reset only column visibility to defaults
export function resetColumnVisibility(): Record<string, boolean> {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    visibility: defaultColumnVisibility,
  };
  saveColumnSettings(newSettings);
  return defaultColumnVisibility;
}

// Reset only column widths to defaults
export function resetColumnWidths(): Record<string, number> {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    widths: defaultColumnWidths,
  };
  saveColumnSettings(newSettings);
  return defaultColumnWidths;
}
