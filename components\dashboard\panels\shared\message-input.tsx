"use client";

import React, { useState, useRef, forwardRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import Spinner from "@/components/spinner";

interface User {
  id: string;
  name: string;
  email?: string;
}

interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (mentions?: string[]) => void;
  isSending: boolean;
  placeholder?: string;
  disabled?: boolean;
  users?: User[];
}

interface MessageInputRef {
  focus: () => void;
  blur: () => void;
  setSelectionRange: (start: number) => void;
  selectionStart: number;
  selectionEnd: number;
  value: string;
}

export const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(
  (
    {
      value,
      onChange,
      onSend,
      isSending,
      placeholder = "Type a message...",
      disabled = false,
      users = [],
    },
    ref
  ) => {
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [suggestions, setSuggestions] = useState<User[]>([]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [mentionStart, setMentionStart] = useState(-1);
    const inputRef = useRef<HTMLInputElement>(null);

    // Create the ref object that will be exposed
    const refObject = React.useMemo(
      () => ({
        focus: () => {
          if (inputRef.current) {
            inputRef.current.focus();
            // Move cursor to end
            const range = document.createRange();
            const selection = window.getSelection();
            if (selection && inputRef.current.lastChild) {
              range.setStartAfter(inputRef.current.lastChild);
              range.collapse(true);
              selection.removeAllRanges();
              selection.addRange(range);
            }
          }
        },
        blur: () => {
          if (inputRef.current) {
            inputRef.current.blur();
          }
        },
        // Add compatibility methods for existing code
        setSelectionRange: (start: number) => {
          if (inputRef.current) {
            setCaretPosition(inputRef.current, start);
          }
        },
        selectionStart: 0, // Placeholder for compatibility
        selectionEnd: 0, // Placeholder for compatibility
        value: value, // Current value for compatibility
      }),
      [value]
    );

    // Merge external ref with internal ref
    React.useEffect(() => {
      if (ref) {
        if (typeof ref === "function") {
          ref(refObject);
        } else {
          ref.current = refObject;
        }
      }
    }, [ref, refObject]);

    // Handle contentEditable input with cursor preservation
    const handleContentEditableInput = (e: React.FormEvent<HTMLDivElement>) => {
      const newValue = e.currentTarget.textContent || "";

      // Only update if the value actually changed
      if (newValue !== value) {
        const cursorPosition = getCaretPosition(e.currentTarget);

        onChange(newValue);

        // Update the content with highlighting
        requestAnimationFrame(() => {
          if (inputRef.current) {
            updateContentWithHighlighting(inputRef.current, newValue);
            setCaretPosition(inputRef.current, cursorPosition);
          }
        });

        // Check for @ mention
        const textBeforeCursor = newValue.slice(0, cursorPosition);
        const lastAtIndex = textBeforeCursor.lastIndexOf("@");

        if (lastAtIndex !== -1) {
          const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1);

          if (
            textAfterAt.length >= 0 &&
            !textAfterAt.match(/\s{2,}|[.,!?;:]$/)
          ) {
            const filteredUsers = users.filter((user) =>
              user.name.toLowerCase().includes(textAfterAt.toLowerCase())
            );

            setSuggestions(filteredUsers);
            setShowSuggestions(filteredUsers.length > 0);
            setMentionStart(lastAtIndex);
            setSelectedIndex(0);
          } else {
            setShowSuggestions(false);
          }
        } else {
          setShowSuggestions(false);
        }
      }
    };

    // Get caret position in contentEditable
    const getCaretPosition = (element: HTMLElement): number => {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return 0;

      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(element);
      preCaretRange.setEnd(range.endContainer, range.endOffset);

      return preCaretRange.toString().length;
    };

    // Set caret position in contentEditable
    const setCaretPosition = (element: HTMLElement, position: number) => {
      const range = document.createRange();
      const selection = window.getSelection();

      let currentPos = 0;
      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null
      );

      let node;
      while ((node = walker.nextNode())) {
        const nodeLength = node.textContent?.length || 0;
        if (currentPos + nodeLength >= position) {
          range.setStart(node, position - currentPos);
          range.setEnd(node, position - currentPos);
          break;
        }
        currentPos += nodeLength;
      }

      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    };

    // Escape HTML to prevent XSS
    const escapeHtml = (text: string): string => {
      const div = document.createElement("div");
      div.textContent = text;
      return div.innerHTML;
    };

    // Render content with styled mentions
    const renderStyledContent = useCallback(
      (text: string): string => {
        if (!text) return "";

        const mentions: Array<{
          start: number;
          end: number;
          text: string;
          username: string;
        }> = [];

        // Find all @mentions that match actual usernames
        users.forEach((user) => {
          const mentionPattern = new RegExp(
            `@${user.name.replace(
              /[.*+?^${}()|[\]\\]/g,
              "\\$&"
            )}(?=\\s|$|[.,!?;:])`,
            "g"
          );
          let match;

          while ((match = mentionPattern.exec(text)) !== null) {
            mentions.push({
              start: match.index,
              end: match.index + match[0].length,
              text: match[0],
              username: user.name,
            });
          }
        });

        // Sort mentions by start position and remove overlaps
        mentions.sort((a, b) => a.start - b.start);
        const uniqueMentions: Array<{
          start: number;
          end: number;
          text: string;
          username: string;
        }> = [];
        for (const mention of mentions) {
          if (
            !uniqueMentions.some(
              (m) =>
                (mention.start >= m.start && mention.start < m.end) ||
                (mention.end > m.start && mention.end <= m.end)
            )
          ) {
            uniqueMentions.push(mention);
          }
        }

        // Build HTML string with styled mentions
        let html = "";
        let lastIndex = 0;

        for (const mention of uniqueMentions) {
          // Add text before mention
          if (mention.start > lastIndex) {
            html += escapeHtml(text.slice(lastIndex, mention.start));
          }

          // Add styled mention
          html += `<span class="bg-blue-100 text-blue-700 px-1 py-0.5 rounded font-medium">${escapeHtml(
            mention.text
          )}</span>`;

          lastIndex = mention.end;
        }

        // Add remaining text
        if (lastIndex < text.length) {
          html += escapeHtml(text.slice(lastIndex));
        }

        return html || escapeHtml(text);
      },
      [users]
    );

    // Update content with highlighting without losing cursor
    const updateContentWithHighlighting = useCallback(
      (element: HTMLElement, text: string) => {
        const styledContent = renderStyledContent(text);
        if (element.innerHTML !== styledContent) {
          element.innerHTML = styledContent;
        }
      },
      [renderStyledContent]
    );

    // Initialize content when value changes externally
    React.useEffect(() => {
      if (inputRef.current && inputRef.current.textContent !== value) {
        updateContentWithHighlighting(inputRef.current, value);
      }
    }, [value, updateContentWithHighlighting]);

    // Extract mentions from message
    const extractMentions = (text: string): string[] => {
      const mentions: string[] = [];

      // Find all @mentions that match actual usernames
      users.forEach((user) => {
        const mentionPattern = new RegExp(
          `@${user.name.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(?=\\s|$)`,
          "g"
        );
        if (mentionPattern.test(text)) {
          mentions.push(user.name);
        }
      });

      return mentions;
    };

    // Handle key down
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (showSuggestions) {
        if (e.key === "ArrowDown") {
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
        } else if (e.key === "ArrowUp") {
          e.preventDefault();
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
        } else if (e.key === "Enter" || e.key === "Tab") {
          e.preventDefault();
          selectSuggestion(suggestions[selectedIndex]);
        } else if (e.key === "Escape") {
          setShowSuggestions(false);
        }
      } else if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    };

    // Select suggestion
    const selectSuggestion = (user: User) => {
      if (mentionStart !== -1 && inputRef.current) {
        const currentPos = getCaretPosition(inputRef.current);
        const beforeMention = value.slice(0, mentionStart);
        const afterMention = value.slice(currentPos);

        // Ensure there's a space after the mention
        const newValue = `${beforeMention}@${user.name} ${afterMention}`;

        onChange(newValue);
        setShowSuggestions(false);

        // Focus back to input and position cursor after the mention and space
        setTimeout(() => {
          if (inputRef.current) {
            const newCursorPos = mentionStart + user.name.length + 2; // +1 for @, +1 for space
            updateContentWithHighlighting(inputRef.current, newValue);
            inputRef.current.focus();
            setCaretPosition(inputRef.current, newCursorPos);
          }
        }, 0);
      }
    };

    // Handle send
    const handleSend = () => {
      if (value.trim()) {
        const mentions = extractMentions(value);
        onSend(mentions);
      }
    };

    const canSend = value.trim() && !isSending && !disabled;

    return (
      <div className="flex-shrink-0 border-t p-4 bg-background">
        <div className="relative">
          {/* Suggestions dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto z-50">
              {suggestions.map((user, index) => (
                <button
                  key={user.id}
                  className={`w-full text-left px-3 py-2 hover:bg-gray-100 ${
                    index === selectedIndex ? "bg-blue-50 text-blue-600" : ""
                  }`}
                  onClick={() => selectSuggestion(user)}
                >
                  <div className="font-medium">@{user.name}</div>
                  {user.email && (
                    <div className="text-sm text-gray-500">{user.email}</div>
                  )}
                </button>
              ))}
            </div>
          )}

          <div className="flex gap-2">
            <div className="relative flex-1">
              <div
                ref={inputRef}
                contentEditable
                suppressContentEditableWarning
                onInput={handleContentEditableInput}
                onKeyDown={handleKeyDown}
                className="flex-1 min-h-[40px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md"
                style={{
                  whiteSpace: "pre-wrap",
                  wordBreak: "break-word",
                  outline: "none",
                }}
                data-placeholder={placeholder}
              />
              {/* Placeholder when empty */}
              {!value && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm pointer-events-none">
                  {placeholder}
                </div>
              )}
            </div>
            <Button
              onClick={() => onSend(extractMentions(value))}
              disabled={!canSend}
              size="sm"
              className="px-3"
            >
              {isSending ? (
                <Spinner loading={true} size={8} />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

MessageInput.displayName = "MessageInput";
