"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { APP_NAME } from "@/lib/constants";
import {
  Home,
  Package,
  Settings,
  UserRound,
  Tickets,
  Shield,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import Link from "next/link";
import Image from "next/image";
import { useNavigationGuard } from "@/context/navigation-guard-context";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export function AppSidebar() {
  const { state } = useSidebar();
  const { user } = useAuth();
  const { navigateWithConfirmation } = useNavigationGuard();
  const pathname = usePathname();

  // Check if user has admin access
  const hasAdminAccess =
    user?.roles?.includes("ADMIN") || user?.roles?.includes("SUPER_ADMIN");

  // Helper function to check if a route is active
  const isRouteActive = (route: string) => {
    if (route === "/dashboard") {
      // Dashboard is active only on exact match
      return pathname === "/dashboard";
    }
    // For other routes, check if pathname starts with the route
    return pathname.startsWith(route);
  };

  // Custom navigation handler
  const handleNavigation = (url: string) => (e: React.MouseEvent) => {
    e.preventDefault();
    navigateWithConfirmation(url);
  };

  return (
    <Sidebar variant="floating" collapsible="icon">
      <SidebarHeader>
        <div className="flex-center items-center py-4">
          <Link href="/" className="flex-start">
            <Image
              src="/images/logo.svg"
              alt={`${APP_NAME} Logo`}
              height={0}
              width={0}
              priority={true}
              style={{ width: "auto", height: "32px" }}
            />
            {state === "expanded" && (
              <span className="font-bold text-xl ml-2 truncate overflow-hidden whitespace-nowrap">
                {APP_NAME}
              </span>
            )}
          </Link>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu className="px-2 space-y-2">
          {/* Dashboard Link */}
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className={cn(
                "pl-2 text-base transition-all duration-200",
                isRouteActive("/dashboard") && [
                  "bg-primary/10 text-primary border-r-2 border-primary",
                  "hover:bg-primary/15",
                  "data-[state=open]:bg-primary/15",
                ]
              )}
            >
              <Link href="/dashboard" onClick={handleNavigation("/dashboard")}>
                <Home
                  style={{ width: "20px", height: "20px" }}
                  className={cn(
                    "transition-colors duration-200",
                    isRouteActive("/dashboard") && "text-primary"
                  )}
                />
                <span
                  className={cn(
                    "pl-1 transition-colors duration-200",
                    isRouteActive("/dashboard") && "text-primary font-medium"
                  )}
                >
                  Dashboard
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* NPD Link */}
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className={cn(
                "pl-2 text-base transition-all duration-200",
                isRouteActive("/dashboard/npd") && [
                  "bg-primary/10 text-primary border-r-2 border-primary",
                  "hover:bg-primary/15",
                  "data-[state=open]:bg-primary/15",
                ]
              )}
            >
              <Link
                href="/dashboard/npd"
                onClick={handleNavigation("/dashboard/npd")}
              >
                <Package
                  style={{ width: "20px", height: "20px" }}
                  className={cn(
                    "transition-colors duration-200",
                    isRouteActive("/dashboard/npd") && "text-primary"
                  )}
                />
                <span
                  className={cn(
                    "pl-1 transition-colors duration-200",
                    isRouteActive("/dashboard/npd") &&
                      "text-primary font-medium"
                  )}
                >
                  NPD
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* Tickets Link */}
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className={cn(
                "pl-2 text-base transition-all duration-200",
                isRouteActive("/dashboard/tickets") && [
                  "bg-primary/10 text-primary border-r-2 border-primary",
                  "hover:bg-primary/15",
                  "data-[state=open]:bg-primary/15",
                ]
              )}
            >
              <Link
                href="/dashboard/tickets"
                onClick={handleNavigation("/dashboard/tickets")}
              >
                <Tickets
                  style={{ width: "20px", height: "20px" }}
                  className={cn(
                    "transition-colors duration-200",
                    isRouteActive("/dashboard/tickets") && "text-primary"
                  )}
                />
                <span
                  className={cn(
                    "pl-1 transition-colors duration-200",
                    isRouteActive("/dashboard/tickets") &&
                      "text-primary font-medium"
                  )}
                >
                  Tickets
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          {/* Admin Panel Link */}
          {hasAdminAccess && (
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                className={cn(
                  "pl-2 text-base transition-all duration-200",
                  isRouteActive("/dashboard/admin") && [
                    "bg-primary/10 text-primary border-r-2 border-primary",
                    "hover:bg-primary/15",
                    "data-[state=open]:bg-primary/15",
                  ]
                )}
              >
                <Link
                  href="/dashboard/admin"
                  onClick={handleNavigation("/dashboard/admin")}
                >
                  <Shield
                    style={{ width: "20px", height: "20px" }}
                    className={cn(
                      "transition-colors duration-200",
                      isRouteActive("/dashboard/admin") && "text-primary"
                    )}
                  />
                  <span
                    className={cn(
                      "pl-1 transition-colors duration-200",
                      isRouteActive("/dashboard/admin") &&
                        "text-primary font-medium"
                    )}
                  >
                    Admin Panel
                  </span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Settings Link */}
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className={cn(
                "pl-2 text-base transition-all duration-200",
                isRouteActive("/dashboard/settings") && [
                  "bg-primary/10 text-primary border-r-2 border-primary",
                  "hover:bg-primary/15",
                  "data-[state=open]:bg-primary/15",
                ]
              )}
            >
              <Link
                href="/dashboard/settings"
                onClick={handleNavigation("/dashboard/settings")}
              >
                <Settings
                  style={{ width: "20px", height: "20px" }}
                  className={cn(
                    "transition-colors duration-200",
                    isRouteActive("/dashboard/settings") && "text-primary"
                  )}
                />
                <span
                  className={cn(
                    "pl-1 transition-colors duration-200",
                    isRouteActive("/dashboard/settings") &&
                      "text-primary font-medium"
                  )}
                >
                  Settings
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* Profile Link */}
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className={cn(
                "pl-2 text-base transition-all duration-200",
                isRouteActive("/dashboard/profile") && [
                  "bg-primary/10 text-primary border-r-2 border-primary",
                  "hover:bg-primary/15",
                  "data-[state=open]:bg-primary/15",
                ]
              )}
            >
              <Link
                href="/dashboard/profile"
                onClick={handleNavigation("/dashboard/profile")}
              >
                <UserRound
                  style={{ width: "20px", height: "20px" }}
                  className={cn(
                    "transition-colors duration-200",
                    isRouteActive("/dashboard/profile") && "text-primary"
                  )}
                />
                <span
                  className={cn(
                    "pl-1 transition-colors duration-200",
                    isRouteActive("/dashboard/profile") &&
                      "text-primary font-medium"
                  )}
                >
                  Profile
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
