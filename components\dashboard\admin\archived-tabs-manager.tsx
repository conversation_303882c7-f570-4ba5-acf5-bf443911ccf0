"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  fetchArchivedTabs,
  getArchivedTabStats,
  searchArchivedTabs,
  bulkRestoreTabs,
  bulkPermanentlyDeleteTabs,
} from "@/actions/admin-tabs.actions";
import { restoreTab } from "@/actions/tab-archiving.actions";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Archive,
  RotateCcw,
  Trash2,
  Search,
  FileText,
  Calendar,
  User,
  Package,
} from "lucide-react";
import Spinner from "@/components/spinner";

interface ArchivedTab {
  id: string;
  tabName: string;
  order: number;
  archivedAt: Date | null;
  npdProduct: {
    id: string;
    name: string;
    slug: string;
    brand: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
  archivedByUser: {
    id: string;
    name: string;
    email: string;
  } | null;
  _count: {
    history: number;
    activityLogs: number;
  };
}

interface ArchivedTabStats {
  totalArchived: number;
  archivedThisMonth: number;
  archivedByUser: Array<{
    userId: string | null;
    userName: string;
    userEmail: string;
    count: number;
  }>;
  archivedByProduct: Array<{
    npdProductId: string;
    productName: string;
    productBrand: string;
    count: number;
  }>;
}

export function ArchivedTabsManager() {
  const [tabs, setTabs] = useState<ArchivedTab[]>([]);
  const [stats, setStats] = useState<ArchivedTabStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTabs, setSelectedTabs] = useState<string[]>([]);
  const [actionLoading, setActionLoading] = useState(false);

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [tabsData, statsData] = await Promise.all([
        fetchArchivedTabs(),
        getArchivedTabStats(),
      ]);
      setTabs(tabsData);
      setStats(statsData);
    } catch (error) {
      console.error("Error loading archived tabs:", error);
      toast.error("Failed to load archived tabs");
    } finally {
      setLoading(false);
    }
  };

  // Search tabs
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadData();
      return;
    }

    try {
      setLoading(true);
      const searchResults = await searchArchivedTabs(searchQuery);
      setTabs(searchResults);
    } catch (error) {
      console.error("Error searching tabs:", error);
      toast.error("Failed to search tabs");
    } finally {
      setLoading(false);
    }
  };

  // Restore single tab
  const handleRestoreTab = async (tabId: string, tabName: string) => {
    try {
      setActionLoading(true);
      const result = await restoreTab(tabId);

      if (result.success) {
        toast.success(`Tab "${tabName}" restored successfully`);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error restoring tab:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to restore tab"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk restore tabs
  const handleBulkRestore = async () => {
    if (selectedTabs.length === 0) {
      toast.error("Please select tabs to restore");
      return;
    }

    try {
      setActionLoading(true);
      const result = await bulkRestoreTabs(selectedTabs);

      if (result.success) {
        toast.success(result.message);
        setSelectedTabs([]);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error bulk restoring tabs:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to restore tabs"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk permanently delete tabs
  const handleBulkDelete = async () => {
    if (selectedTabs.length === 0) {
      toast.error("Please select tabs to delete");
      return;
    }

    try {
      setActionLoading(true);
      const result = await bulkPermanentlyDeleteTabs(selectedTabs);

      if (result.success) {
        toast.success(result.message);
        setSelectedTabs([]);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error bulk deleting tabs:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete tabs"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Toggle tab selection
  const toggleTabSelection = (tabId: string) => {
    setSelectedTabs((prev) =>
      prev.includes(tabId)
        ? prev.filter((id) => id !== tabId)
        : [...prev, tabId]
    );
  };

  // Select all tabs
  const toggleSelectAll = () => {
    setSelectedTabs(
      selectedTabs.length === tabs.length ? [] : tabs.map((t) => t.id)
    );
  };

  if (loading && !tabs.length) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner loading={true} size={12} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Archived
              </CardTitle>
              <Archive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalArchived}</div>
              <p className="text-xs text-muted-foreground">Tabs in archive</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.archivedThisMonth}
              </div>
              <p className="text-xs text-muted-foreground">
                Archived this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Top Archiver
              </CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.archivedByUser[0]?.count || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.archivedByUser[0]?.userName || "No data"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Product</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.archivedByProduct[0]?.count || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.archivedByProduct[0]?.productName || "No data"}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Archived Tabs Management</CardTitle>
          <CardDescription>
            Restore or permanently delete archived tabs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by tab name, product name, or brand..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} variant="outline">
              Search
            </Button>
            <Button onClick={loadData} variant="outline">
              Reset
            </Button>
          </div>

          {/* Bulk Actions */}
          {selectedTabs.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedTabs.length} selected
              </span>
              <Button
                size="sm"
                onClick={handleBulkRestore}
                disabled={actionLoading}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Restore Selected
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="destructive"
                    disabled={actionLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete Selected
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Permanently Delete Tabs?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete {selectedTabs.length} tabs
                      and all their data. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBulkDelete}
                      className="bg-destructive text-white hover:bg-destructive/90"
                    >
                      Delete Permanently
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Archived Tabs ({tabs.length})</CardTitle>
              <CardDescription>
                Tabs that have been archived and can be restored
              </CardDescription>
            </div>
            {tabs.length > 0 && (
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedTabs.length === tabs.length}
                  onCheckedChange={toggleSelectAll}
                />
                <span className="text-sm text-muted-foreground">
                  Select All
                </span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {tabs.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Archived Tabs</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? "No tabs match your search."
                  : "No tabs have been archived yet."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className="flex items-center gap-4 p-4 border rounded-lg"
                >
                  <Checkbox
                    checked={selectedTabs.includes(tab.id)}
                    onCheckedChange={() => toggleTabSelection(tab.id)}
                  />

                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{tab.tabName}</h4>
                      <Badge variant="outline">Order: {tab.order}</Badge>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{tab.npdProduct.brand}</Badge>
                      <span className="text-sm text-muted-foreground">
                        {tab.npdProduct.name}
                      </span>
                    </div>

                    <div className="text-sm text-muted-foreground space-y-1">
                      <div className="flex items-center gap-4">
                        <span>
                          Archived:{" "}
                          {tab.archivedAt
                            ? new Date(tab.archivedAt).toLocaleDateString()
                            : "Unknown"}
                        </span>
                        {tab.archivedByUser && (
                          <span>By: {tab.archivedByUser.name}</span>
                        )}
                        {tab.user && <span>Creator: {tab.user.name}</span>}
                      </div>
                      <div className="flex items-center gap-4">
                        <span>{tab._count.history} history entries</span>
                        <span>{tab._count.activityLogs} activities</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleRestoreTab(tab.id, tab.tabName)}
                      disabled={actionLoading}
                    >
                      <RotateCcw className="h-4 w-4 mr-1" />
                      Restore
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
