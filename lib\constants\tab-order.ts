// Predefined order for product tabs
export const TAB_ORDER_MAP = {
  Overview: 1,
  "1.1": 2,
  "1.2": 3,
  "1.3": 4,
  "Sourcing Brief": 5,
} as const;

// Type derived from the actual map - single source of truth!
export type TabName = keyof typeof TAB_ORDER_MAP;

// Helper function to get order for a tab
export function getTabOrder(tabName: string): number {
  return TAB_ORDER_MAP[tabName as TabName] || 99; // Default to end if not found
}

// Helper function to validate if a tab name is predefined
export function isValidTabName(tabName: string): tabName is TabName {
  return tabName in TAB_ORDER_MAP;
}

// Get all available tab names sorted by order
export function getAvailableTabNames(): TabName[] {
  return Object.keys(TAB_ORDER_MAP) as TabName[];
}

// Get suggested order for custom tab names
export function getSuggestedOrder(
  existingTabs: { tabName: string; order: number }[]
): number {
  const maxOrder = Math.max(...existingTabs.map((tab) => tab.order), 0);
  return maxOrder + 1;
}
