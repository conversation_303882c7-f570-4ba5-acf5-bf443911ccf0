import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit3, ChevronDown, ChevronRight, History, Info } from "lucide-react";
import { useState, useEffect, useRef } from "react";

interface ProductInfoVersion {
  id: string;
  features: string;
  color: string;
  size: string;
  pricerange: string;
  user: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface ProductInfo {
  features: string;
  color: string;
  size: string;
  pricerange: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: ProductInfoVersion[];
}

interface ProductInfoCardProps {
  data: ProductInfo;
  isEditing?: boolean;
  onValuesChange?: (values: ProductInfo) => void;
  onDirtyChange?: (isDirty: boolean) => void;
  modifiedFields?: Set<string>;
  onFieldChange?: (
    field: string,
    value: string,
    originalData: ProductInfo
  ) => void;
}

export function ProductInfoCard({
  data,
  isEditing = false,
  onValuesChange,
  onDirtyChange,
  modifiedFields = new Set(),
  onFieldChange,
}: ProductInfoCardProps) {
  // Local state for form values
  const [formValues, setFormValues] = useState<ProductInfo>(data);
  const [isUserEditing, setIsUserEditing] = useState(false);
  const [expandedVersions, setExpandedVersions] = useState(false);
  const featuresTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Reset form values when data changes, but only if user is not currently editing
  useEffect(() => {
    if (!isUserEditing) {
      setFormValues(data);
    }
  }, [data, isUserEditing]);

  // Auto-resize textarea when entering edit mode or when content changes
  useEffect(() => {
    if (isEditing && featuresTextareaRef.current) {
      const textarea = featuresTextareaRef.current;
      textarea.style.height = "auto";
      textarea.style.height = Math.max(80, textarea.scrollHeight) + "px";
    }
  }, [isEditing, formValues.features]);

  // Reset user editing state when switching out of edit mode
  useEffect(() => {
    if (!isEditing) {
      setIsUserEditing(false);
      setFormValues(data); // Reset to original data when exiting edit mode
    }
  }, [isEditing, data]);

  // Check if form has been modified
  const isDirty = JSON.stringify(formValues) !== JSON.stringify(data);

  // Notify parent about dirty state changes
  useEffect(() => {
    onDirtyChange?.(isDirty);
  }, [isDirty, onDirtyChange]);

  // Handle input changes
  const handleChange = (field: keyof ProductInfo, value: string) => {
    setIsUserEditing(true);
    const newValues = { ...formValues, [field]: value };
    setFormValues(newValues);
    onValuesChange?.(newValues);
    onFieldChange?.(field, value, data);
  };

  // Helper function to get styling for modified fields
  const getFieldStyling = (fieldName: string) => {
    const isModified = modifiedFields.has(fieldName);
    const baseClass = isEditing
      ? "border-blue-300 focus:border-blue-500 bg-blue-50/50"
      : "";

    if (isModified && isEditing) {
      return `${baseClass} ring-2 ring-orange-200 border-orange-400 bg-orange-50/50`;
    }

    return baseClass;
  };

  const toggleVersions = () => {
    setExpandedVersions(!expandedVersions);
  };

  return (
    <Card className="flex-shrink-0">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Version toggle icon on the left */}
            {data.versions && data.versions.length > 0 && (
              <Button
                size="sm"
                variant="ghost"
                onClick={toggleVersions}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
              >
                {expandedVersions ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            )}
            <Info className="h-5 w-5 text-blue-600" />
            Product Information
            {data.isEdited && (
              <Badge variant="secondary" className="text-xs">
                edited
              </Badge>
            )}
            {isEditing && (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-normal">
                Editing
              </span>
            )}
          </div>
          {data.user && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>by @{data.user}</span>
              {data.editedAt && (
                <>
                  <span>•</span>
                  <span>
                    {data.isEdited ? "edited" : "created"}{" "}
                    {new Date(data.editedAt).toLocaleDateString()}
                  </span>
                </>
              )}
            </div>
          )}
        </CardTitle>
        <CardDescription>
          Basic product characteristics for competitor analysis
          {isEditing && " • Click in any field to edit"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Features - Full width row */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              Features
              {isEditing && <Edit3 className="h-3 w-3 text-blue-500" />}
            </label>
            {isEditing ? (
              <Textarea
                ref={featuresTextareaRef}
                value={formValues.features}
                onChange={(e) => handleChange("features", e.target.value)}
                placeholder="Enter product features..."
                className={`min-h-[80px] resize-y ${getFieldStyling(
                  "features"
                )}`}
                style={{
                  height: "auto",
                  minHeight: "80px",
                }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = "auto";
                  target.style.height =
                    Math.max(80, target.scrollHeight) + "px";
                }}
              />
            ) : (
              <div className="text-sm bg-muted/50 p-3 rounded border">
                <div className="whitespace-pre-wrap">
                  {data.features || "No features specified"}
                </div>
              </div>
            )}
          </div>

          {/* Color, Size, Price Range - Three columns in second row */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                Color
                {isEditing && <Edit3 className="h-3 w-3 text-blue-500" />}
              </label>
              {isEditing ? (
                <Input
                  value={formValues.color}
                  onChange={(e) => handleChange("color", e.target.value)}
                  placeholder="Enter color..."
                  className={getFieldStyling("color")}
                />
              ) : (
                <div className="text-sm bg-muted/50 p-3 rounded border">
                  {data.color || "No color specified"}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                Size
                {isEditing && <Edit3 className="h-3 w-3 text-blue-500" />}
              </label>
              {isEditing ? (
                <Input
                  value={formValues.size}
                  onChange={(e) => handleChange("size", e.target.value)}
                  placeholder="Enter size..."
                  className={getFieldStyling("size")}
                />
              ) : (
                <div className="text-sm bg-muted/50 p-3 rounded border">
                  {data.size || "No size specified"}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                Price Range
                {isEditing && <Edit3 className="h-3 w-3 text-blue-500" />}
              </label>
              {isEditing ? (
                <Input
                  value={formValues.pricerange}
                  onChange={(e) => handleChange("pricerange", e.target.value)}
                  placeholder="Enter price range..."
                  className={getFieldStyling("pricerange")}
                />
              ) : (
                <div className="text-sm bg-muted/50 p-3 rounded border">
                  {data.pricerange || "No price range specified"}
                </div>
              )}
            </div>
          </div>

          {/* Previous versions */}
          {data.versions && data.versions.length > 0 && expandedVersions && (
            <div className="mt-6 pt-6 border-t border-dashed">
              <div className="flex items-center gap-2 mb-4">
                <History className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground font-medium">
                  Previous Versions
                </span>
              </div>
              <div className="space-y-4">
                {[...data.versions].reverse().map((version) => (
                  <div
                    key={version.id}
                    className="p-4 bg-muted/30 rounded-lg border"
                  >
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-sm font-medium text-muted-foreground">
                        by @{version.user}
                      </span>
                      {version.editedAt && (
                        <>
                          <span className="text-muted-foreground">•</span>
                          <span className="text-sm text-muted-foreground">
                            {new Date(version.editedAt).toLocaleDateString()}
                          </span>
                        </>
                      )}
                      {version.isEdited && (
                        <Badge variant="outline" className="text-xs">
                          edited
                        </Badge>
                      )}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-muted-foreground">
                          Features:
                        </span>
                        <div className="mt-1 p-2 bg-background rounded border text-xs">
                          {version.features || "No features specified"}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">
                          Color:
                        </span>
                        <div className="mt-1 p-2 bg-background rounded border text-xs">
                          {version.color || "No color specified"}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">
                          Size:
                        </span>
                        <div className="mt-1 p-2 bg-background rounded border text-xs">
                          {version.size || "No size specified"}
                        </div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <span className="font-medium text-muted-foreground">
                        Price Range:
                      </span>
                      <div className="mt-1 p-2 bg-background rounded border text-xs">
                        {version.pricerange || "No price range specified"}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
