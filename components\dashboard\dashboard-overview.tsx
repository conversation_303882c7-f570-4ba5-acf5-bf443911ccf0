"use client";

import { useNPDContext } from "@/context/npd-context";
import Spinner from "@/components/spinner";
import type { ProductSummary } from "@/types";

export function DashboardOverview() {
  const { allProducts, isLoadingProducts, error } = useNPDContext();

  if (isLoadingProducts) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center">
        <Spinner loading={true} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-red-500">Error loading NPDs: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {allProducts.length === 0 ? (
        <p className="text-gray-500">No products found.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {allProducts.map((product: ProductSummary) => (
            <div
              key={product.id}
              className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
            >
              <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
              <p className="text-sm text-gray-600 mb-1">
                <span className="font-medium">Brand:</span> {product.brand}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
