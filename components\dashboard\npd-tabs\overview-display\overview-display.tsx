"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  forwardRef,
} from "react";
import { useSearchParams } from "next/navigation";
import { updateTabData } from "@/actions/npd.actions";
import { updateTabImages } from "@/actions/cloudinary-images.actions";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import { TabDangerZone } from "../shared/tab-danger-zone";
// Card level changes functionality available if needed

import { KeyActionsCard } from "./components/key-actions-card";
import { DependenciesCard } from "./components/dependencies-card";
import { NextStepsCard } from "./components/next-steps-card";
import { ImagesCard } from "./components/images-card";

interface OverviewData {
  keyActions?: Array<{
    id: string;
    title: string;
    description: string;
    date: string;
    user?: string;
    editedAt?: string;
    editedBy?: string;
    isEdited?: boolean;
    versions?: Array<{
      id: string;
      title: string;
      description: string;
      date: string;
      user?: string;
      editedAt?: string;
      editedBy?: string;
      isEdited?: boolean;
    }>;
  }>;
  dependencies?: Array<{
    id: string;
    title: string;
    type: "internal" | "external";
    status: "resolved" | "pending" | "blocked";
    description?: string;
    dueDate?: string;
    user?: string;
    editedAt?: string;
    editedBy?: string;
    isEdited?: boolean;
    versions?: Array<{
      id: string;
      title: string;
      type: "internal" | "external";
      status: "resolved" | "pending" | "blocked";
      description?: string;
      dueDate?: string;
      user?: string;
      editedAt?: string;
      editedBy?: string;
      isEdited?: boolean;
    }>;
  }>;
  nextSteps?: Array<{
    id: string;
    title: string;
    assignees?: string[];
    dueDate?: string;
    description?: string;
    status: "not-started" | "in-progress" | "completed";
    user?: string;
    editedAt?: string;
    editedBy?: string;
    isEdited?: boolean;
    versions?: Array<{
      id: string;
      title: string;
      assignees?: string[];
      dueDate?: string;
      description?: string;
      status: "not-started" | "in-progress" | "completed";
      user?: string;
      editedAt?: string;
      editedBy?: string;
      isEdited?: boolean;
    }>;
  }>;
  images?: Array<{
    id: string;
    url: string;
    title?: string;
    description?: string;
    uploadedAt: string;
    uploadedBy: string;
  }>;
  // For delayed image uploads
  newImageFiles?: File[];
  deletedImages?: string[];
}

interface OverviewDisplayProps {
  tabId: string;
  initialData: OverviewData;
  isEditing: boolean;
  onDirtyChange: (isDirty: boolean) => void;
  onUserMention?: (username: string) => void;
  productId?: string;
  productName?: string;
  productSlug?: string;
  // Props for TabDangerZone
  tabName?: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
}

export interface OverviewDisplayRef {
  save: () => Promise<void>;
}

export const OverviewDisplay = forwardRef<
  OverviewDisplayRef,
  OverviewDisplayProps
>(
  (
    {
      tabId,
      initialData,
      isEditing,
      onDirtyChange,
      onUserMention,
      productId,
      productName,
      productSlug,
      tabName,
      tabCreatorId,
      productOwnerId,
    },
    ref
  ) => {
    const [data, setData] = useState<OverviewData>(initialData);
    const [originalData, setOriginalData] = useState<OverviewData>(initialData);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const { user } = useAuth();
    const searchParams = useSearchParams();

    // State for image handling (similar to sourcing brief)
    const [previewUrls, setPreviewUrls] = useState<string[]>(
      (initialData.images || []).map((img) =>
        typeof img === "string" ? img : img.url
      )
    );

    // Update data when initialData changes
    useEffect(() => {
      setData(initialData);
      setOriginalData(initialData);
      setHasUnsavedChanges(false);
      // Update preview URLs from saved images only (filter out blob URLs)
      const savedImages = (initialData.images || [])
        .map((img) => (typeof img === "string" ? img : img.url))
        .filter((url) => url && !url.startsWith("blob:"));
      setPreviewUrls(savedImages);
    }, [initialData]);

    // Notify parent of changes
    useEffect(() => {
      onDirtyChange(hasUnsavedChanges);
    }, [hasUnsavedChanges, onDirtyChange]);

    // Handle highlighting from URL parameter (similar to chat panel)
    useEffect(() => {
      const highlightParam = searchParams.get("highlight");
      if (
        highlightParam &&
        data &&
        ((data.nextSteps && data.nextSteps.length > 0) ||
          (data.keyActions && data.keyActions.length > 0) ||
          (data.dependencies && data.dependencies.length > 0))
      ) {
        // Check if we've already highlighted this item in this session
        const highlightKey = `highlighted-${highlightParam}`;
        const alreadyHighlighted = sessionStorage.getItem(highlightKey);

        if (!alreadyHighlighted) {
          // Mark as highlighted for this session
          sessionStorage.setItem(highlightKey, "true");

          // Wait for components to render, then scroll to and highlight the specific entry
          setTimeout(() => {
            const entryElement = document.getElementById(
              `entry-${highlightParam}`
            );
            if (entryElement) {
              entryElement.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });

              // Add a temporary highlight effect (simpler like chat panel)
              entryElement.classList.add(
                "bg-yellow-100",
                "dark:bg-yellow-900/30",
                "transition-colors",
                "duration-300"
              );

              // Remove highlight after 3 seconds (same as chat panel)
              setTimeout(() => {
                entryElement.classList.remove(
                  "bg-yellow-100",
                  "dark:bg-yellow-900/30"
                );
              }, 3000);

              // Clean up URL parameter after highlighting
              const url = new URL(window.location.href);
              url.searchParams.delete("highlight");
              window.history.replaceState({}, "", url.pathname + url.search);
            }
          }, 1000); // Wait longer for overview components to fully render
        }
      }
    }, [searchParams, data]);

    // Check for changes
    const checkForChanges = useCallback(
      (newData: OverviewData) => {
        const hasChanges =
          JSON.stringify(newData) !== JSON.stringify(originalData);
        setHasUnsavedChanges(hasChanges);
        return hasChanges;
      },
      [originalData]
    );

    // Update data and check for changes
    const updateData = useCallback(
      (updates: Partial<OverviewData>) => {
        const newData = { ...data, ...updates };
        setData(newData);
        checkForChanges(newData);
      },
      [data, checkForChanges]
    );

    // Process data for versioning before save
    const processDataForSave = useCallback(
      (currentData: OverviewData, originalData: OverviewData) => {
        if (!user) return currentData;

        const processedData = { ...currentData };

        // Process Key Actions
        if (processedData.keyActions && originalData.keyActions) {
          processedData.keyActions = processedData.keyActions.map((action) => {
            const originalAction = originalData.keyActions?.find(
              (orig) => orig.id === action.id
            );

            if (originalAction) {
              // This is an existing action being modified
              const isOwnEntry = originalAction.user === user.name;
              const hasChanges =
                JSON.stringify(action) !== JSON.stringify(originalAction);

              if (hasChanges) {
                if (isOwnEntry) {
                  // User editing their own entry - mark as edited
                  return {
                    ...action,
                    isEdited: true,
                    editedAt: new Date().toISOString(),
                    editedBy: user.name,
                  };
                } else {
                  // User editing someone else's entry - create version
                  const currentVersion = {
                    id: `${originalAction.id}_v${Date.now()}`,
                    title: originalAction.title,
                    description: originalAction.description,
                    date: originalAction.date,
                    user: originalAction.user,
                    editedAt: originalAction.editedAt,
                    editedBy: originalAction.editedBy,
                    isEdited: originalAction.isEdited,
                  };

                  return {
                    ...action,
                    user: user.name,
                    editedAt: new Date().toISOString(),
                    editedBy: user.name,
                    isEdited: true,
                    versions: [
                      ...(originalAction.versions || []),
                      currentVersion,
                    ],
                  };
                }
              }
            }

            return action; // New action or no changes
          });
        }

        // Process Next Steps (similar logic)
        if (processedData.nextSteps && originalData.nextSteps) {
          processedData.nextSteps = processedData.nextSteps.map((step) => {
            const originalStep = originalData.nextSteps?.find(
              (orig) => orig.id === step.id
            );

            if (originalStep) {
              const isOwnEntry = originalStep.user === user.name;
              const hasChanges =
                JSON.stringify(step) !== JSON.stringify(originalStep);

              if (hasChanges) {
                if (isOwnEntry) {
                  return {
                    ...step,
                    isEdited: true,
                    editedAt: new Date().toISOString(),
                    editedBy: user.name,
                  };
                } else {
                  const currentVersion = {
                    id: `${originalStep.id}_v${Date.now()}`,
                    title: originalStep.title,
                    assignees: originalStep.assignees,
                    dueDate: originalStep.dueDate,
                    description: originalStep.description,
                    status: originalStep.status,
                    user: originalStep.user,
                    editedAt: originalStep.editedAt,
                    editedBy: originalStep.editedBy,
                    isEdited: originalStep.isEdited,
                  };

                  return {
                    ...step,
                    user: user.name,
                    editedAt: new Date().toISOString(),
                    editedBy: user.name,
                    isEdited: true,
                    versions: [
                      ...(originalStep.versions || []),
                      currentVersion,
                    ],
                  };
                }
              }
            }

            return step;
          });
        }

        // Process Dependencies (similar logic)
        if (processedData.dependencies && originalData.dependencies) {
          processedData.dependencies = processedData.dependencies.map(
            (dependency) => {
              const originalDependency = originalData.dependencies?.find(
                (orig) => orig.id === dependency.id
              );

              if (originalDependency) {
                const isOwnEntry = originalDependency.user === user.name;
                const hasChanges =
                  JSON.stringify(dependency) !==
                  JSON.stringify(originalDependency);

                if (hasChanges) {
                  if (isOwnEntry) {
                    return {
                      ...dependency,
                      isEdited: true,
                      editedAt: new Date().toISOString(),
                      editedBy: user.name,
                    };
                  } else {
                    const currentVersion = {
                      id: `${originalDependency.id}_v${Date.now()}`,
                      title: originalDependency.title,
                      type: originalDependency.type,
                      status: originalDependency.status,
                      description: originalDependency.description,
                      dueDate: originalDependency.dueDate,
                      user: originalDependency.user,
                      editedAt: originalDependency.editedAt,
                      editedBy: originalDependency.editedBy,
                      isEdited: originalDependency.isEdited,
                    };

                    return {
                      ...dependency,
                      user: user.name,
                      editedAt: new Date().toISOString(),
                      editedBy: user.name,
                      isEdited: true,
                      versions: [
                        ...(originalDependency.versions || []),
                        currentVersion,
                      ],
                    };
                  }
                }
              }

              return dependency;
            }
          );
        }

        return processedData;
      },
      [user]
    );

    // Save function
    const save = useCallback(async () => {
      if (!hasUnsavedChanges) return;

      try {
        let processedData = processDataForSave(data, originalData);

        // Handle image uploads if there are new files
        if (data.newImageFiles?.length || data.deletedImages?.length) {
          const imageResult = await updateTabImages(
            productName || "default-product", // Use productName like sourcing brief
            "overview",
            data.newImageFiles || [],
            data.deletedImages || []
          );

          if (imageResult.success) {
            // Get existing Cloudinary images (not blob URLs) that weren't deleted
            const existingCloudinaryImages = (data.images || [])
              .filter(
                (img) =>
                  !img.url.startsWith("blob:") &&
                  !data.deletedImages?.includes(img.url)
              )
              .map((img) => img.url);

            // Create new image objects for uploaded images
            const newImageObjects = (imageResult.uploadedUrls || []).map(
              (url, index) => ({
                id: `img-${Date.now()}-${index}`,
                url,
                title: `Image ${existingCloudinaryImages.length + index + 1}`,
                description: "",
                uploadedAt: new Date().toISOString(),
                uploadedBy: user?.name || "Current User",
              })
            );

            // Combine existing and new images
            const existingImageObjects = (data.images || []).filter(
              (img) =>
                !img.url.startsWith("blob:") &&
                !data.deletedImages?.includes(img.url)
            );

            processedData = {
              ...processedData,
              images: [...existingImageObjects, ...newImageObjects],
              newImageFiles: undefined,
              deletedImages: undefined,
            };
          } else {
            toast.error(imageResult.message || "Failed to upload images");
            throw new Error(imageResult.message);
          }
        }

        await updateTabData(tabId, processedData as Record<string, unknown>);

        // Trigger notification refresh for potential auto-subscription
        window.dispatchEvent(new CustomEvent("refreshNotifications"));

        setOriginalData(processedData);
        setData(processedData);
        setHasUnsavedChanges(false);
        // Update preview URLs with final Cloudinary URLs
        const finalImageUrls = (processedData.images || []).map((img) =>
          typeof img === "string" ? img : img.url
        );
        setPreviewUrls(finalImageUrls);
        toast.success("Overview updated successfully");
      } catch (error) {
        console.error("Error saving overview:", error);
        toast.error("Failed to save overview changes");
        throw error;
      }
    }, [
      hasUnsavedChanges,
      tabId,
      data,
      originalData,
      processDataForSave,
      productName,
      user,
    ]);

    // Reset data when initialData changes (e.g., when switching products or after refresh)
    useEffect(() => {
      setData(initialData);
      setOriginalData(initialData);
      setHasUnsavedChanges(false);
    }, [initialData, tabId]);

    // Expose save function to parent
    useImperativeHandle(
      ref,
      () => ({
        save,
      }),
      [save]
    );

    return (
      <div className="h-full w-full flex flex-col">
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {/* Header */}
            {isEditing && (
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-muted-foreground">
                  Overview - Editing Mode
                </h2>
              </div>
            )}

            {/* Key Actions */}
            <KeyActionsCard
              data={data.keyActions || []}
              isEditing={isEditing}
              onUpdate={(keyActions) => updateData({ keyActions })}
              onUserMention={onUserMention}
              productId={productId}
              productName={productName}
              productSlug={productSlug}
              tabId={tabId}
            />

            {/* Dependencies and Next Steps Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <DependenciesCard
                data={data.dependencies || []}
                isEditing={isEditing}
                onUpdate={(dependencies) => updateData({ dependencies })}
                onUserMention={onUserMention}
                productId={productId}
                productName={productName}
                productSlug={productSlug}
                tabId={tabId}
              />

              <NextStepsCard
                data={data.nextSteps || []}
                isEditing={isEditing}
                onUpdate={(nextSteps) => updateData({ nextSteps })}
                onUserMention={onUserMention}
                productId={productId}
                productName={productName}
                productSlug={productSlug}
                tabId={tabId}
              />
            </div>

            {/* Images */}
            <ImagesCard
              data={previewUrls}
              isEditing={isEditing}
              onUpdate={(imageUrls) => {
                setPreviewUrls(imageUrls);
                // Convert URLs back to image objects for final data (excluding blob URLs for now)
                const imageObjects = imageUrls
                  .filter((url) => !url.startsWith("blob:")) // Only include non-blob URLs in data
                  .map((url, index) => ({
                    id: `img-${Date.now()}-${index}`,
                    url,
                    title: `Image ${index + 1}`,
                    description: "",
                    uploadedAt: new Date().toISOString(),
                    uploadedBy: user?.name || "Current User",
                  }));
                updateData({ images: imageObjects });
              }}
              productSlug={productName || "default-product"}
              onNewFiles={(files) => {
                // Store new files for upload on save
                const existingNewFiles = data.newImageFiles || [];
                const updatedNewFiles = [...existingNewFiles, ...files];
                updateData({ newImageFiles: updatedNewFiles });
              }}
            />
          </div>

          {/* Tab Danger Zone - Only show in edit mode */}
          {isEditing && tabName && productId && productName && productSlug && (
            <div className="mt-6">
              <TabDangerZone
                tabId={tabId}
                tabName={tabName}
                productId={productId}
                productName={productName}
                productSlug={productSlug}
                tabCreatorId={tabCreatorId}
                productOwnerId={productOwnerId}
                isEditMode={isEditing}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
);

OverviewDisplay.displayName = "OverviewDisplay";
