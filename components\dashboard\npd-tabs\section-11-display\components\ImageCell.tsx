"use client";

import { useState } from "react";
import Image from "next/image";

// Constants for tooltip positioning
const TOOLTIP_WIDTH_OFFSET: number = 280;
const TOOLTIP_HEIGHT_OFFSET: number = 400;

interface ImageCellProps {
  imageUrl: string;
  onCopy?: (url: string, cellId: string) => void;
  cellId?: string;
  copiedCell?: string | null;
}

export function ImageCell({
  imageUrl,
  onCopy,
  cellId,
  copiedCell,
}: ImageCellProps) {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  const handleMouseEnter = (e: React.MouseEvent) => {
    if (imageUrl) {
      const rect = e.currentTarget.getBoundingClientRect();
      setTooltipPosition({
        x: rect.right - TOOLTIP_WIDTH_OFFSET,
        y: rect.bottom - TOOLTIP_HEIGHT_OFFSET,
      });
      setShowTooltip(true);
    }
  };

  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  const handleClick = () => {
    if (imageUrl && onCopy && cellId) {
      onCopy(imageUrl, cellId);
    }
  };

  return (
    <>
      <div className="flex justify-center relative">
        {imageUrl ? (
          <div
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={handleClick}
            className="cursor-pointer relative"
          >
            <Image
              src={imageUrl}
              alt="Product"
              width={32}
              height={32}
              className="object-contain rounded border max-h-8 w-auto"
              onError={(e) => {
                e.currentTarget.style.display = "none";
              }}
            />
            {copiedCell === cellId && (
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                Copied!
              </div>
            )}
          </div>
        ) : (
          <div className="w-8 h-8 bg-muted rounded border flex items-center justify-center text-muted-foreground text-xs">
            N/A
          </div>
        )}
      </div>

      {/* Enlarged image tooltip - using fixed positioning to escape table boundaries */}
      {showTooltip && imageUrl && (
        <div
          className="fixed z-[9999] pointer-events-none"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
          }}
        >
          <div className="bg-background border border-border rounded-lg shadow-xl p-1">
            <Image
              src={imageUrl}
              alt="Product (enlarged)"
              width={130}
              height={130}
              className="object-contain rounded w-auto h-auto min-w-[130px] min-h-[130px]"
              onError={(e) => {
                e.currentTarget.style.display = "none";
              }}
            />
          </div>
        </div>
      )}
    </>
  );
}
