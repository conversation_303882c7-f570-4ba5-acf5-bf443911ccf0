"use client";

import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Section11Display,
  type Section11DisplayRef,
} from "@/components/dashboard/npd-tabs/section-11-display";
import {
  OverviewDisplay,
  type OverviewDisplayRef,
} from "@/components/dashboard/npd-tabs/overview-display";
import {
  Section12Display,
  type Section12DisplayRef,
} from "@/components/dashboard/npd-tabs/section-12-display";
import {
  Section13Display,
  type Section13DisplayRef,
} from "@/components/dashboard/npd-tabs/section-13-display";
import {
  SourcingBriefDisplay,
  type SourcingBriefDisplayRef,
} from "@/components/dashboard/npd-tabs/sourcing-brief-display";
import { TabDangerZone } from "@/components/dashboard/npd-tabs/shared/tab-danger-zone";
import { TagIcon } from "lucide-react";
import type { JsonValue } from "@prisma/client/runtime/library";

interface Tab {
  id: string;
  tabName: string;
  order: number;
  fields: JsonValue;
  user?: {
    id: string;
    name: string;
    email: string;
  } | null;
}

interface TabContentAreaProps {
  tabs: Tab[];
  isEditing: boolean;
  onDirtyChange: (isDirty: boolean) => void;
  productName: string;
  productId?: string;
  productSlug?: string;
  productOwnerId?: string | null;
  overviewRef?: React.RefObject<OverviewDisplayRef | null>;
  section11Ref?: React.RefObject<Section11DisplayRef | null>;
  section12Ref?: React.RefObject<Section12DisplayRef | null>;
  section13Ref?: React.RefObject<Section13DisplayRef | null>;
  sourcingBriefRef?: React.RefObject<SourcingBriefDisplayRef | null>;
  onUserMention?: (username: string) => void;
}

export function TabContentArea({
  tabs,
  isEditing,
  onDirtyChange,
  productName,
  productId,
  productSlug,
  productOwnerId,
  overviewRef,
  section11Ref,
  section12Ref,
  section13Ref,
  sourcingBriefRef,
  onUserMention,
}: TabContentAreaProps) {
  if (tabs.length === 0) {
    return (
      <div className="flex-1 min-h-0">
        <div className="h-full flex items-center justify-center relative">
          <div className="text-center py-8">
            <TagIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No data added</h3>
            <p className="text-muted-foreground">
              This product doesn&apos;t have any data added yet. Use the +
              button above to add product data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 min-h-0">
      {tabs
        .sort((a, b) => a.order - b.order)
        .map((tab) => (
          <TabsContent
            key={tab.id}
            value={tab.id}
            className="mt-0 h-full overflow-visible"
          >
            <div className="h-full overflow-hidden relative">
              {/* Check if this is Overview tab and use specialized component */}
              {tab.tabName === "Overview" &&
              tab.fields &&
              typeof tab.fields === "object" &&
              tab.fields !== null ? (
                <OverviewDisplay
                  ref={overviewRef}
                  tabId={tab.id}
                  initialData={tab.fields as Record<string, unknown>}
                  isEditing={isEditing}
                  onDirtyChange={onDirtyChange}
                  onUserMention={onUserMention}
                  productId={productId}
                  productName={productName}
                  productSlug={productSlug}
                  tabName={tab.tabName}
                  tabCreatorId={tab.user?.id}
                  productOwnerId={productOwnerId}
                />
              ) : tab.tabName === "1.1" &&
                tab.fields &&
                typeof tab.fields === "object" &&
                tab.fields !== null ? (
                <div className="p-6 space-y-6 h-full overflow-y-auto">
                  <Section11Display
                    ref={section11Ref}
                    tabId={tab.id}
                    initialData={
                      tab.fields as {
                        features: string;
                        color: string;
                        size: string;
                        pricerange: string;
                        launchIntoExistingListing: string;
                        dataDiveDashboardName: string;
                        dataDiveDashboardLink: string;
                        totalSellersInNiche: string;
                        googleTrendsKeyword: string;
                        googleTrendsData?: {
                          keyword: string;
                          data: Array<{
                            date: string;
                            value: number;
                            formattedDate: string;
                          }>;
                          fiveYearData?: Array<{
                            date: string;
                            value: number;
                            formattedDate: string;
                          }>;
                          relatedQueries: string[];
                          averageInterest: number;
                          trend: "rising" | "falling" | "stable";
                          peakInterest: number;
                          region: string;
                          timeframe: string;
                          isRealData?: boolean;
                          dataSource?: string;
                        };
                        competitors: Array<{
                          productDetails: string;
                          asin: string;
                          url: string;
                          imageUrl: string;
                          brand: string;
                          price: string;
                          asinSales: string;
                          asinRevenue: string;
                          bsr: string;
                          sellerCountry: string;
                          fees: string;
                          ratings: string;
                          reviewCount: string;
                          fulfillment: string;
                          dimensions: string;
                          weight: string;
                          creationDate: string;
                        }>;
                        user?: string;
                        editedAt?: string;
                        editedBy?: string;
                        isEdited?: boolean;
                        versions?: Array<{
                          id: string;
                          features: string;
                          color: string;
                          size: string;
                          pricerange: string;
                          user: string;
                          editedAt?: string;
                          editedBy?: string;
                          isEdited?: boolean;
                        }>;
                      }
                    }
                    isEditing={isEditing}
                    onDirtyChange={onDirtyChange}
                    tabName={tab.tabName}
                    productId={productId}
                    productName={productName}
                    productSlug={productSlug}
                    tabCreatorId={tab.user?.id}
                    productOwnerId={productOwnerId}
                  />
                </div>
              ) : tab.tabName === "1.2" &&
                tab.fields &&
                typeof tab.fields === "object" &&
                tab.fields !== null ? (
                <Section12Display
                  ref={section12Ref}
                  tabId={tab.id}
                  initialData={tab.fields as never}
                  isEditing={isEditing}
                  onDirtyChange={onDirtyChange}
                  onUserMention={onUserMention}
                  productId={productId || ""}
                  productName={productName}
                  productSlug={productSlug || ""}
                  tabName={tab.tabName}
                  tabCreatorId={tab.user?.id}
                  productOwnerId={productOwnerId}
                />
              ) : tab.tabName === "1.3" &&
                tab.fields &&
                typeof tab.fields === "object" &&
                tab.fields !== null ? (
                <Section13Display
                  ref={section13Ref}
                  tabId={tab.id}
                  initialData={tab.fields as never}
                  isEditing={isEditing}
                  onDirtyChange={onDirtyChange}
                  onUserMention={onUserMention}
                  productId={productId || ""}
                  productName={productName}
                  productSlug={productSlug || ""}
                  tabName={tab.tabName}
                  tabCreatorId={tab.user?.id}
                  productOwnerId={productOwnerId}
                />
              ) : tab.tabName === "Sourcing Brief" &&
                tab.fields &&
                typeof tab.fields === "object" &&
                tab.fields !== null ? (
                <SourcingBriefDisplay
                  ref={sourcingBriefRef}
                  tabId={tab.id}
                  initialData={tab.fields as never}
                  isEditing={isEditing}
                  onDirtyChange={onDirtyChange}
                  onUserMention={onUserMention}
                  productId={productId || ""}
                  productName={productName}
                  productSlug={productSlug || ""}
                  tabName={tab.tabName}
                  tabCreatorId={tab.user?.id}
                  productOwnerId={productOwnerId}
                />
              ) : (
                <div className="p-6 space-y-6 h-full overflow-y-auto">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TagIcon className="h-5 w-5" />
                        {tab.tabName}
                      </CardTitle>
                      <CardDescription>
                        Tab content and field data
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {tab.fields &&
                      typeof tab.fields === "object" &&
                      tab.fields !== null &&
                      !Array.isArray(tab.fields) ? (
                        <div className="space-y-4">
                          {Object.entries(
                            tab.fields as Record<string, unknown>
                          ).map(([key, value]) => (
                            <div
                              key={key}
                              className="grid grid-cols-1 md:grid-cols-3 gap-4"
                            >
                              <div>
                                <label className="text-sm font-medium text-muted-foreground">
                                  {key.charAt(0).toUpperCase() +
                                    key.slice(1).replace(/([A-Z])/g, " $1")}
                                </label>
                              </div>
                              <div className="md:col-span-2">
                                <div className="text-sm bg-muted/50 p-3 rounded border">
                                  {JSON.stringify(value)}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground">
                          No field data available for this tab.
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  {/* Tab Danger Zone for generic tabs - Only show in edit mode */}
                  {isEditing && productId && productName && productSlug && (
                    <TabDangerZone
                      tabId={tab.id}
                      tabName={tab.tabName}
                      productId={productId}
                      productName={productName}
                      productSlug={productSlug}
                      tabCreatorId={tab.user?.id}
                      productOwnerId={productOwnerId}
                      isEditMode={isEditing}
                    />
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        ))}
    </div>
  );
}
