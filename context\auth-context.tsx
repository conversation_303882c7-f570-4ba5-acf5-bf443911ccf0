"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { AuthPayload } from "@/lib/auth";

interface AuthContextType {
  user: AuthPayload | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
  refetch: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  setUser: (user: AuthPayload | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthPayload | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuthStatus = async () => {
    try {
      // Cache-busting request for serverless reliability
      const response = await fetch(`/api/auth/me?t=${Date.now()}`, {
        method: "GET",
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
        cache: "no-store",
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUserData = async () => {
    try {
      // First refresh the JWT token to get updated roles from database
      const refreshResponse = await fetch("/api/auth/refresh", {
        method: "POST",
      });

      if (refreshResponse.ok) {
        // Then fetch the updated user data
        const response = await fetch("/api/auth/me");
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        } else {
          setUser(null);
        }
      } else {
        // If refresh fails, try normal auth check
        await checkAuthStatus();
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
      // Fallback to normal auth check
      await checkAuthStatus();
    }
  };

  const signOut = async () => {
    try {
      // Clear user state immediately
      setUser(null);

      const response = await fetch("/api/auth/signout", {
        method: "POST",
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
        cache: "no-store",
      });

      if (response.ok) {
        // Clear auth-related browser storage
        try {
          const authKeys = [
            "quickt-auth-token",
            "auth-user",
            "user-session",
            "access-token",
            "refresh-token",
          ];
          authKeys.forEach((key) => localStorage.removeItem(key));

          const authSessionKeys = [
            "quickt-auth-token",
            "auth-user",
            "session-data",
            "oauth-state",
          ];
          authSessionKeys.forEach((key) => sessionStorage.removeItem(key));
        } catch {
          // Ignore storage errors
        }

        // Redirect to homepage
        window.location.replace("/");
      } else {
        // Even if API fails, clear state and redirect
        setUser(null);
        window.location.replace("/");
      }
    } catch (error) {
      console.error("Error signing out:", error);
      // Force clear state and redirect on error
      setUser(null);
      window.location.replace("/");
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        signOut,
        refetch: checkAuthStatus,
        refreshUserData,
        setUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
