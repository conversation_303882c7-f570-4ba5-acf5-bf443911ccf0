import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

// GET /api/admin/permissions - Get all permissions
export async function GET() {
  try {
    const user = await validateServerAction();

    // Check if user has admin or super admin role
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const hasAdminAccess = userWithRoles?.roles.some(
      (role) => role.name === "ADMIN" || role.name === "SUPER_ADMIN"
    );

    if (!hasAdminAccess) {
      return NextResponse.json(
        { message: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const permissions = await prisma.permission.findMany({
      include: {
        roles: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { code: "asc" },
    });

    return NextResponse.json(permissions);
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json(
      { message: "Failed to fetch permissions" },
      { status: 500 }
    );
  }
}

// POST /api/admin/permissions - Create new permission
export async function POST(request: NextRequest) {
  try {
    const user = await validateServerAction();

    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(
      (role) => role.name === "SUPER_ADMIN"
    );

    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    const { code } = await request.json();

    if (!code || typeof code !== "string") {
      return NextResponse.json(
        { message: "Permission code is required" },
        { status: 400 }
      );
    }

    // Validate permission code format
    const permissionCode = code.trim().toUpperCase();
    if (!/^[A-Z_]+$/.test(permissionCode)) {
      return NextResponse.json(
        {
          message:
            "Permission code must contain only uppercase letters and underscores",
        },
        { status: 400 }
      );
    }

    // Check if permission already exists
    const existingPermission = await prisma.permission.findUnique({
      where: { code: permissionCode },
    });

    if (existingPermission) {
      return NextResponse.json(
        { message: "Permission with this code already exists" },
        { status: 409 }
      );
    }

    const permission = await prisma.permission.create({
      data: { code: permissionCode },
    });

    return NextResponse.json(permission, { status: 201 });
  } catch (error) {
    console.error("Error creating permission:", error);
    return NextResponse.json(
      { message: "Failed to create permission" },
      { status: 500 }
    );
  }
}
