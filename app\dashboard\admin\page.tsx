import { AdminTabbedInterface } from "@/components/dashboard/admin/admin-tabbed-interface";
import { getCurrentUser } from "@/lib/auth";

export default async function AdminPage() {
  const user = await getCurrentUser();

  if (!user) {
    return (
      <div className="flex items-center justify-center py-8">
        <p>Access denied. Please log in.</p>
      </div>
    );
  }

  // Check if user is Super Admin
  const isSuperAdmin = user.roles.includes("SUPER_ADMIN");
  const userRole = isSuperAdmin ? "SUPER_ADMIN" : user.roles[0];

  return (
    <AdminTabbedInterface userRole={userRole} isSuperAdmin={isSuperAdmin} />
  );
}
