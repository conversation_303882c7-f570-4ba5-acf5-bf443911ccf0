"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import Spinner from "@/components/spinner";
import <PERSON>sonViewer from "./json-viewer";
import {
  Download,
  Upload,
  Database,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Edit,
  Eye,
  Save,
  RotateCcw,
} from "lucide-react";

interface ExportMetadata {
  exportedAt: string;
  version: string;
  totalRecords: number;
  models: Record<string, number>;
}

interface ImportResult {
  success: boolean;
  summary: {
    totalRecords: number;
    processed: number;
    created: number;
    updated: number;
    errors: number;
  };
  details: Record<
    string,
    { created: number; updated: number; errors: string[] }
  >;
  errors: string[];
}

export default function DatabaseManager() {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportResult, setExportResult] = useState<ExportMetadata | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importMode, setImportMode] = useState<
    "create" | "update" | "overwrite"
  >("overwrite");
  const [validateData, setValidateData] = useState(true);
  const [showImportConfirm, setShowImportConfirm] = useState(false);

  // JSON Editor state
  const [jsonData, setJsonData] = useState<string>("");
  const [originalJsonData, setOriginalJsonData] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);
  const [jsonError, setJsonError] = useState<string>("");
  const [showJsonEditor, setShowJsonEditor] = useState(false);
  const [parsedMetadata, setParsedMetadata] = useState<ExportMetadata | null>(
    null
  );

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setExportResult(null);

      const response = await fetch("/api/admin/export", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Export failed");
      }

      // Get the JSON data first to show metadata
      const exportData = await response.json();
      setExportResult(exportData.metadata);

      // Create download link
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `database-export-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export error:", error);
      alert("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file && file.type === "application/json") {
      setImportFile(file);
      setImportResult(null);

      // Read and parse the JSON file for preview/editing
      try {
        const fileContent = await file.text();
        const parsedData = JSON.parse(fileContent);

        // Validate basic structure
        if (parsedData.data && parsedData.metadata) {
          setJsonData(JSON.stringify(parsedData, null, 2));
          setOriginalJsonData(JSON.stringify(parsedData, null, 2));
          setParsedMetadata(parsedData.metadata);
          setJsonError("");
          setShowJsonEditor(false); // Start with preview mode
        } else {
          throw new Error("Invalid export file format");
        }
      } catch (error) {
        setJsonError(
          error instanceof Error ? error.message : "Invalid JSON file"
        );
        setJsonData("");
        setParsedMetadata(null);
      }
    } else {
      alert("Please select a valid JSON file.");
    }
  };

  const handleImport = async () => {
    if (!importFile) return;

    try {
      setIsImporting(true);
      setImportResult(null);

      // Read file content
      const fileContent = await importFile.text();
      const importData = JSON.parse(fileContent);

      // Validate file structure
      if (!importData.data || !importData.metadata) {
        throw new Error("Invalid import file format");
      }

      const response = await fetch("/api/admin/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: importData,
          options: {
            mode: importMode,
            validateData,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Import failed");
      }

      const result = await response.json();
      setImportResult(result);
    } catch (error) {
      setImportResult({
        success: false,
        summary: {
          totalRecords: 0,
          processed: 0,
          created: 0,
          updated: 0,
          errors: 1,
        },
        details: {},
        errors: [error instanceof Error ? error.message : "Unknown error"],
      });
    } finally {
      setIsImporting(false);
      setShowImportConfirm(false);
    }
  };

  // JSON Editor functions
  const handleJsonChange = (value: string) => {
    setJsonData(value);

    // Validate JSON as user types
    try {
      const parsed = JSON.parse(value);
      if (parsed.data && parsed.metadata) {
        setJsonError("");
        setParsedMetadata(parsed.metadata);
      } else {
        setJsonError("Invalid export file structure");
      }
    } catch {
      setJsonError("Invalid JSON format");
    }
  };

  const handleStartEditing = () => {
    setIsEditing(true);
    setShowJsonEditor(true);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
    setJsonData(originalJsonData);
    setJsonError("");
    setShowJsonEditor(false);
  };

  const handleSaveChanges = () => {
    try {
      const parsed = JSON.parse(jsonData);
      if (!parsed.data || !parsed.metadata) {
        throw new Error("Invalid export file structure");
      }

      // Create a new file blob with the edited JSON
      const blob = new Blob([jsonData], { type: "application/json" });
      const file = new File([blob], importFile?.name || "edited-export.json", {
        type: "application/json",
      });

      setImportFile(file);
      setOriginalJsonData(jsonData);
      setIsEditing(false);
      setShowJsonEditor(false);
      setJsonError("");

      alert("Changes saved successfully!");
    } catch (error) {
      setJsonError(error instanceof Error ? error.message : "Invalid JSON");
    }
  };

  const handleImportFromEditor = async () => {
    if (!jsonData) return;

    try {
      setIsImporting(true);
      setImportResult(null);

      // Use the edited JSON data directly
      const importData = JSON.parse(jsonData);

      // Validate file structure
      if (!importData.data || !importData.metadata) {
        throw new Error("Invalid import file format");
      }

      const response = await fetch("/api/admin/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: importData,
          options: {
            mode: importMode,
            validateData,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Import failed");
      }

      const result = await response.json();
      setImportResult(result);
    } catch (error) {
      console.error("Import error:", error);
      setImportResult({
        success: false,
        summary: {
          totalRecords: 0,
          processed: 0,
          created: 0,
          updated: 0,
          errors: 1,
        },
        details: {},
        errors: [error instanceof Error ? error.message : "Unknown error"],
      });
    } finally {
      setIsImporting(false);
      setShowImportConfirm(false);
    }
  };

  const confirmImport = () => {
    setShowImportConfirm(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Database className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Database Management</h2>
      </div>

      <Tabs defaultValue="export" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="export">Export Data</TabsTrigger>
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="editor" disabled={!importFile}>
            JSON Editor
          </TabsTrigger>
        </TabsList>

        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Export Database
              </CardTitle>
              <CardDescription>
                Export all database records to a JSON file for backup or
                migration purposes.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Full Database Export</h4>
                    <p className="text-sm text-muted-foreground">
                      Exports all records from all tables with proper
                      relationship handling
                    </p>
                  </div>
                  <Button onClick={handleExport} disabled={isExporting}>
                    {isExporting ? (
                      <>
                        <Spinner loading={true} />
                        Exporting...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Export Now
                      </>
                    )}
                  </Button>
                </div>

                {exportResult && (
                  <Card className="bg-green-50 border-green-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-green-800 flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        Export Completed
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Export Date:</span>
                          <br />
                          {new Date(exportResult.exportedAt).toLocaleString()}
                        </div>
                        <div>
                          <span className="font-medium">Total Records:</span>
                          <br />
                          <Badge variant="secondary">
                            {exportResult.totalRecords.toLocaleString()}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-sm">
                          Records by Model:
                        </span>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {Object.entries(exportResult.models).map(
                            ([model, count]) => (
                              <Badge key={model} variant="outline">
                                {model}: {count}
                              </Badge>
                            )
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Import Database
              </CardTitle>
              <CardDescription>
                Import data from a previously exported JSON file.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="import-file"
                    className="block text-sm font-medium mb-2"
                  >
                    Select Import File
                  </label>
                  <input
                    id="import-file"
                    type="file"
                    accept=".json"
                    onChange={handleFileSelect}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>

                {importFile && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Import Mode
                      </label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            value="create"
                            checked={importMode === "create"}
                            onChange={(e) =>
                              setImportMode(
                                e.target.value as
                                  | "create"
                                  | "update"
                                  | "overwrite"
                              )
                            }
                          />
                          <span className="text-sm">
                            Create only (skip existing records)
                          </span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            value="update"
                            checked={importMode === "update"}
                            onChange={(e) =>
                              setImportMode(
                                e.target.value as
                                  | "create"
                                  | "update"
                                  | "overwrite"
                              )
                            }
                          />
                          <span className="text-sm">
                            Update only (update existing records)
                          </span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            value="overwrite"
                            checked={importMode === "overwrite"}
                            onChange={(e) =>
                              setImportMode(
                                e.target.value as
                                  | "create"
                                  | "update"
                                  | "overwrite"
                              )
                            }
                          />
                          <span className="text-sm">
                            Overwrite (create new or update existing)
                          </span>
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={validateData}
                          onChange={(e) => setValidateData(e.target.checked)}
                        />
                        <span className="text-sm">
                          Validate data before import
                        </span>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">
                            Import Warning
                          </h4>
                          <p className="text-sm text-yellow-700">
                            This will modify your database. Make sure you have a
                            backup before proceeding.
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={confirmImport}
                        disabled={isImporting}
                        variant="destructive"
                      >
                        {isImporting ? (
                          <>
                            <Spinner loading={true} />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            Import Data
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}

                {importResult && (
                  <Card
                    className={
                      importResult.success
                        ? "bg-green-50 border-green-200"
                        : "bg-red-50 border-red-200"
                    }
                  >
                    <CardHeader className="pb-3">
                      <CardTitle
                        className={`flex items-center gap-2 ${
                          importResult.success
                            ? "text-green-800"
                            : "text-red-800"
                        }`}
                      >
                        {importResult.success ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <XCircle className="h-5 w-5" />
                        )}
                        Import {importResult.success ? "Completed" : "Failed"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Total:</span>
                          <br />
                          <Badge variant="secondary">
                            {importResult.summary.totalRecords}
                          </Badge>
                        </div>
                        <div>
                          <span className="font-medium">Processed:</span>
                          <br />
                          <Badge variant="secondary">
                            {importResult.summary.processed}
                          </Badge>
                        </div>
                        <div>
                          <span className="font-medium">Created:</span>
                          <br />
                          <Badge className="bg-green-100 text-green-800">
                            {importResult.summary.created}
                          </Badge>
                        </div>
                        <div>
                          <span className="font-medium">Updated:</span>
                          <br />
                          <Badge className="bg-blue-100 text-blue-800">
                            {importResult.summary.updated}
                          </Badge>
                        </div>
                      </div>

                      {importResult.summary.errors > 0 && (
                        <div>
                          <span className="font-medium text-sm text-red-800">
                            Errors ({importResult.summary.errors}):
                          </span>
                          <div className="mt-2 space-y-1">
                            {importResult.errors.map((error, index) => (
                              <div
                                key={index}
                                className="text-sm text-red-700 bg-red-100 p-2 rounded"
                              >
                                {error}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div>
                        <span className="font-medium text-sm">
                          Details by Model:
                        </span>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                          {Object.entries(importResult.details).map(
                            ([model, details]) => (
                              <div
                                key={model}
                                className="text-sm p-2 border rounded"
                              >
                                <span className="font-medium">{model}:</span>
                                <span className="ml-2">
                                  +{details.created} created, ~{details.updated}{" "}
                                  updated
                                  {details.errors.length > 0 && (
                                    <span className="text-red-600">
                                      {" "}
                                      ({details.errors.length} errors)
                                    </span>
                                  )}
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="editor" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                JSON Editor & Preview
              </CardTitle>
              <CardDescription>
                Review and edit your import data before processing. The JSON
                viewer provides multiple views: Tree (structured), Form
                (user-friendly), and Raw (direct JSON editing).
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {importFile && parsedMetadata && (
                <div className="space-y-4">
                  {/* File Info */}
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">
                      File Information
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium">File:</span>
                        <br />
                        {importFile.name}
                      </div>
                      <div>
                        <span className="font-medium">Export Date:</span>
                        <br />
                        {new Date(parsedMetadata.exportedAt).toLocaleString()}
                      </div>
                      <div>
                        <span className="font-medium">Version:</span>
                        <br />
                        {parsedMetadata.version}
                      </div>
                      <div>
                        <span className="font-medium">Total Records:</span>
                        <br />
                        <Badge variant="secondary">
                          {parsedMetadata.totalRecords.toLocaleString()}
                        </Badge>
                      </div>
                    </div>
                    <div className="mt-3">
                      <span className="font-medium text-sm">
                        Records by Model:
                      </span>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {Object.entries(parsedMetadata.models).map(
                          ([model, count]) => (
                            <Badge key={model} variant="outline">
                              {model}: {count}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    {!isEditing ? (
                      <>
                        <Button onClick={handleStartEditing} variant="outline">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit JSON
                        </Button>
                        <Button
                          onClick={() => setShowJsonEditor(!showJsonEditor)}
                          variant="outline"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {showJsonEditor ? "Hide" : "Show"} JSON
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          onClick={handleSaveChanges}
                          disabled={!!jsonError}
                        >
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </Button>
                        <Button onClick={handleCancelEditing} variant="outline">
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Cancel
                        </Button>
                      </>
                    )}
                  </div>

                  {/* JSON Error */}
                  {jsonError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 text-red-800">
                        <XCircle className="h-4 w-4" />
                        <span className="font-medium">JSON Error:</span>
                      </div>
                      <p className="text-red-700 text-sm mt-1">{jsonError}</p>
                    </div>
                  )}

                  {/* JSON Editor/Preview */}
                  {showJsonEditor && (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">
                        {isEditing ? "Edit JSON Data:" : "JSON Preview:"}
                      </label>
                      {jsonData && !jsonError ? (
                        <JsonViewer
                          data={JSON.parse(jsonData)}
                          onDataChange={(newData) =>
                            handleJsonChange(JSON.stringify(newData, null, 2))
                          }
                          isEditing={isEditing}
                        />
                      ) : (
                        <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500">
                          {jsonError ? (
                            <div className="text-red-500">
                              <XCircle className="h-8 w-8 mx-auto mb-2" />
                              <p>Invalid JSON format</p>
                              <p className="text-sm">{jsonError}</p>
                            </div>
                          ) : (
                            <div>
                              <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
                              <p>No valid JSON data to display</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Import from Editor */}
                  {jsonData && !jsonError && (
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50 border-green-200">
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-800">
                            Ready to Import
                          </h4>
                          <p className="text-sm text-green-700">
                            JSON is valid and ready for import with current
                            settings.
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={() => setShowImportConfirm(true)}
                        disabled={isImporting}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        {isImporting ? (
                          <>
                            <Spinner loading={true} />
                            Importing...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            Import Data
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {!importFile && (
                <div className="text-center py-8 text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>
                    Select a JSON file in the Import tab to review and edit it
                    here.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <ConfirmationDialog
        open={showImportConfirm}
        onOpenChange={setShowImportConfirm}
        title="Confirm Database Import"
        description={`Are you sure you want to import this data in "${importMode}" mode? This action cannot be undone.`}
        confirmText="Import Data"
        variant="destructive"
        onConfirm={jsonData ? handleImportFromEditor : handleImport}
      />
    </div>
  );
}
