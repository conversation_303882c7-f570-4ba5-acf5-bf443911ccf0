import { NextResponse } from "next/server";
import {
  getCurrentUser,
  signAuthToken,
  setAuthCookieOnResponse,
} from "@/lib/auth";

export async function POST() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Generate a fresh token with updated user data from database
    const newToken = await signAuthToken(currentUser.userId);

    const response = NextResponse.json({
      success: true,
      message: "Token refreshed successfully",
    });

    // Set the new token as a cookie
    setAuthCookieOnResponse(response, newToken);

    return response;
  } catch (error) {
    console.error("Error refreshing token:", error);
    return NextResponse.json(
      { error: "Failed to refresh token" },
      { status: 500 }
    );
  }
}
