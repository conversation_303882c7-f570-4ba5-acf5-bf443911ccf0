import { LucideIcon } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface FormFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  disabled?: boolean;
  className?: string;
  type?: "input" | "textarea";
  rows?: number;
  isChanged?: boolean;
  isEditing?: boolean;
}

export function FormField({
  id,
  label,
  value,
  onChange,
  placeholder,
  disabled = false,
  className = "",
  type = "input",
  rows = 4,
  isChanged = false,
  isEditing = false,
}: FormFieldProps) {
  const Component = type === "textarea" ? Textarea : Input;

  return (
    <div className="space-y-2">
      <Label htmlFor={id}>
        <div className="flex items-center gap-2">
          {label}
          {isEditing && isChanged && (
            <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
          )}
        </div>
      </Label>
      <Component
        id={id}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={className}
        {...(type === "textarea" && { rows })}
      />
    </div>
  );
}

interface FormSectionProps {
  title: string;
  icon: LucideIcon;
  children: React.ReactNode;
  hasUnsavedChanges?: boolean;
}

export function FormSection({
  title,
  icon: Icon,
  children,
  hasUnsavedChanges = false,
}: FormSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className="h-5 w-5" />
          {title}
          {hasUnsavedChanges && (
            <div className="flex items-center gap-1 ml-auto">
              <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
              <span className="text-xs text-amber-600 font-normal">
                Unsaved changes
              </span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">{children}</CardContent>
    </Card>
  );
}

interface FormGridProps {
  children: React.ReactNode;
  columns?: 1 | 2;
}

export function FormGrid({ children, columns = 2 }: FormGridProps) {
  const gridClass =
    columns === 1
      ? "grid grid-cols-1 gap-4"
      : "grid grid-cols-1 md:grid-cols-2 gap-4";

  return <div className={gridClass}>{children}</div>;
}
