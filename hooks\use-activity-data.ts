"use client";

import { useCallback, useState, useEffect, useRef } from "react";
import {
  getActivityLogsWithReadStatus,
  getActivityUnreadCount,
  type ActivityLog,
  type ActivityUnreadData,
} from "@/actions/activity.actions";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";

export function useActivityData(productId: string, enabled: boolean = true) {
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [unreadData, setUnreadData] = useState<ActivityUnreadData>({
    unreadCount: 0,
    totalCount: 0,
    readCount: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Refs for polling
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch activity logs
  const fetchActivityLogs = useCallback(async () => {
    if (!productId || !enabled) {
      setActivityLogs([]);
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      setIsLoading(true);

      const logs = await getActivityLogsWithReadStatus(productId);
      setActivityLogs(logs);
    } catch (error) {
      console.error("Error fetching activity logs:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch activity logs";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [productId, enabled]);

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    if (!productId || !enabled) {
      setUnreadData({ unreadCount: 0, totalCount: 0, readCount: 0 });
      return;
    }

    try {
      const result = await getActivityUnreadCount(productId);
      setUnreadData(result);
    } catch (error) {
      console.error("Error fetching activity unread count:", error);
      // Don't set error for unread count failures, just log them
    }
  }, [productId, enabled]);

  // Refresh all data
  const refresh = useCallback(() => {
    fetchActivityLogs();
    fetchUnreadCount();
  }, [fetchActivityLogs, fetchUnreadCount]);

  // Setup polling for real-time updates
  useEffect(() => {
    if (!productId || !enabled || !isPollingEnabled("ACTIVITY_READ_STATUS"))
      return;

    const interval = getPollingInterval("ACTIVITY_READ_STATUS");

    const poll = () => {
      // Poll both activity logs and unread count
      fetchUnreadCount();
      // Optionally refresh activity logs less frequently
      fetchActivityLogs();
    };

    pollTimeoutRef.current = setTimeout(function pollInterval() {
      poll();
      pollTimeoutRef.current = setTimeout(pollInterval, interval);
    }, interval);

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, [productId, enabled, fetchActivityLogs, fetchUnreadCount]);

  // Initial fetch with delay to prevent blocking product switching
  useEffect(() => {
    if (!enabled) return;

    // Add a small delay to prevent blocking product switching
    const timer = setTimeout(() => {
      refresh();
    }, 100); // 100ms delay

    return () => clearTimeout(timer);
  }, [refresh, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, []);

  // Update an activity log in the local state (optimistic updates)
  const updateActivityInState = useCallback((updatedActivity: ActivityLog) => {
    setActivityLogs((prev) =>
      prev.map((activity) =>
        activity.id === updatedActivity.id ? updatedActivity : activity
      )
    );
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Data
    activityLogs,
    unreadCount: unreadData.unreadCount,
    totalCount: unreadData.totalCount,
    readCount: unreadData.readCount,

    // Loading states
    isLoading,
    error,

    // Actions
    refresh,
    clearError,

    // Optimistic update helpers
    updateActivityInState,
  };
}
