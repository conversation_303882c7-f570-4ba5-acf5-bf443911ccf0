"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Activity,
  Plus,
  Trash2,
  Calendar,
  ChevronDown,
  ChevronRight,
  History,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { DeletionRequestDialog } from "@/components/ui/deletion-request-dialog";
import { toast } from "sonner";
import { sendDeletionRequestNotification } from "@/actions/overview-notifications.actions";

interface KeyActionVersion {
  id: string;
  title: string;
  description: string;
  date: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface KeyAction {
  id: string;
  title: string;
  description: string;
  date: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: KeyActionVersion[]; // Previous versions
}

interface KeyActionsCardProps {
  data: KeyAction[];
  isEditing: boolean;
  onUpdate: (data: KeyAction[]) => void;
  onUserMention?: (username: string) => void;
  productId?: string;
  productName?: string;
  productSlug?: string;
  tabId?: string;
}

// Removed category options - no dropdown needed

export function KeyActionsCard({
  data,
  isEditing,
  onUpdate,
  onUserMention,
  productId,
  productName,
  productSlug,
  tabId,
}: KeyActionsCardProps) {
  const [localData, setLocalData] = useState<KeyAction[]>(data);
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(
    new Set()
  );
  const [showDeletionDialog, setShowDeletionDialog] = useState(false);
  const [actionToDelete, setActionToDelete] = useState<KeyAction | null>(null);
  const { user } = useAuth();

  const handleUserMention = (username: string) => {
    if (onUserMention) {
      onUserMention(username);
    }
  };

  const updateLocalData = (newData: KeyAction[]) => {
    setLocalData(newData);
    onUpdate(newData);
  };

  const addAction = () => {
    const newAction: KeyAction = {
      id: Date.now().toString(),
      title: "",
      description: "",
      date: "",
      user: user?.name || "", // Automatically populate with current user's name
      isEdited: false, // Explicitly set as not edited for new entries
    };
    updateLocalData([...localData, newAction]);
  };

  const updateAction = (id: string, updates: Partial<KeyAction>) => {
    const updatedActions = localData.map((a) =>
      a.id === id ? { ...a, ...updates } : a
    );
    updateLocalData(updatedActions);
  };

  const toggleVersions = (actionId: string) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(actionId)) {
      newExpanded.delete(actionId);
    } else {
      newExpanded.add(actionId);
    }
    setExpandedVersions(newExpanded);
  };

  const removeAction = (id: string) => {
    const action = localData.find((a) => a.id === id);
    if (!action || !user) return;

    const isOwnEntry = action.user === user.name;

    if (isOwnEntry) {
      // User can delete their own entry
      const filteredActions = localData.filter((a) => a.id !== id);
      updateLocalData(filteredActions);
    } else {
      // Show deletion request dialog for others' entries
      setActionToDelete(action);
      setShowDeletionDialog(true);
    }
  };

  const handleSendDeletionRequest = async () => {
    if (
      !actionToDelete ||
      !productId ||
      !productName ||
      !productSlug ||
      !tabId ||
      !actionToDelete.user
    ) {
      return;
    }

    const result = await sendDeletionRequestNotification(
      actionToDelete.user,
      "key-action",
      actionToDelete.title,
      productId,
      productName,
      productSlug,
      tabId,
      actionToDelete.id
    );

    // Reset state
    setActionToDelete(null);

    // Show result message
    if (result.success) {
      toast.success("Deletion request sent", {
        description: result.message,
      });
    } else {
      toast.error("Failed to send deletion request", {
        description: result.message,
      });
    }
  };

  // Sort actions by date (most recent first)
  const sortedActions = [...localData].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-green-500" />
            Key Actions Made
          </CardTitle>
          {isEditing && (
            <Button size="sm" variant="outline" onClick={addAction}>
              <Plus className="h-4 w-4 mr-1" />
              Add Action
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedActions.map((action) => (
            <div
              key={action.id}
              id={`entry-key-action-${action.id}`}
              className="border rounded-lg p-4 space-y-3"
            >
              {isEditing ? (
                /* Edit mode - simple edit interface */
                <div className="space-y-3">
                  <Input
                    placeholder="Action title"
                    value={action.title}
                    onChange={(e) =>
                      updateAction(action.id, { title: e.target.value })
                    }
                  />
                  <Input
                    type="date"
                    value={action.date}
                    onChange={(e) =>
                      updateAction(action.id, { date: e.target.value })
                    }
                  />
                  <Textarea
                    placeholder="Action description (optional)"
                    value={action.description || ""}
                    onChange={(e) =>
                      updateAction(action.id, { description: e.target.value })
                    }
                    rows={2}
                  />
                  <div className="flex justify-end">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeAction(action.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                </div>
              ) : (
                /* View mode */
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        {/* Version toggle icon on the left */}
                        {action.versions && action.versions.length > 0 && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleVersions(action.id)}
                            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
                          >
                            {expandedVersions.has(action.id) ? (
                              <ChevronDown className="h-3 w-3" />
                            ) : (
                              <ChevronRight className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                        <h4 className="font-medium">{action.title}</h4>
                        {action.isEdited && (
                          <Badge variant="secondary" className="text-xs">
                            edited
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(action.date).toLocaleDateString()}
                        </div>
                        {action.user && (
                          <div className="flex items-center gap-1">
                            <button
                              className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm bg-blue-50 px-1 py-0.5 rounded-sm"
                              onClick={() =>
                                action.user && handleUserMention(action.user)
                              }
                            >
                              @{action.user}
                            </button>
                          </div>
                        )}
                        {action.editedAt &&
                          action.editedBy &&
                          action.editedBy !== action.user && (
                            <div className="text-xs text-muted-foreground">
                              edited by @{action.editedBy} on{" "}
                              {new Date(action.editedAt).toLocaleDateString()}
                            </div>
                          )}
                      </div>
                    </div>
                  </div>

                  {action.description && (
                    <p className="text-sm text-muted-foreground">
                      {action.description}
                    </p>
                  )}

                  {/* Previous versions */}
                  {action.versions &&
                    action.versions.length > 0 &&
                    expandedVersions.has(action.id) && (
                      <div className="mt-3 pt-3 border-t border-dashed">
                        <div className="flex items-center gap-2 mb-2">
                          <History className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground font-medium">
                            Previous Versions
                          </span>
                        </div>
                        <div className="space-y-2 ml-4">
                          {[...action.versions].reverse().map((version) => (
                            <div
                              key={version.id}
                              className="p-2 bg-muted/30 rounded text-sm"
                            >
                              <div className="font-medium text-muted-foreground">
                                {version.title}
                              </div>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                <span>by @{version.user}</span>
                                <span>•</span>
                                <span>
                                  {new Date(version.date).toLocaleDateString()}
                                </span>
                              </div>
                              {version.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {version.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          ))}

          {localData.length === 0 && !isEditing && (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No key actions recorded yet</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Deletion Request Dialog */}
      <DeletionRequestDialog
        open={showDeletionDialog}
        onOpenChange={setShowDeletionDialog}
        creatorUsername={actionToDelete?.user || ""}
        entryType="key action"
        entryTitle={actionToDelete?.title || ""}
        onSendRequest={handleSendDeletionRequest}
        onCancel={() => setActionToDelete(null)}
      />
    </Card>
  );
}
