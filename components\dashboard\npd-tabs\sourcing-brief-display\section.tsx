import { AccordionContent, AccordionTrigger } from "@/components/ui/accordion";

interface SectionProps {
  title: string;
  children: React.ReactNode;
}

export function Section({ title, children }: SectionProps) {
  return (
    <>
      <AccordionTrigger className="text-left font-medium">
        {title}
      </AccordionTrigger>
      <AccordionContent>
        <div className="space-y-4 pt-4">
          {children}
        </div>
      </AccordionContent>
    </>
  );
}
