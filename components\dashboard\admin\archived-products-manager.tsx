"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  fetchArchivedProducts,
  getArchivedProductStats,
  searchArchivedProducts,
  bulkRestoreProducts,
  bulkPermanentlyDeleteProducts,
} from "@/actions/admin-npds.actions";
import { restoreProduct } from "@/actions/npd-deletion.actions";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Archive,
  RotateCcw,
  Trash2,
  Search,
  Package,
  Calendar,
  BarChart3,
} from "lucide-react";
import Spinner from "@/components/spinner";

interface ArchivedProduct {
  id: string;
  name: string;
  slug: string;
  brand: string;
  stage: string;
  description: string | null;
  archivedAt: Date | null;
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
  archivedByUser: {
    id: string;
    name: string;
    email: string;
  } | null;
  _count: {
    tabs: number;
    activityLogs: number;
    chats: number;
    subscriptions: number;
  };
}

interface ArchivedProductStats {
  totalArchived: number;
  archivedThisMonth: number;
  archivedByUser: Array<{
    userId: string | null;
    userName: string;
    userEmail: string;
    count: number;
  }>;
}

export function ArchivedProductsManager() {
  const [products, setProducts] = useState<ArchivedProduct[]>([]);
  const [stats, setStats] = useState<ArchivedProductStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [actionLoading, setActionLoading] = useState(false);

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsData, statsData] = await Promise.all([
        fetchArchivedProducts(),
        getArchivedProductStats(),
      ]);
      setProducts(productsData);
      setStats(statsData);
    } catch (error) {
      console.error("Error loading archived products:", error);
      toast.error("Failed to load archived products");
    } finally {
      setLoading(false);
    }
  };

  // Search products
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadData();
      return;
    }

    try {
      setLoading(true);
      const searchResults = await searchArchivedProducts(searchQuery);
      setProducts(searchResults);
    } catch (error) {
      console.error("Error searching products:", error);
      toast.error("Failed to search products");
    } finally {
      setLoading(false);
    }
  };

  // Restore single product
  const handleRestoreProduct = async (
    productId: string,
    productName: string
  ) => {
    try {
      setActionLoading(true);
      const result = await restoreProduct(productId);

      if (result.success) {
        toast.success(`Product "${productName}" restored successfully`);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error restoring product:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to restore product"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk restore products
  const handleBulkRestore = async () => {
    if (selectedProducts.length === 0) {
      toast.error("Please select products to restore");
      return;
    }

    try {
      setActionLoading(true);
      const result = await bulkRestoreProducts(selectedProducts);

      if (result.success) {
        toast.success(result.message);
        setSelectedProducts([]);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error bulk restoring products:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to restore products"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Bulk permanently delete products
  const handleBulkDelete = async () => {
    if (selectedProducts.length === 0) {
      toast.error("Please select products to delete");
      return;
    }

    try {
      setActionLoading(true);
      const result = await bulkPermanentlyDeleteProducts(selectedProducts);

      if (result.success) {
        toast.success(result.message);
        setSelectedProducts([]);
        loadData(); // Refresh the list
      }
    } catch (error) {
      console.error("Error bulk deleting products:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete products"
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Toggle product selection
  const toggleProductSelection = (productId: string) => {
    setSelectedProducts((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  // Select all products
  const toggleSelectAll = () => {
    setSelectedProducts(
      selectedProducts.length === products.length
        ? []
        : products.map((p) => p.id)
    );
  };

  if (loading && !products.length) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner loading={true} size={12} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Archived
              </CardTitle>
              <Archive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalArchived}</div>
              <p className="text-xs text-muted-foreground">
                NPD projects in archive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.archivedThisMonth}
              </div>
              <p className="text-xs text-muted-foreground">
                Archived this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Top Archiver
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.archivedByUser[0]?.count || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.archivedByUser[0]?.userName || "No data"}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Archived NPD Management</CardTitle>
          <CardDescription>
            Restore or permanently delete archived NPD projects
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, brand, or slug..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} variant="outline">
              Search
            </Button>
            <Button onClick={loadData} variant="outline">
              Reset
            </Button>
          </div>

          {/* Bulk Actions */}
          {selectedProducts.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <span className="text-sm font-medium">
                {selectedProducts.length} selected
              </span>
              <Button
                size="sm"
                onClick={handleBulkRestore}
                disabled={actionLoading}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Restore Selected
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="destructive"
                    disabled={actionLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete Selected
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Permanently Delete NPD Projects?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete {selectedProducts.length} NPD
                      projects and all their data. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBulkDelete}
                      className="bg-destructive text-white hover:bg-destructive/90"
                    >
                      Delete Permanently
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Products List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Archived NPD ({products.length})</CardTitle>
              <CardDescription>
                NPD projects that have been archived and can be restored
              </CardDescription>
            </div>
            {products.length > 0 && (
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedProducts.length === products.length}
                  onCheckedChange={toggleSelectAll}
                />
                <span className="text-sm text-muted-foreground">
                  Select All
                </span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Archived NPD</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? "No NPD products match your search."
                  : "No NPD products have been archived yet."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center gap-4 p-4 border rounded-lg"
                >
                  <Checkbox
                    checked={selectedProducts.includes(product.id)}
                    onCheckedChange={() => toggleProductSelection(product.id)}
                  />

                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{product.name}</h4>
                      <Badge variant="outline">{product.brand}</Badge>
                      <Badge variant="secondary">{product.stage}</Badge>
                    </div>

                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>Slug: {product.slug}</div>
                      <div className="flex items-center gap-4">
                        <span>
                          Archived:{" "}
                          {product.archivedAt
                            ? new Date(product.archivedAt).toLocaleDateString()
                            : "Unknown"}
                        </span>
                        {product.archivedByUser && (
                          <span>By: {product.archivedByUser.name}</span>
                        )}
                        {product.user && (
                          <span>Owner: {product.user.name}</span>
                        )}
                      </div>
                      <div className="flex items-center gap-4">
                        <span>{product._count.tabs} tabs</span>
                        <span>{product._count.activityLogs} activities</span>
                        <span>{product._count.chats} chats</span>
                        <span>{product._count.subscriptions} subscribers</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() =>
                        handleRestoreProduct(product.id, product.name)
                      }
                      disabled={actionLoading}
                    >
                      <RotateCcw className="h-4 w-4 mr-1" />
                      Restore
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
