"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

/**
 * Fetch all archived tabs - only for admins
 */
export async function fetchArchivedTabs() {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const archivedTabs = await prisma.nPDProductTab.findMany({
      where: {
        archived: true,
      },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
            slug: true,
            brand: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            history: true,
            activityLogs: true,
          },
        },
      },
      orderBy: {
        archivedAt: "desc",
      },
    });

    return archivedTabs;
  } catch (error) {
    console.error("Error fetching archived tabs:", error);
    throw error;
  }
}

/**
 * Get archived tab statistics - only for admins
 */
export async function getArchivedTabStats() {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const [
      totalArchived,
      archivedThisMonth,
      archivedByUser,
      archivedByProduct,
    ] = await Promise.all([
      // Total archived tabs
      prisma.nPDProductTab.count({
        where: {
          archived: true,
        },
      }),

      // Archived this month
      prisma.nPDProductTab.count({
        where: {
          archived: true,
          archivedAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      }),

      // Archived tabs by user
      prisma.nPDProductTab.groupBy({
        by: ["archivedBy"],
        where: {
          archived: true,
          archivedBy: {
            not: null,
          },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: "desc",
          },
        },
        take: 5,
      }),

      // Archived tabs by product
      prisma.nPDProductTab.groupBy({
        by: ["npdProductId"],
        where: {
          archived: true,
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: "desc",
          },
        },
        take: 5,
      }),
    ]);

    // Get user details for the top archivers
    const archivedByUserIds = archivedByUser
      .map((item) => item.archivedBy)
      .filter(Boolean);
    const users = await prisma.user.findMany({
      where: {
        id: {
          in: archivedByUserIds as string[],
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    // Get product details for products with most archived tabs
    const productIds = archivedByProduct.map((item) => item.npdProductId);
    const products = await prisma.nPDProduct.findMany({
      where: {
        id: {
          in: productIds,
        },
      },
      select: {
        id: true,
        name: true,
        brand: true,
      },
    });

    const archivedByUserWithDetails = archivedByUser.map((item) => {
      const user = users.find((u) => u.id === item.archivedBy);
      return {
        userId: item.archivedBy,
        userName: user?.name || "Unknown",
        userEmail: user?.email || "Unknown",
        count: item._count.id,
      };
    });

    const archivedByProductWithDetails = archivedByProduct.map((item) => {
      const product = products.find((p) => p.id === item.npdProductId);
      return {
        npdProductId: item.npdProductId,
        productName: product?.name || "Unknown",
        productBrand: product?.brand || "Unknown",
        count: item._count.id,
      };
    });

    return {
      totalArchived,
      archivedThisMonth,
      archivedByUser: archivedByUserWithDetails,
      archivedByProduct: archivedByProductWithDetails,
    };
  } catch (error) {
    console.error("Error fetching archived tab stats:", error);
    throw error;
  }
}

/**
 * Search archived tabs - only for admins
 */
export async function searchArchivedTabs(query: string) {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const archivedTabs = await prisma.nPDProductTab.findMany({
      where: {
        archived: true,
        OR: [
          {
            tabName: {
              contains: query,
              mode: "insensitive",
            },
          },
          {
            npdProduct: {
              name: {
                contains: query,
                mode: "insensitive",
              },
            },
          },
          {
            npdProduct: {
              brand: {
                contains: query,
                mode: "insensitive",
              },
            },
          },
        ],
      },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
            slug: true,
            brand: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            history: true,
            activityLogs: true,
          },
        },
      },
      orderBy: {
        archivedAt: "desc",
      },
      take: 50, // Limit results
    });

    return archivedTabs;
  } catch (error) {
    throw error;
  }
}

/**
 * Bulk restore tabs - only for super admins
 */
export async function bulkRestoreTabs(tabIds: string[]) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    // Restore tabs
    const restoredTabs = await prisma.nPDProductTab.updateMany({
      where: {
        id: {
          in: tabIds,
        },
        archived: true,
      },
      data: {
        archived: false,
        archivedAt: null,
        archivedBy: null,
      },
    });

    return {
      success: true,
      message: `Successfully restored ${restoredTabs.count} tabs`,
      restoredCount: restoredTabs.count,
    };
  } catch (error) {
    console.error("Error bulk restoring tabs:", error);
    throw error;
  }
}

/**
 * Bulk permanently delete tabs - only for super admins
 */
export async function bulkPermanentlyDeleteTabs(tabIds: string[]) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    let deletedCount = 0;

    // Delete each tab in a transaction
    for (const tabId of tabIds) {
      await prisma.$transaction(async (tx) => {
        // Delete tab completion records
        await tx.nPDProductTabCompletion.deleteMany({
          where: { npdProductTabId: tabId },
        });

        // Delete tab history
        await tx.nPDProductTabHistory.deleteMany({
          where: { npdProductTabId: tabId },
        });

        // Delete activity logs related to this tab
        await tx.nPDProductActivityLog.deleteMany({
          where: {
            npdProductTabId: tabId,
          },
        });

        // Delete the tab
        await tx.nPDProductTab.delete({
          where: { id: tabId },
        });

        deletedCount++;
      });
    }

    return {
      success: true,
      message: `Successfully permanently deleted ${deletedCount} tabs`,
      deletedCount,
    };
  } catch (error) {
    console.error("Error bulk deleting tabs:", error);
    throw error;
  }
}
