"use client";

import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";

const THEME_COOKIE = "active_theme";
const DEFAULT_THEME = "blue";

export interface ThemeConfig {
  activeTheme: string;
  setActiveTheme: (theme: string) => void;
}

type ThemeContextType = ThemeConfig;

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

function setCookie(name: string, value: string) {
  if (typeof window === "undefined") return;

  document.cookie = `${name}=${value}; path=/; max-age=31536000; SameSite=Lax; ${
    window.location.protocol === "https:" ? "Secure;" : ""
  }`;
}

export function ActiveThemeProvider({
  children,
  initialTheme,
}: {
  children: ReactNode;
  initialTheme?: string;
}) {
  const [activeTheme, setActiveTheme] = useState<string>(
    () => initialTheme || DEFAULT_THEME
  );

  useEffect(() => {
    setCookie(THEME_COOKIE, activeTheme);

    // Remove all existing theme classes
    Array.from(document.body.classList)
      .filter((className) => className.startsWith("theme-"))
      .forEach((className) => {
        document.body.classList.remove(className);
      });

    // Split the theme string into base theme and modifiers
    const [baseTheme, ...modifiers] = activeTheme.split(" ");

    // Add base theme class
    document.body.classList.add(`theme-${baseTheme}`);

    // Add modifier classes
    if (modifiers.includes("compact")) {
      document.body.classList.add("theme-compact");
    }
  }, [activeTheme]);

  return (
    <ThemeContext.Provider value={{ activeTheme, setActiveTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeConfig() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error(
      "useThemeConfig must be used within an ActiveThemeProvider"
    );
  }
  return context;
}
