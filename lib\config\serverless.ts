/**
 * Serverless-specific configuration for Vercel deployment
 * Optimized for Neon database and serverless functions
 */

import { isDevelopment } from "@/lib/constants";

/**
 * Serverless environment detection
 */
export const isServerless = Boolean(
  process.env.VERCEL === "1" || process.env.AWS_LAMBDA_FUNCTION_NAME
);
export const isVercel = process.env.VERCEL === "1";
export const isProduction = process.env.NODE_ENV === "production";

/**
 * Serverless-optimized database configuration
 */
export const SERVERLESS_DB_CONFIG = {
  // Connection limits for serverless
  MAX_CONNECTIONS: 1, // Each function instance gets 1 connection
  CONNECTION_TIMEOUT: 5000, // 5 seconds max for connection
  QUERY_TIMEOUT: 10000, // 10 seconds max for queries

  // Neon-specific optimizations
  NEON_POOLING: true, // Use Neon's connection pooling
  NEON_REGION: process.env.NEON_REGION || "us-east-1",

  // Retry configuration for serverless
  MAX_RETRIES: 2, // Reduced retries for faster failure
  RETRY_DELAY: 100, // Quick retry delay
} as const;

/**
 * Serverless-optimized caching configuration
 */
export const SERVERLESS_CACHE_CONFIG = {
  // Request-scoped cache (within single function invocation)
  REQUEST_CACHE_ENABLED: true,
  REQUEST_CACHE_MAX_SIZE: 100, // Limit memory usage

  // Edge caching (Vercel Edge Cache)
  EDGE_CACHE_TTL: 60, // 1 minute edge cache
  STALE_WHILE_REVALIDATE: 300, // 5 minutes stale-while-revalidate

  // API response caching
  API_CACHE_HEADERS: {
    "Cache-Control": "s-maxage=60, stale-while-revalidate=300",
    "CDN-Cache-Control": "max-age=60",
    "Vercel-CDN-Cache-Control": "max-age=3600",
  },
} as const;

/**
 * Serverless-optimized performance configuration
 */
export const SERVERLESS_PERFORMANCE_CONFIG = {
  // Function timeout limits
  MAX_FUNCTION_DURATION: 10000, // 10 seconds max

  // Memory optimization
  MEMORY_LIMIT_MB: 1024, // 1GB memory limit

  // Cold start optimization
  KEEP_WARM: isProduction, // Keep functions warm in production
  WARM_UP_INTERVAL: 300000, // 5 minutes

  // Batch processing limits
  MAX_BATCH_SIZE: 10, // Smaller batches for serverless
  BATCH_TIMEOUT: 50, // Quick batch processing

  // Polling optimization for serverless
  MIN_POLLING_INTERVAL: 60000, // Minimum 1 minute polling
  MAX_POLLING_INTERVAL: 600000, // Maximum 10 minutes polling
} as const;

/**
 * Serverless-optimized logging configuration
 */
export const SERVERLESS_LOGGING_CONFIG = {
  // Structured logging for Vercel
  STRUCTURED_LOGS: isVercel,

  // Log levels
  LOG_LEVEL: isDevelopment ? "debug" : "info",

  // Performance logging
  LOG_PERFORMANCE: true,
  LOG_SLOW_QUERIES: true,
  SLOW_QUERY_THRESHOLD: 1000, // 1 second

  // Error tracking
  LOG_ERRORS: true,
  LOG_STACK_TRACES: isDevelopment,
} as const;

/**
 * Get serverless-optimized database URL
 */
export function getServerlessDbUrl(): string {
  const baseUrl = process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error("DATABASE_URL environment variable is required");
  }

  // Add serverless-specific connection parameters for Neon
  const url = new URL(baseUrl);

  // Optimize for serverless
  url.searchParams.set("connection_limit", "1");
  url.searchParams.set("pool_timeout", "5");
  url.searchParams.set("connect_timeout", "5");

  // Enable Neon's serverless features
  if (url.hostname.includes("neon.tech")) {
    url.searchParams.set("sslmode", "require");
    url.searchParams.set("application_name", "quickt-serverless");
  }

  return url.toString();
}

/**
 * Get optimal cache headers for serverless responses
 */
export function getServerlessCacheHeaders(
  ttl: number = 60
): Record<string, string> {
  return {
    "Cache-Control": `s-maxage=${ttl}, stale-while-revalidate=${ttl * 5}`,
    "CDN-Cache-Control": `max-age=${ttl}`,
    "Vercel-CDN-Cache-Control": `max-age=${ttl * 60}`,
  };
}

/**
 * Check if current environment is serverless
 */
export function isServerlessEnvironment(): boolean {
  return isServerless;
}

/**
 * Get serverless-optimized timeout for operations
 */
export function getServerlessTimeout(
  operation: "query" | "function" | "batch"
): number {
  switch (operation) {
    case "query":
      return SERVERLESS_DB_CONFIG.QUERY_TIMEOUT;
    case "function":
      return SERVERLESS_PERFORMANCE_CONFIG.MAX_FUNCTION_DURATION;
    case "batch":
      return SERVERLESS_PERFORMANCE_CONFIG.BATCH_TIMEOUT;
    default:
      return 5000;
  }
}

/**
 * Log performance metrics for serverless functions
 */
export function logServerlessMetrics(
  functionName: string,
  duration: number,
  memoryUsed?: number
): void {
  if (!SERVERLESS_LOGGING_CONFIG.LOG_PERFORMANCE) {
    return;
  }

  const metrics = {
    function: functionName,
    duration: `${duration.toFixed(2)}ms`,
    memory: memoryUsed ? `${memoryUsed}MB` : "unknown",
    timestamp: new Date().toISOString(),
    environment: isProduction ? "production" : "development",
  };

  if (SERVERLESS_LOGGING_CONFIG.STRUCTURED_LOGS) {
    console.log(JSON.stringify({ type: "performance", ...metrics }));
  } else {
    console.log(`📊 [${functionName}] ${duration.toFixed(2)}ms`);
  }
}

/**
 * Serverless-optimized error handling
 */
export function handleServerlessError(
  error: Error,
  context: string
): { message: string; statusCode: number } {
  if (SERVERLESS_LOGGING_CONFIG.LOG_ERRORS) {
    const errorLog = {
      type: "error",
      context,
      message: error.message,
      stack: SERVERLESS_LOGGING_CONFIG.LOG_STACK_TRACES
        ? error.stack
        : undefined,
      timestamp: new Date().toISOString(),
    };

    if (SERVERLESS_LOGGING_CONFIG.STRUCTURED_LOGS) {
      console.error(JSON.stringify(errorLog));
    } else {
      console.error(`❌ [${context}] ${error.message}`);
    }
  }

  // Return user-friendly error response
  return {
    message: isDevelopment ? error.message : "Internal server error",
    statusCode: 500,
  };
}
