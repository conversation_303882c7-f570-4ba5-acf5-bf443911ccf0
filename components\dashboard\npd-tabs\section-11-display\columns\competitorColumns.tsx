import type { Row, ColumnDef, CellContext } from "@tanstack/react-table";
import { ImageCell } from "../components/ImageCell";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Eye, EyeOff } from "lucide-react";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { defaultColumnWidths } from "../utils/columnSettings";
import {
  EditableCell,
  RatingEditableCell,
  NumberEditableCell,
  CurrencyEditableCell,
  WeightEditableCell,
  CountryEditableCell,
  FulfillmentEditableCell,
  DateEditableCell,
  UrlEditableCell,
  ImageUrlEditableCell,
} from "../components/FormattedEditableCells";

// Types
interface Competitor {
  productDetails: string;
  asin: string;
  url: string;
  imageUrl: string;
  brand: string;
  price: string;
  asinSales: string;
  asinRevenue: string;
  bsr: string;
  sellerCountry: string;
  fees: string;
  ratings: string;
  reviewCount: string;
  fulfillment: string;
  dimensions: string;
  weight: string;
  creationDate: string;
  isVisible?: boolean; // New field for visibility toggle
}

interface Section11Data {
  features: string;
  color: string;
  size: string;
  pricerange: string;
  competitors: Competitor[];
}

export type TableRow = Competitor & { index: number; [key: string]: unknown };

// Utility functions
export const stringSort = (
  rowA: Row<TableRow>,
  rowB: Row<TableRow>,
  id: string
): number => {
  const a = (rowA.original[id] || "").toString().toLowerCase();
  const b = (rowB.original[id] || "").toString().toLowerCase();
  if (a < b) return -1;
  if (a > b) return 1;
  return 0;
};

// Delete confirmation component
const DeleteConfirmation = React.memo(
  ({
    onDelete,
    competitorIndex,
    productDetails,
  }: {
    onDelete: (index: number) => void;
    competitorIndex: number;
    productDetails: string;
  }) => {
    const [showConfirmation, setShowConfirmation] = React.useState(false);

    return (
      <>
        <Button
          onClick={() => setShowConfirmation(true)}
          variant="ghost"
          size="sm"
          className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto w-auto"
          title="Delete this competitor"
          type="button"
        >
          <Trash2 className="h-4 w-4" />
        </Button>

        <ConfirmationDialog
          open={showConfirmation}
          onOpenChange={setShowConfirmation}
          title="Delete Competitor"
          description={`Are you sure you want to delete this competitor${
            productDetails
              ? ` "${productDetails.slice(0, 50)}${
                  productDetails.length > 50 ? "..." : ""
                }"`
              : ""
          }? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          onConfirm={() => {
            onDelete(competitorIndex);
            setShowConfirmation(false);
          }}
          onCancel={() => setShowConfirmation(false)}
          variant="destructive"
        />
      </>
    );
  }
);

DeleteConfirmation.displayName = "DeleteConfirmation";

// Column definitions function
export const createCompetitorColumns = (
  data: Section11Data,
  handleCopy: (text: string, cellId: string) => Promise<void>,
  copiedCell: string | null,
  columnWidths?: Record<string, number>,
  isEditing?: boolean,
  onCompetitorChange?: (
    rowIndex: number,
    field: keyof Competitor,
    value: string
  ) => void,
  onDeleteCompetitor?: (rowIndex: number) => void,
  onToggleVisibility?: (rowIndex: number) => void,
  isFieldModified?: (rowIndex: number, fieldName: string) => boolean
): ColumnDef<TableRow>[] => {
  // Use provided widths or fallback to defaults
  const widths = columnWidths || defaultColumnWidths;

  // Helper function to check if a field is modified
  const checkFieldModified = (rowIndex: number, fieldName: string): boolean => {
    return isFieldModified?.(rowIndex, fieldName) ?? false;
  };

  return [
    // Visibility toggle column (only visible in non-edit mode)
    ...(!isEditing
      ? [
          {
            id: "visibility",
            header: () => (
              <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
                👁️
              </div>
            ),
            size: 40,
            enableResizing: false,
            enableSorting: false,
            cell: (context: CellContext<TableRow, unknown>) => {
              const isVisible = context.row.original.isVisible !== false; // Default to visible
              return (
                <div className="flex items-center justify-center px-1">
                  <Button
                    onClick={() => onToggleVisibility?.(context.row.index)}
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto w-auto hover:bg-gray-100"
                    title={isVisible ? "Hide competitor" : "Show competitor"}
                    type="button"
                  >
                    {isVisible ? (
                      <Eye className="h-4 w-4 text-gray-600" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              );
            },
          },
        ]
      : []),

    // Product details column
    {
      accessorKey: "productDetails",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px] h-full">
          Product Title
        </div>
      ),
      size: widths.productDetails,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "productDetails"),
      cell: (context) => {
        const productDetails = String(context.getValue() || "");
        const cellId = `productDetails-${context.row.index}`;
        return (
          <div className="px-1 py-0.5">
            {isEditing ? (
              <Input
                value={productDetails}
                onChange={(e) =>
                  onCompetitorChange?.(
                    context.row.index,
                    "productDetails",
                    e.target.value
                  )
                }
                className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                  checkFieldModified(context.row.index, "productDetails")
                    ? "focus:border-orange-500 ring-2 ring-orange-200"
                    : "focus:border-blue-300"
                }`}
                placeholder="Product title..."
              />
            ) : (
              <div
                className="px-1 py-0.5 text-left cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-6 flex items-center overflow-hidden"
                onClick={() => handleCopy(productDetails, cellId)}
                title={productDetails}
                style={{ fontSize: "12px" }}
              >
                <div className="relative truncate w-full">
                  {productDetails || "-"}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );
      },
    },

    // Brand column
    {
      accessorKey: "brand",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Brand
        </div>
      ),
      size: widths.brand,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "brand"),
      cell: (context) => {
        const brand = String(context.getValue() || "");
        const cellId = `brand-${context.row.index}`;
        return (
          <EditableCell
            value={brand}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "brand", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "brand")}
            className="text-center"
          />
        );
      },
    },

    // ASIN column
    {
      accessorKey: "asin",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          ASIN
        </div>
      ),
      size: widths.asin,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "asin"),
      cell: (context) => {
        const asin = String(context.getValue() || "");
        const cellId = `asin-${context.row.index}`;
        return (
          <EditableCell
            value={asin}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "asin", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "asin")}
            className="text-center"
          />
        );
      },
    },

    // URL column
    {
      accessorKey: "url",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Link
        </div>
      ),
      size: widths.url,
      enableSorting: false,
      cell: (context) => {
        const url = String(context.getValue() || "");
        const cellId = `url-${context.row.index}`;
        return (
          <UrlEditableCell
            value={url}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "url", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "url")}
            className="text-center"
          />
        );
      },
    },

    // Image column
    {
      accessorKey: "imageUrl",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Image
        </div>
      ),
      size: widths.imageUrl,
      enableSorting: false,
      cell: (context) => {
        const imageUrl = String(context.getValue() || "");
        const cellId = `imageUrl-${context.row.index}`;

        if (isEditing) {
          return (
            <ImageUrlEditableCell
              value={imageUrl}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "imageUrl", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "imageUrl")}
              className="text-center"
            />
          );
        }

        return (
          <ImageCell
            imageUrl={imageUrl}
            onCopy={handleCopy}
            cellId={cellId}
            copiedCell={copiedCell}
          />
        );
      },
    },

    // Annual Sales column (calculated: monthly sales × 12)
    {
      accessorKey: "annualSales",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Annual Sales
        </div>
      ),
      size: widths.annualSales,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getAnnualSales = (row: Row<TableRow>): number => {
          const monthlySales = row.original.asinSales as string;
          if (!monthlySales || monthlySales === "-") return 0;
          const cleaned = monthlySales.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed * 12;
        };
        return getAnnualSales(rowA) - getAnnualSales(rowB);
      },
      cell: (context) => {
        const monthlySales = String(context.row.original.asinSales || "");
        const cellId = `annualSales-${context.row.index}`;

        // Calculate annual sales (monthly × 12)
        const monthlyValue = parseFloat(
          monthlySales.replace(/[$,]/g, "") || "0"
        );
        const annualValue = monthlyValue * 12;
        const annualSalesFormatted =
          annualValue > 0
            ? `${annualValue.toLocaleString(undefined, {
                maximumFractionDigits: 0,
              })}`
            : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded text-primary border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-6 flex items-center justify-end overflow-hidden"
            onClick={() => handleCopy(annualSalesFormatted, cellId)}
            title={`Annual: ${annualSalesFormatted} (Monthly: ${
              monthlySales || "-"
            })`}
            style={{ fontSize: "12px" }}
          >
            <div className="relative truncate w-full">
              {annualSalesFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Annual Revenue column (calculated: monthly revenue × 12)
    {
      accessorKey: "annualRevenue",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Annual Revenue
        </div>
      ),
      size: widths.annualRevenue,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getAnnualRevenue = (row: Row<TableRow>): number => {
          const monthlyRevenue = row.original.asinRevenue as string;
          if (!monthlyRevenue || monthlyRevenue === "-") return 0;
          const cleaned = monthlyRevenue.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed * 12;
        };
        return getAnnualRevenue(rowA) - getAnnualRevenue(rowB);
      },
      cell: (context) => {
        const monthlyRevenue = String(context.row.original.asinRevenue || "");
        const cellId = `annualRevenue-${context.row.index}`;

        // Calculate annual revenue (monthly × 12)
        const monthlyValue = parseFloat(
          monthlyRevenue.replace(/[$,]/g, "") || "0"
        );
        const annualValue = monthlyValue * 12;
        const annualRevenueFormatted =
          annualValue > 0
            ? `$${annualValue.toLocaleString(undefined, {
                maximumFractionDigits: 0,
              })}`
            : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all text-primary font-medium h-6 flex items-center justify-end overflow-hidden"
            onClick={() => handleCopy(annualRevenueFormatted, cellId)}
            title={annualRevenueFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative truncate w-full">
              {annualRevenueFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Market Share column (calculated: annual sales / total annual sales)
    {
      accessorKey: "marketShare",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Market Share
        </div>
      ),
      size: widths.marketShare,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getMarketShare = (row: Row<TableRow>): number => {
          const monthlyRevenue = String(row.original.asinRevenue || "").replace(
            /[$,]/g,
            ""
          );
          const annualRevenue = parseFloat(monthlyRevenue) * 12;
          if (isNaN(annualRevenue) || annualRevenue <= 0) return 0;

          // Calculate total annual revenue across all competitors
          const totalAnnualRevenue = data.competitors.reduce(
            (sum, competitor) => {
              const revenue =
                parseFloat(
                  String(competitor.asinRevenue || "").replace(/[$,]/g, "") ||
                    "0"
                ) * 12;
              return sum + (isNaN(revenue) ? 0 : revenue);
            },
            0
          );

          return totalAnnualRevenue > 0
            ? (annualRevenue / totalAnnualRevenue) * 100
            : 0;
        };
        return getMarketShare(rowA) - getMarketShare(rowB);
      },
      cell: (context) => {
        const monthlyRevenue = String(context.row.original.asinRevenue || "");
        const cellId = `marketShare-${context.row.index}`;

        // Calculate market share based on revenue
        const monthlyValue = parseFloat(
          monthlyRevenue.replace(/[$,]/g, "") || "0"
        );
        const annualValue = monthlyValue * 12;

        // Calculate total annual revenue across all competitors
        const totalAnnualRevenue = data.competitors.reduce(
          (sum, competitor) => {
            const revenue =
              parseFloat(
                String(competitor.asinRevenue || "").replace(/[$,]/g, "") || "0"
              ) * 12;
            return sum + (isNaN(revenue) ? 0 : revenue);
          },
          0
        );

        const marketSharePercent =
          totalAnnualRevenue > 0 && annualValue > 0
            ? (annualValue / totalAnnualRevenue) * 100
            : 0;

        const marketShareFormatted =
          marketSharePercent > 0 ? `${marketSharePercent.toFixed(1)}%` : "-";

        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(marketShareFormatted, cellId)}
            title={`Market share: ${marketShareFormatted} of total annual sales`}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {marketShareFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Sales to Review Ratio column (calculated: asinSales / reviewCount)
    {
      accessorKey: "salesToReviewRatio",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Sales/Review Ratio
        </div>
      ),
      size: widths.salesToReviewRatio,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getRatio = (row: Row<TableRow>): number => {
          const sales = parseFloat(
            String(row.original.asinSales || "").replace(/[^\d.]/g, "")
          );
          const reviews = parseFloat(
            String(row.original.reviewCount || "").replace(/[^\d.]/g, "")
          );
          return !isNaN(sales) && !isNaN(reviews) && reviews > 0
            ? sales / reviews
            : 0;
        };
        return getRatio(rowA) - getRatio(rowB);
      },
      cell: (context) => {
        const sales = String(context.row.original.asinSales || "");
        const reviews = String(context.row.original.reviewCount || "");
        const cellId = `salesToReviewRatio-${context.row.index}`;

        // Calculate ratio
        const salesValue = parseFloat(sales.replace(/[^\d.]/g, "") || "0");
        const reviewsValue = parseFloat(reviews.replace(/[^\d.]/g, "") || "0");
        const ratio = reviewsValue > 0 ? salesValue / reviewsValue : 0;
        const ratioFormatted = ratio > 0 ? ratio.toFixed(1) : "-";

        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(ratioFormatted, cellId)}
            title={`${ratioFormatted} sales per review (${
              sales || "0"
            } sales / ${reviews || "0"} reviews)`}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {ratioFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Ratings column
    {
      accessorKey: "ratings",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Ratings
        </div>
      ),
      size: widths.ratings,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          const parsed = parseFloat(value);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "ratings") - getValue(rowB, "ratings");
      },
      cell: (context) => {
        const ratings = String(context.getValue() || "");
        const cellId = `ratings-${context.row.index}`;

        if (isEditing) {
          return (
            <RatingEditableCell
              value={ratings}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "ratings", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "ratings")}
              className="text-center"
            />
          );
        }

        // Format ratings to always show 1 decimal place
        const ratingsValue = parseFloat(ratings || "0");
        const ratingsFormatted =
          ratings && ratings !== "-" && !isNaN(ratingsValue)
            ? `${ratingsValue.toFixed(1)} ⭐`
            : "-";
        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(ratingsFormatted, cellId)}
            title={ratingsFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {ratingsFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Review Count column
    {
      accessorKey: "reviewCount",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Review Count
        </div>
      ),
      size: widths.reviewCount,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Remove commas and parse
          const cleaned = value.replace(/,/g, "");
          const parsed = parseInt(cleaned, 10);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "reviewCount") - getValue(rowB, "reviewCount");
      },
      cell: (context) => {
        const reviewCount = String(context.getValue() || "");
        const cellId = `reviewCount-${context.row.index}`;

        if (isEditing) {
          return (
            <NumberEditableCell
              value={reviewCount}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "reviewCount", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "reviewCount")}
              className="text-right"
            />
          );
        }

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(reviewCount, cellId)}
            title={reviewCount || "-"}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {reviewCount || "-"}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Price column
    {
      accessorKey: "price",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Price
        </div>
      ),
      size: widths.price,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Remove $ sign and commas, then parse
          const cleaned = value.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "price") - getValue(rowB, "price");
      },
      cell: (context) => {
        const price = String(context.getValue() || "");
        const cellId = `price-${context.row.index}`;

        if (isEditing) {
          return (
            <CurrencyEditableCell
              value={price}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "price", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "price")}
              className="text-right"
            />
          );
        }

        // Parse and format price for display
        const priceValue = parseFloat(price?.replace(/[$,]/g, "") || "0");
        const priceFormatted =
          priceValue > 0 ? `$${priceValue.toFixed(2)}` : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded text-green-600 border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(priceFormatted, cellId)}
            title={priceFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {priceFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // ASIN Sales column
    {
      accessorKey: "asinSales",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          ASIN Sales
        </div>
      ),
      size: widths.asinSales,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Remove $ sign and commas, then parse
          const cleaned = value.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "asinSales") - getValue(rowB, "asinSales");
      },
      cell: (context) => {
        const asinSales = String(context.getValue() || "");
        const cellId = `asinSales-${context.row.index}`;

        if (isEditing) {
          return (
            <NumberEditableCell
              value={asinSales}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "asinSales", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "asinSales")}
              className="text-right"
            />
          );
        }

        // Parse and format ASIN sales
        const salesValue = parseFloat(asinSales?.replace(/[$,]/g, "") || "0");
        const asinSalesFormatted =
          salesValue > 0 ? `$${salesValue.toLocaleString()}` : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(asinSalesFormatted, cellId)}
            title={asinSalesFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {asinSalesFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // ASIN Revenue column
    {
      accessorKey: "asinRevenue",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          ASIN Revenue
        </div>
      ),
      size: widths.asinRevenue,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Remove $ sign and commas, then parse
          const cleaned = value.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "asinRevenue") - getValue(rowB, "asinRevenue");
      },
      cell: (context) => {
        const asinRevenue = String(context.getValue() || "");
        const cellId = `asinRevenue-${context.row.index}`;

        if (isEditing) {
          return (
            <NumberEditableCell
              value={asinRevenue}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "asinRevenue", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "asinRevenue")}
              className="text-right"
            />
          );
        }

        // Parse and format ASIN revenue
        const revenueValue = parseFloat(
          asinRevenue?.replace(/[$,]/g, "") || "0"
        );
        const asinRevenueFormatted =
          revenueValue > 0 ? `$${revenueValue.toLocaleString()}` : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(asinRevenueFormatted, cellId)}
            title={asinRevenueFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {asinRevenueFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // BSR column
    {
      accessorKey: "bsr",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          BSR
        </div>
      ),
      size: widths.bsr,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return Infinity; // Put empty values at the end
          // Remove commas and parse
          const cleaned = value.replace(/,/g, "");
          const parsed = parseInt(cleaned, 10);
          return isNaN(parsed) ? Infinity : parsed;
        };
        return getValue(rowA, "bsr") - getValue(rowB, "bsr");
      },
      cell: (context) => {
        const bsr = String(context.getValue() || "");
        const cellId = `bsr-${context.row.index}`;

        return (
          <NumberEditableCell
            value={bsr}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "bsr", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing || false}
            isModified={checkFieldModified(context.row.index, "bsr")}
            className="text-right"
          />
        );
      },
    },

    // Fees column
    {
      accessorKey: "fees",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Fees
        </div>
      ),
      size: widths.fees,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Remove $ sign and commas, then parse
          const cleaned = value.replace(/[$,]/g, "");
          const parsed = parseFloat(cleaned);
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "fees") - getValue(rowB, "fees");
      },
      cell: (context) => {
        const fees = String(context.getValue() || "");
        const cellId = `fees-${context.row.index}`;

        if (isEditing) {
          return (
            <CurrencyEditableCell
              value={fees}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "fees", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "fees")}
              className="text-right"
            />
          );
        }

        // Parse and format fees
        const feesValue = parseFloat(fees?.replace(/[$,]/g, "") || "0");
        const feesFormatted = feesValue > 0 ? `$${feesValue.toFixed(2)}` : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded text-red-400 border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(feesFormatted, cellId)}
            title={feesFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {feesFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Gross Margin column (calculated: (price - fees) / price)
    {
      accessorKey: "grossMargin",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Gross Margin
        </div>
      ),
      size: widths.grossMargin,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getGrossMargin = (row: Row<TableRow>): number => {
          const price = parseFloat(
            String(row.original.price || "").replace(/[$,]/g, "") || "0"
          );
          const fees = parseFloat(
            String(row.original.fees || "").replace(/[$,]/g, "") || "0"
          );
          return price > 0 ? ((price - fees) / price) * 100 : 0;
        };
        return getGrossMargin(rowA) - getGrossMargin(rowB);
      },
      cell: (context) => {
        const price = String(context.row.original.price || "");
        const fees = String(context.row.original.fees || "");
        const cellId = `grossMargin-${context.row.index}`;

        // Calculate gross margin: (price - fees) / price
        const priceValue = parseFloat(price.replace(/[$,]/g, "") || "0");
        const feesValue = parseFloat(fees.replace(/[$,]/g, "") || "0");
        const grossMarginPercent =
          priceValue > 0 ? ((priceValue - feesValue) / priceValue) * 100 : 0;

        const grossMarginFormatted =
          grossMarginPercent > 0 ? `${grossMarginPercent.toFixed(1)}%` : "-";

        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all text-primary font-medium h-6 flex items-center justify-center overflow-hidden"
            onClick={() => handleCopy(grossMarginFormatted, cellId)}
            title={`Gross margin: ${grossMarginFormatted} ((${price || "0"} - ${
              fees || "0"
            }) / ${price || "0"})`}
            style={{ fontSize: "12px" }}
          >
            <div className="relative truncate w-full">
              {grossMarginFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Seller Country column
    {
      accessorKey: "sellerCountry",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Seller Country
        </div>
      ),
      size: widths.sellerCountry,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "sellerCountry"),
      cell: (context) => {
        const sellerCountry = String(context.getValue() || "");
        const cellId = `sellerCountry-${context.row.index}`;
        return (
          <CountryEditableCell
            value={sellerCountry}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "sellerCountry", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "sellerCountry")}
            className="text-center"
          />
        );
      },
    },

    // Fulfillment column
    {
      accessorKey: "fulfillment",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Fulfillment
        </div>
      ),
      size: widths.fulfillment,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "fulfillment"),
      cell: (context) => {
        const fulfillment = String(context.getValue() || "");
        const cellId = `fulfillment-${context.row.index}`;
        return (
          <FulfillmentEditableCell
            value={fulfillment}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "fulfillment", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "fulfillment")}
            className="text-center"
          />
        );
      },
    },

    // Creation Date column
    {
      accessorKey: "creationDate",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Creation Date
        </div>
      ),
      size: widths.creationDate,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getCreationDate = (row: Row<TableRow>): number => {
          const creationDateStr = String(row.original.creationDate || "");
          if (!creationDateStr || creationDateStr === "-") return 0;
          const creationDate = new Date(creationDateStr);
          return isNaN(creationDate.getTime()) ? 0 : creationDate.getTime();
        };
        return getCreationDate(rowA) - getCreationDate(rowB);
      },
      cell: (context) => {
        const creationDate = String(context.getValue() || "");
        const cellId = `creationDate-${context.row.index}`;

        if (isEditing) {
          return (
            <DateEditableCell
              value={creationDate}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "creationDate", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "creationDate")}
              className="text-center"
            />
          );
        }

        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(creationDate, cellId)}
            title={creationDate || "-"}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {creationDate || "-"}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Listing Age column (calculated: today's date - creation date)
    {
      accessorKey: "listingAge",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Listing Age
        </div>
      ),
      size: widths.listingAge,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getListingAge = (row: Row<TableRow>): number => {
          const creationDateStr = String(row.original.creationDate || "");
          if (!creationDateStr || creationDateStr === "-") return 0;
          const creationDate = new Date(creationDateStr);
          if (isNaN(creationDate.getTime())) return 0;
          const today = new Date();
          return today.getTime() - creationDate.getTime();
        };
        return getListingAge(rowA) - getListingAge(rowB);
      },
      cell: (context) => {
        const creationDate = String(context.row.original.creationDate || "");
        const cellId = `listingAge-${context.row.index}`;

        // Calculate listing age in days
        let ageInDays = 0;
        let ageFormatted = "-";

        if (creationDate && creationDate !== "-") {
          const creation = new Date(creationDate);
          if (!isNaN(creation.getTime())) {
            const today = new Date();
            const diffTime = today.getTime() - creation.getTime();
            ageInDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            if (ageInDays >= 365) {
              const years = Math.floor(ageInDays / 365);
              const remainingMonths = Math.floor((ageInDays % 365) / 30);
              if (remainingMonths > 0) {
                ageFormatted =
                  years === 1
                    ? `1y ${remainingMonths}m`
                    : `${years}y ${remainingMonths}m`;
              } else {
                ageFormatted = years === 1 ? `1y` : `${years}y`;
              }
            } else if (ageInDays >= 30) {
              const months = Math.floor(ageInDays / 30);
              ageFormatted = months === 1 ? `1m` : `${months}m`;
            } else {
              ageFormatted = `<1m`; // Less than 1 month
            }
          }
        }

        return (
          <div
            className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(ageFormatted, cellId)}
            title={`Listing age: ${ageFormatted} (${ageInDays} days since ${
              creationDate || "unknown"
            })`}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {ageFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Dimensions column
    {
      accessorKey: "dimensions",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Dimensions
        </div>
      ),
      size: widths.dimensions,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) =>
        stringSort(rowA, rowB, "dimensions"),
      cell: (context) => {
        const dimensions = String(context.getValue() || "");
        const cellId = `dimensions-${context.row.index}`;
        return (
          <EditableCell
            value={dimensions}
            onChange={(value) =>
              onCompetitorChange?.(context.row.index, "dimensions", value)
            }
            cellId={cellId}
            copiedCell={copiedCell}
            onCopy={handleCopy}
            isEditing={isEditing ?? false}
            isModified={checkFieldModified(context.row.index, "dimensions")}
            className="max-w-[135px] truncate"
          />
        );
      },
    },

    // Weight column
    {
      accessorKey: "weight",
      header: () => (
        <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
          Weight
        </div>
      ),
      size: widths.weight,
      enableSorting: true,
      sortingFn: (rowA: Row<TableRow>, rowB: Row<TableRow>) => {
        const getValue = (row: Row<TableRow>, key: string): number => {
          const value = row.original[key] as string;
          if (!value || value === "-") return 0;
          // Parse weight value (remove units and parse)
          const parsed = parseFloat(value.replace(/[^\d.]/g, ""));
          return isNaN(parsed) ? 0 : parsed;
        };
        return getValue(rowA, "weight") - getValue(rowB, "weight");
      },
      cell: (context) => {
        const weight = String(context.getValue() || "");
        const cellId = `weight-${context.row.index}`;

        if (isEditing) {
          return (
            <WeightEditableCell
              value={weight}
              onChange={(value) =>
                onCompetitorChange?.(context.row.index, "weight", value)
              }
              cellId={cellId}
              copiedCell={copiedCell}
              onCopy={handleCopy}
              isEditing={true}
              isModified={checkFieldModified(context.row.index, "weight")}
              className="text-right"
            />
          );
        }

        // Parse and format weight for display
        const weightValue = parseFloat(weight?.replace(/[^\d.]/g, "") || "0");
        const weightFormatted = weightValue > 0 ? weightValue.toFixed(2) : "-";

        return (
          <div
            className="px-1 py-0.5 text-right cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all"
            onClick={() => handleCopy(weightFormatted, cellId)}
            title={weightFormatted}
            style={{ fontSize: "12px" }}
          >
            <div className="relative">
              {weightFormatted}
              {copiedCell === cellId && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                  Copied!
                </div>
              )}
            </div>
          </div>
        );
      },
    },

    // Actions column (only visible in edit mode)
    ...(isEditing
      ? [
          {
            id: "actions",
            header: () => (
              <div className="flex items-center justify-center text-center whitespace-normal break-keep leading-tight px-1 py-0.5 min-h-[32px]">
                Actions
              </div>
            ),
            size: 80,
            enableResizing: false,
            enableSorting: false,
            cell: (context: CellContext<TableRow, unknown>) => (
              <div className="flex items-center justify-center px-1">
                <DeleteConfirmation
                  onDelete={onDeleteCompetitor || (() => {})}
                  competitorIndex={context.row.index}
                  productDetails={context.row.original.productDetails}
                />
              </div>
            ),
          },
        ]
      : []),
  ];
};
