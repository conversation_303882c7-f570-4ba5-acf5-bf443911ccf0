"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { useNPDContext } from "@/context/npd-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  CalendarIcon,
  PackageIcon,
  TagIcon,
  TrendingUpIcon,
  Star,
  StarOff,
} from "lucide-react";
import Spinner from "@/components/spinner";
import { NPDSubscriptionToggle } from "@/components/dashboard/npd-subscription-toggle";
import { navigateToProductWithBrandFilter } from "@/lib/utils";
import { getProductStageBorderColor } from "@/lib/constants/product-stages";
import type { ProductSummary } from "@/types";

export function NPDsList() {
  const router = useRouter();
  const { allProducts, isLoadingProducts, error } = useNPDContext();

  if (isLoadingProducts) {
    return (
      <div className="h-full w-full overflow-y-auto">
        <div className="space-y-6 p-6 pb-8 w-full min-h-full flex items-center justify-center">
          <Spinner loading={true} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full w-full overflow-y-auto">
        <div className="space-y-6 p-6 pb-8 w-full min-h-full">
          <div className="text-center">
            <p className="text-red-500">Error loading products: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Group products by brand for better organization
  const productsByBrand = allProducts.reduce((acc, product) => {
    const brand = product.brand;
    if (!acc[brand]) {
      acc[brand] = [];
    }
    acc[brand].push(product);
    return acc;
  }, {} as Record<string, ProductSummary[]>);

  // Filter subscribed products
  const subscribedProducts = allProducts.filter(
    (product) => product.isSubscribed
  );

  const totalProducts = allProducts.length;
  const totalBrands = Object.keys(productsByBrand).length;

  return (
    <div className="h-full w-full overflow-y-auto">
      <div className="space-y-6 p-6 pb-8 w-full min-h-full">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              New Product Development
            </h1>
            <p className="text-muted-foreground">
              Manage and view all NPD products
            </p>
          </div>
          <Button
            onClick={() => router.push("/dashboard/npd/new")}
            className="cursor-pointer"
          >
            <PackageIcon className="mr-2 h-4 w-4" />
            Add New Product
          </Button>
        </div>

        <Separator />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total NPD Products
              </CardTitle>
              <PackageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalProducts}</div>
              <p className="text-xs text-muted-foreground">
                Across {totalBrands} {totalBrands === 1 ? "brand" : "brands"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                My Subscriptions
              </CardTitle>
              <Star className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {subscribedProducts.length}
              </div>
              <p className="text-xs text-muted-foreground">
                NPD products you follow
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Brands</CardTitle>
              <TagIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalBrands}</div>
              <p className="text-xs text-muted-foreground">
                Unique brand{totalBrands === 1 ? "" : "s"} in catalog
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth</CardTitle>
              <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                +{Math.floor(totalProducts * 0.12)}
              </div>
              <p className="text-xs text-muted-foreground">
                NPD products added this month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* My Subscriptions Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500 fill-current" />
              My Subscriptions
            </CardTitle>
            <CardDescription>
              NPD products you&apos;re subscribed to (
              {subscribedProducts.length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            {subscribedProducts.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <StarOff className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No subscriptions yet
                </h3>
                <p className="text-muted-foreground text-center mb-4">
                  Subscribe to NPD projects to see them here. Use the star icon
                  on any NPD project to subscribe.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {subscribedProducts.map((product) => (
                  <Card
                    key={product.id}
                    className={`hover:shadow-md transition-shadow cursor-pointer border-l-4 ${getProductStageBorderColor(
                      product.stage
                    )}`}
                    onClick={() =>
                      navigateToProductWithBrandFilter(
                        router,
                        product.slug,
                        product.brand
                      )
                    }
                  >
                    <CardHeader className="px-4">
                      <div className="flex items-start justify-between gap-3">
                        {/* Product image */}
                        <div className="flex-shrink-0">
                          {product.imageUrl ? (
                            <Image
                              src={product.imageUrl}
                              alt={`${product.name} image`}
                              width={40}
                              height={40}
                              className="w-10 h-10 rounded-md object-cover border border-border/30"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-md bg-muted/50 border border-border/30 flex items-center justify-center">
                              <PackageIcon className="h-5 w-5 text-muted-foreground" />
                            </div>
                          )}
                        </div>

                        {/* Product info */}
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-sm font-medium line-clamp-1 mb-1">
                            {product.name}
                          </CardTitle>
                          <CardDescription className="text-xs">
                            {product.brand}
                          </CardDescription>
                        </div>

                        {/* Subscription toggle */}
                        <NPDSubscriptionToggle
                          productId={product.id}
                          productName={product.name}
                          isSubscribed={true}
                          size="sm"
                          variant="ghost"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="px-4">
                      {/* Product description */}
                      {product.description && (
                        <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                          {product.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <Badge
                          variant="outline"
                          className="text-xs px-2 py-0.5"
                        >
                          {product.stage}
                        </Badge>
                        <p className="text-xs text-muted-foreground">
                          {new Date(product.updatedAt).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "short",
                              day: "2-digit",
                            }
                          )}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* NPD Summary */}
        {totalProducts === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <PackageIcon className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No NPD products found
              </h3>
              <p className="text-muted-foreground text-center mb-4">
                Get started by adding your first NPD project to the catalog.
              </p>
              <Button>
                <PackageIcon className="mr-2 h-4 w-4" />
                Add Your First NPD Project
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Brand Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TagIcon className="h-5 w-5" />
                  Brand Overview
                </CardTitle>
                <CardDescription>
                  Distribution of NPD products across your brands
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(productsByBrand)
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([brand, brandProducts]) => (
                      <div
                        key={brand}
                        className="flex items-center justify-between p-4 rounded-lg border bg-card hover:shadow-sm transition-shadow cursor-pointer"
                        onClick={() => {
                          // Set brand filter in localStorage
                          localStorage.setItem("npd_selected_brand", brand);

                          // Dispatch custom event for same-tab localStorage changes
                          window.dispatchEvent(
                            new CustomEvent("localStorageChange", {
                              detail: {
                                key: "npd_selected_brand",
                                newValue: brand,
                              },
                            })
                          );
                        }}
                      >
                        <div>
                          <h3 className="font-medium">{brand}</h3>
                          <p className="text-sm text-muted-foreground">
                            {brandProducts.length} product
                            {brandProducts.length === 1 ? "" : "s"}
                          </p>
                        </div>
                        <Badge
                          variant="secondary"
                          className="text-lg px-3 py-1"
                        >
                          {brandProducts.length}
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Latest NPD products added to the catalog
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {allProducts
                    .sort(
                      (a, b) =>
                        new Date(b.createdAt).getTime() -
                        new Date(a.createdAt).getTime()
                    )
                    .slice(0, 5)
                    .map((product) => (
                      <div
                        key={product.id}
                        className="flex items-start justify-between p-3 rounded-lg border bg-card hover:shadow-sm transition-shadow cursor-pointer"
                        onClick={() =>
                          navigateToProductWithBrandFilter(
                            router,
                            product.slug,
                            product.brand
                          )
                        }
                      >
                        <div className="flex items-start gap-3 flex-1">
                          {/* Product image */}
                          <div className="flex-shrink-0">
                            {product.imageUrl ? (
                              <Image
                                src={product.imageUrl}
                                alt={`${product.name} image`}
                                width={40}
                                height={40}
                                className="w-10 h-10 rounded-md object-cover border border-border/30"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-md bg-primary/10 border border-border/30 flex items-center justify-center">
                                <PackageIcon className="h-5 w-5 text-primary" />
                              </div>
                            )}
                          </div>

                          {/* Product info */}
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <p className="font-medium text-sm line-clamp-1">
                                {product.name}
                              </p>
                              {product.isSubscribed && (
                                <Star className="h-3 w-3 text-yellow-500 fill-current flex-shrink-0" />
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mb-1">
                              {product.brand}
                            </p>
                            {product.description && (
                              <p className="text-xs text-muted-foreground line-clamp-1">
                                {product.description}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Date and stage */}
                        <div className="text-right flex-shrink-0 ml-3">
                          <p className="text-xs text-muted-foreground mb-1">
                            {new Date(product.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "2-digit",
                              }
                            )}
                          </p>
                          <Badge variant="outline" className="text-xs">
                            {product.stage}
                          </Badge>
                        </div>
                      </div>
                    ))}
                </div>
                {allProducts.length > 5 && (
                  <div className="mt-4 text-center">
                    <p className="text-sm text-muted-foreground">
                      And {allProducts.length - 5} more products...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
