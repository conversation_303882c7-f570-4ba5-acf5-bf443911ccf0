"use client";

import React, { createContext, useContext } from "react";
import { AppProvider } from "@/context/app-context";
import { AuthProvider } from "@/context/auth-context";
import { NPDProvider } from "@/context/npd-context";
import { RoleUpdatesProvider } from "@/components/role-updates-provider";

// Context for sidebar default state
const SidebarStateContext = createContext<{ defaultOpen: boolean }>({
  defaultOpen: true,
});

export const useSidebarState = () => useContext(SidebarStateContext);

interface ProvidersProps {
  children: React.ReactNode;
  sidebarDefaultOpen?: boolean;
}

export function Providers({
  children,
  sidebarDefaultOpen = true,
}: ProvidersProps) {
  return (
    <AppProvider>
      <AuthProvider>
        <NPDProvider>
          <SidebarStateContext.Provider
            value={{ defaultOpen: sidebarDefaultOpen }}
          >
            <RoleUpdatesProvider />
            {children}
          </SidebarStateContext.Provider>
        </NPDProvider>
      </AuthProvider>
    </AppProvider>
  );
}
