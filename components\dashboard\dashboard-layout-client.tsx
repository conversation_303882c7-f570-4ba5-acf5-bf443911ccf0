"use client";

import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import DashboardHeader from "@/components/header/dashboard-header";
import Footer from "@/components/footer";
import { useSidebarState } from "@/context/providers";
import { NavigationGuardProvider } from "@/context/navigation-guard-context";

export function DashboardLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const { defaultOpen } = useSidebarState();

  return (
    <NavigationGuardProvider>
      <SidebarProvider
        defaultOpen={defaultOpen}
        style={
          {
            "--sidebar-width": "13rem",
            "--sidebar-width-mobile": "20rem",
          } as React.CSSProperties
        }
      >
        <div className="flex h-screen w-full">
          <AppSidebar />

          <SidebarInset className="flex flex-col flex-1 overflow-hidden">
            <DashboardHeader />

            <main className="flex-1 overflow-y-auto pl-3 pr-4 py-1">
              {children}
            </main>

            <Footer />
          </SidebarInset>
        </div>
      </SidebarProvider>
    </NavigationGuardProvider>
  );
}
