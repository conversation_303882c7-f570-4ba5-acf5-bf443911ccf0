"use client";

import { useCallback, useState, useTransition } from "react";
import { validateNPDSlug } from "@/actions/npd.actions";

export function useProductValidation() {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Validate product slug uniqueness
  const validateSlug = useCallback(
    async (
      slug: string,
      currentId?: string
    ): Promise<{ exists: boolean } | null> => {
      if (!slug) return null;

      setError(null);

      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            const result = await validateNPDSlug(slug, currentId);
            resolve(result);
          } catch (error) {
            const errorMessage =
              error instanceof Error
                ? error.message
                : "Failed to validate slug";
            setError(errorMessage);
            resolve(null);
          }
        });
      });
    },
    []
  );

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Actions
    validateSlug,

    // State
    isPending,
    error,
    clearError,
  };
}
