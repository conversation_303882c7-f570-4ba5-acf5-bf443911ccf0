/**
 * Centralized polling configuration for the application
 * All polling intervals and controls are managed from this file
 */

import { isDevelopment } from "./index";

/**
 * Serverless-optimized polling intervals in milliseconds
 * Minimized frequencies to reduce cold starts and function invocations
 */
export const POLLING_INTERVALS = {
  // Role updates polling - very infrequent for serverless
  ROLE_UPDATES: isDevelopment ? 120000 : 600000, // 2min in dev, 10min in prod

  // Activity logs refresh (disabled by default)
  ACTIVITY_LOGS: 900000, // 15 minutes

  // Product data refresh (disabled by default)
  PRODUCT_DATA: 1800000, // 30 minutes

  // General notification polling - serverless optimized
  NOTIFICATIONS: isDevelopment ? 60000 : 300000, // 1min in dev, 5min in prod

  // Activity read status sync - reduced for serverless
  ACTIVITY_READ_STATUS: isDevelopment ? 90000 : 300000, // 1.5min in dev, 5min in prod

  // Notes read status sync for unread count system
  NOTE_READ_STATUS: isDevelopment ? 90000 : 300000, // 1.5min in dev, 5min in prod

  // Notes messages refresh for active notes panels
  NOTE_MESSAGES: isDevelopment ? 30000 : 60000, // 30s in dev, 1min in prod
} as const;

/**
 * Polling feature flags - enable/disable specific polling
 */
export const POLLING_ENABLED = {
  // Role updates polling
  ROLE_UPDATES: true,

  // Activity logs auto-refresh (currently disabled)
  ACTIVITY_LOGS: false,

  // Product data auto-refresh (currently disabled)
  PRODUCT_DATA: false,

  // General notifications
  NOTIFICATIONS: true,

  // Activity read status sync for unread count system
  ACTIVITY_READ_STATUS: true,

  // Notes read status sync for unread count system
  NOTE_READ_STATUS: true,

  // Notes messages refresh for active notes panels
  NOTE_MESSAGES: false, // Will be enabled per-panel when notes panel is open
} as const;

/**
 * Optimized polling configuration options
 */
export const POLLING_CONFIG = {
  // Maximum number of retries for failed polling requests
  MAX_RETRIES: 2, // Reduced from 3 to 2

  // Backoff multiplier for retry delays
  RETRY_BACKOFF: 2.0, // Increased for faster backoff

  // Maximum retry delay in milliseconds
  MAX_RETRY_DELAY: 60000, // Increased to 1 minute

  // Enable/disable polling when tab is not visible (optimized)
  POLL_IN_BACKGROUND: false, // Disabled to save resources

  // Enable/disable polling when user is idle (optimized)
  POLL_WHEN_IDLE: false, // Disabled to save resources

  // Idle timeout in milliseconds (5 minutes - reduced)
  IDLE_TIMEOUT: 300000, // 5 minutes instead of 15

  // Visibility change detection
  PAUSE_ON_HIDDEN: true, // New: Pause polling when tab is hidden

  // Network-aware polling
  REDUCE_ON_SLOW_CONNECTION: true, // New: Reduce frequency on slow connections
} as const;

/**
 * Development helpers
 */
export const POLLING_DEBUG = {
  // Enable verbose logging for polling operations
  VERBOSE_LOGGING: isDevelopment,

  // Log all polling events
  LOG_ALL_EVENTS: isDevelopment,

  // Show polling status in console
  SHOW_STATUS: isDevelopment,
} as const;

/**
 * Helper function to get polling interval with environment override
 */
export function getPollingInterval(
  type: keyof typeof POLLING_INTERVALS
): number {
  // Check for environment variable override
  const envKey = `NEXT_PUBLIC_POLLING_${type}`;
  const envValue = process.env[envKey];

  if (envValue && !isNaN(Number(envValue))) {
    return Number(envValue);
  }

  return POLLING_INTERVALS[type];
}

/**
 * Helper function to check if polling is enabled for a specific feature
 */
export function isPollingEnabled(type: keyof typeof POLLING_ENABLED): boolean {
  // Check for environment variable override
  const envKey = `NEXT_PUBLIC_POLLING_ENABLED_${type}`;
  const envValue = process.env[envKey];

  if (envValue !== undefined) {
    return envValue.toLowerCase() === "true";
  }

  return POLLING_ENABLED[type];
}

/**
 * Utility function to format polling interval for display
 */
export function formatPollingInterval(intervalMs: number): string {
  if (intervalMs < 1000) {
    return `${intervalMs}ms`;
  } else if (intervalMs < 60000) {
    return `${intervalMs / 1000}s`;
  } else {
    return `${intervalMs / 60000}m`;
  }
}

/**
 * Get all active polling configurations for debugging
 */
export function getActivePollingConfig() {
  type PollingInfo = {
    configured: number;
    actual: number;
    enabled: boolean;
    formatted: string;
  };

  return {
    intervals: Object.entries(POLLING_INTERVALS).reduce((acc, [key, value]) => {
      const typedKey = key as keyof typeof POLLING_INTERVALS;
      acc[typedKey] = {
        configured: value,
        actual: getPollingInterval(typedKey),
        enabled: isPollingEnabled(typedKey as keyof typeof POLLING_ENABLED),
        formatted: formatPollingInterval(getPollingInterval(typedKey)),
      };
      return acc;
    }, {} as Record<keyof typeof POLLING_INTERVALS, PollingInfo>),
    config: POLLING_CONFIG,
    debug: POLLING_DEBUG,
  };
}
