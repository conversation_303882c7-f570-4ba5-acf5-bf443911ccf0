"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useImperativeHandle,
  forwardRef,
} from "react";
import { updateTabData } from "@/actions/npd.actions";
import { toast } from "sonner";

import { TabDangerZone } from "../shared/tab-danger-zone";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import Image from "next/image";
import {
  Shield,
  Settings,
  DollarSign,
  TrendingUp,
  BarChart3,
  Upload,
  X,
} from "lucide-react";
import { useFormFieldStyles } from "@/hooks/use-form-field-styles";
import {
  EditableDataTable,
  useCardLevelChanges,
  CardHeaderWithChanges,
} from "../shared";
import { EnhancedImageCard } from "../shared/enhanced-image-card";
import { updateTabImages } from "@/actions/cloudinary-images.actions";
import { usePermissions } from "@/hooks/use-permissions";
import { useAuth } from "@/context/auth-context";

// Data structure for Sourcing Brief
interface SourcingBriefData {
  // Basic Info
  title: string | null;
  description: string | null;
  notes: string | null;

  // Compliance Section
  certification: string | null;
  patent: string | null;
  instructionsManual: string | null;
  packagingType: string | null;

  // Product Specifications Section
  material: string | null;
  formulation: string | null;
  formShape: string | null;
  color: string | null;
  netContentCapacity: string | null;
  dimensions: string | null;
  application: string | null;
  productFeatures: string | null;
  productFeaturesArray: string[] | null;

  // Target FBA Fee Section
  retailPackaging: string | null;
  packagingDimensions: string | null;
  packagingWeight: string | null;
  fbaFee: string | null;

  // Price / Variation Section
  targetProductAsin: string | null;
  gravitiqRetailPrice: string | null;
  monthlyTargetSales: string | null;
  variation: string | null;

  // Competitor Analysis Section
  upgradeAddedValue: string | null;
  competitorRating: string | null;
  issues: string | null;
  issuesResolved: string | null;

  // Images Section
  images: string[] | string | null;
  newImageFiles?: File[]; // For new images to upload
  deletedImages?: string[]; // For tracking deleted images

  // Legacy fields (for backward compatibility)
  productSpecs?: string | null;
  targetMarkets?: string | null;
  budgetRange?: string | null;
  timeline?: string | null;
  qualityRequirements?: string | null;
  complianceNeeds?: string | null;
  logisticsPreferences?: string | null;
  supplierCriteria?: string | null;

  // Meta fields
  user?: string | null;
  editedAt?: string;
  editedBy?: string | null;
  isEdited?: boolean;
  versions?: unknown[];
}

interface SourcingBriefDisplayProps {
  tabId: string;
  initialData: SourcingBriefData;
  isEditing: boolean;
  onDirtyChange: (isDirty: boolean) => void;
  onUserMention?: (username: string) => void;
  productId: string;
  productName: string;
  productSlug: string;
  // Props for TabDangerZone
  tabName?: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
}

export interface SourcingBriefDisplayRef {
  save: () => Promise<void>;
}

export const SourcingBriefDisplay = forwardRef<
  SourcingBriefDisplayRef,
  SourcingBriefDisplayProps
>(function SourcingBriefDisplay(
  {
    tabId,
    initialData,
    isEditing,
    onDirtyChange,
    productId,
    productName,
    productSlug,
    tabName,
    tabCreatorId,
    productOwnerId,
  },
  ref
) {
  const { user } = useAuth();

  // State for the current data
  const [data, setData] = useState<SourcingBriefData>(initialData);
  const [originalData, setOriginalData] =
    useState<SourcingBriefData>(initialData);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [modifiedFields, setModifiedFields] = useState<
    Set<keyof SourcingBriefData>
  >(new Set());

  // Helper function to normalize images to array
  const normalizeImages = useCallback(
    (images: string[] | string | null): string[] => {
      if (Array.isArray(images)) return images;
      if (typeof images === "string") return images.split(",").filter(Boolean);
      return [];
    },
    []
  );

  // State for image handling
  const [previewUrls, setPreviewUrls] = useState<string[]>(
    normalizeImages(data.images)
  );
  const [prevIsEditing, setPrevIsEditing] = useState(isEditing);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );

  // Permission system
  const { hasRole, isAdmin, isSuperAdmin } = usePermissions();

  // Permission checking functions
  const canEditCompliance = useCallback(() => {
    // Admins and Super Admins are excluded from this rule
    if (isAdmin || isSuperAdmin) {
      return isEditing;
    }
    // Only M&A_REGULATORY role can edit compliance section
    return isEditing && hasRole("M&A_REGULATORY");
  }, [isEditing, hasRole, isAdmin, isSuperAdmin]);

  const canEditOtherSections = useCallback(() => {
    if (!isEditing) return false;

    // Admins and Super Admins can always edit
    if (isAdmin || isSuperAdmin) {
      return true;
    }

    // Product creator can edit
    if (user?.userId === productOwnerId) {
      return true;
    }

    // Tab creator can edit
    if (user?.userId === tabCreatorId) {
      return true;
    }

    return false;
  }, [
    isEditing,
    isAdmin,
    isSuperAdmin,
    user?.userId,
    productOwnerId,
    tabCreatorId,
  ]);

  // State for product features array
  const [productFeaturesArray, setProductFeaturesArray] = useState<string[]>(
    data.productFeaturesArray ||
      (data.productFeatures ? [data.productFeatures] : [""])
  );

  // Update data function
  const updateData = (updates: Partial<SourcingBriefData>) => {
    setData((prev) => ({ ...prev, ...updates }));
  };

  // Check for unsaved changes
  useEffect(() => {
    const hasChanges = JSON.stringify(data) !== JSON.stringify(originalData);
    setHasUnsavedChanges(hasChanges);
    onDirtyChange(hasChanges);
  }, [data, originalData, onDirtyChange]);

  // Save function
  const save = useCallback(async () => {
    if (!hasUnsavedChanges) return;

    try {
      let finalImageUrls = data.images || [];

      // Handle image uploads and deletions if there are changes
      if (data.newImageFiles?.length || data.deletedImages?.length) {
        const imageResult = await updateTabImages(
          productName, // Using productName as slug
          "sourcing-brief",
          data.newImageFiles || [],
          data.deletedImages || []
        );

        if (imageResult.success) {
          // Get existing Cloudinary images (not blob URLs) that weren't deleted
          const existingCloudinaryImages = normalizeImages(data.images).filter(
            (url: string) =>
              !url.startsWith("blob:") && !data.deletedImages?.includes(url)
          );
          finalImageUrls = [
            ...existingCloudinaryImages,
            ...(imageResult.uploadedUrls || []),
          ];
        } else {
          toast.error(imageResult.message);
          throw new Error(imageResult.message);
        }
      }

      const dataToSave = {
        ...data,
        images: finalImageUrls,
        // Remove temporary fields
        newImageFiles: undefined,
        deletedImages: undefined,
        user: user?.name || "",
        editedAt: new Date().toISOString(),
        editedBy: user?.name || "",
        isEdited: true,
      };

      await updateTabData(tabId, dataToSave as Record<string, unknown>);

      // Update local state with final data
      setOriginalData(dataToSave);
      setData(dataToSave);
      setPreviewUrls(normalizeImages(finalImageUrls));
      setHasUnsavedChanges(false);

      // Dispatch event to trigger progress indicator update
      window.dispatchEvent(new CustomEvent("refreshNotifications"));

      toast.success("Sourcing Brief updated successfully");
    } catch (error) {
      console.error("Error saving sourcing brief:", error);
      toast.error("Failed to save sourcing brief changes");
      throw error;
    }
  }, [hasUnsavedChanges, tabId, data, user, productName, normalizeImages]);

  // Expose save function to parent
  useImperativeHandle(
    ref,
    () => ({
      save,
    }),
    [save]
  );

  const handleInputChange = (field: keyof SourcingBriefData, value: string) => {
    updateData({ [field]: value });

    // Track which fields have been modified
    const originalValue = originalData[field] || "";
    setModifiedFields((prev) => {
      const newModified = new Set(prev);
      if (value !== originalValue) {
        newModified.add(field);
      } else {
        newModified.delete(field);
      }
      return newModified;
    });
  };

  // Reset to original data function
  const resetToOriginalData = useCallback(() => {
    setData(originalData);
    setModifiedFields(new Set());
    setHasUnsavedChanges(false);
  }, [originalData]);

  // Update data when initialData changes
  useEffect(() => {
    setData(initialData);
    setOriginalData(initialData);
    setHasUnsavedChanges(false);
    setProductFeaturesArray(
      initialData.productFeaturesArray ||
        (initialData.productFeatures ? [initialData.productFeatures] : [""])
    );
    // Only set preview URLs from actual saved images, filter out blob URLs
    const savedImages = normalizeImages(initialData.images).filter(
      (url) => url && !url.startsWith("blob:")
    );
    setPreviewUrls(savedImages);
  }, [initialData, normalizeImages]);

  // Notify parent of changes
  useEffect(() => {
    onDirtyChange(hasUnsavedChanges);
  }, [hasUnsavedChanges, onDirtyChange]);

  // Handle edit mode transitions
  useEffect(() => {
    if (prevIsEditing && !isEditing) {
      // Exiting edit mode - reset to original data
      resetToOriginalData();
    }
    setPrevIsEditing(isEditing);
  }, [isEditing, prevIsEditing, resetToOriginalData]);

  // Use centralized form field styling
  const { getFieldStyles } = useFormFieldStyles(modifiedFields);

  // Define card field groups for Sourcing Brief
  const cardFieldGroups = {
    compliance: [
      "certification",
      "patent",
      "instructionsManual",
      "packagingType",
    ],
    productSpecs: [
      "material",
      "formulation",
      "formShape",
      "color",
      "netContentCapacity",
      "dimensions",
      "application",
      "productFeatures",
    ],
    packaging: [
      "retailPackaging",
      "packagingDimensions",
      "packagingWeight",
      "fbaFee",
    ],
    pricing: [
      "targetProductAsin",
      "gravitiqRetailPrice",
      "monthlyTargetSales",
      "variation",
    ],
    competitor: [
      "upgradeAddedValue",
      "competitorRating",
      "issues",
      "issuesResolved",
    ],
  };

  // Use card-level changes hook
  const { hasCardChanges } = useCardLevelChanges(
    modifiedFields,
    cardFieldGroups
  );

  // Product features array handlers
  const handleProductFeaturesChange = (features: string[]) => {
    setProductFeaturesArray(features);
    const featuresString = features.filter((f) => f.trim()).join("\n");
    updateData({
      productFeaturesArray: features,
      productFeatures: featuresString,
    });

    // Track changes
    const originalFeatures =
      data.productFeaturesArray ||
      (data.productFeatures ? [data.productFeatures] : [""]);
    const hasChanged =
      JSON.stringify(features) !== JSON.stringify(originalFeatures);
    setModifiedFields((prev) => {
      const newModified = new Set(prev);
      if (hasChanged) {
        newModified.add("productFeatures");
      } else {
        newModified.delete("productFeatures");
      }
      return newModified;
    });
  };

  const addProductFeature = () => {
    const newFeatures = [...productFeaturesArray, ""];
    handleProductFeaturesChange(newFeatures);
  };

  const removeProductFeature = (index: number) => {
    const newFeatures = productFeaturesArray.filter((_, i) => i !== index);
    handleProductFeaturesChange(newFeatures);
  };

  // Keyboard navigation for image modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedImageIndex === null) return;

      switch (e.key) {
        case "Escape":
          setSelectedImageIndex(null);
          break;
        case "ArrowLeft":
          if (selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
          }
          break;
        case "ArrowRight":
          if (selectedImageIndex < previewUrls.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
          }
          break;
      }
    };

    if (selectedImageIndex !== null) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [selectedImageIndex, previewUrls.length]);

  return (
    <div className="h-full w-full overflow-y-auto">
      <div className="space-y-6 p-6">
        {/* Compliance */}
        <Card>
          <CardHeader>
            <CardHeaderWithChanges
              icon={<Shield className="h-5 w-5 text-purple-600" />}
              title="Compliance"
              hasChanges={hasCardChanges("compliance")}
            />
          </CardHeader>
          <CardContent>
            <EditableDataTable
              data={[
                {
                  id: "certification",
                  label: "Certification",
                  value: data.certification,
                  placeholder: "Required certifications",
                  onChange: (value) =>
                    handleInputChange("certification", value),
                  disabled: !canEditCompliance(),
                  className: getFieldStyles("certification"),
                  isChanged: modifiedFields.has("certification"),
                  isEditing: canEditCompliance(),
                  isPermissionRestricted: !canEditCompliance() && isEditing,
                },
                {
                  id: "patent",
                  label: "Patent",
                  value: data.patent,
                  placeholder: "Patent requirements or restrictions",
                  onChange: (value) => handleInputChange("patent", value),
                  disabled: !canEditCompliance(),
                  className: getFieldStyles("patent"),
                  isChanged: modifiedFields.has("patent"),
                  isEditing: canEditCompliance(),
                  isPermissionRestricted: !canEditCompliance() && isEditing,
                },
                {
                  id: "instructionsManual",
                  label: "Instructions Manual",
                  value: data.instructionsManual,
                  placeholder: "Manual requirements",
                  onChange: (value) =>
                    handleInputChange("instructionsManual", value),
                  disabled: !canEditCompliance(),
                  className: getFieldStyles("instructionsManual"),
                  isChanged: modifiedFields.has("instructionsManual"),
                  isEditing: canEditCompliance(),
                  isPermissionRestricted: !canEditCompliance() && isEditing,
                },
                {
                  id: "packagingType",
                  label: "Packaging Type",
                  value: data.packagingType,
                  placeholder: "Type of packaging required",
                  onChange: (value) =>
                    handleInputChange("packagingType", value),
                  disabled: !canEditCompliance(),
                  className: getFieldStyles("packagingType"),
                  isChanged: modifiedFields.has("packagingType"),
                  isEditing: canEditCompliance(),
                  isPermissionRestricted: !canEditCompliance() && isEditing,
                },
              ]}
            />
          </CardContent>
        </Card>

        {/* Product Specifications */}
        <Card>
          <CardHeader>
            <CardHeaderWithChanges
              icon={<Settings className="h-5 w-5 text-blue-600" />}
              title="Product Specifications"
              hasChanges={hasCardChanges("productSpecs")}
            />
          </CardHeader>
          <CardContent>
            <EditableDataTable
              data={[
                {
                  id: "material",
                  label: "Material",
                  value: data.material,
                  placeholder: "Primary materials used",
                  onChange: (value) => handleInputChange("material", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("material"),
                  isChanged: modifiedFields.has("material"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "formulation",
                  label: "Formulation",
                  value: data.formulation,
                  placeholder: "Product formulation details",
                  type: "textarea",
                  rows: 3,
                  onChange: (value) => handleInputChange("formulation", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("formulation"),
                  isChanged: modifiedFields.has("formulation"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "formShape",
                  label: "Form / Shape",
                  value: data.formShape,
                  placeholder: "Product form or shape",
                  onChange: (value) => handleInputChange("formShape", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("formShape"),
                  isChanged: modifiedFields.has("formShape"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "color",
                  label: "Color",
                  value: data.color,
                  placeholder: "Product color specifications",
                  onChange: (value) => handleInputChange("color", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("color"),
                  isChanged: modifiedFields.has("color"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "netContentCapacity",
                  label: "Net Content / Capacity",
                  value: data.netContentCapacity,
                  placeholder: "Net content or capacity",
                  onChange: (value) =>
                    handleInputChange("netContentCapacity", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("netContentCapacity"),
                  isChanged: modifiedFields.has("netContentCapacity"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "dimensions",
                  label: "Dimensions",
                  value: data.dimensions,
                  placeholder: "Product dimensions (L x W x H)",
                  onChange: (value) => handleInputChange("dimensions", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("dimensions"),
                  isChanged: modifiedFields.has("dimensions"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "application",
                  label: "Application",
                  value: data.application,
                  placeholder: "Product application and use cases",
                  type: "textarea",
                  rows: 3,
                  onChange: (value) => handleInputChange("application", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("application"),
                  isChanged: modifiedFields.has("application"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "productFeatures",
                  label: "Product Features",
                  value: data.productFeatures,
                  placeholder: "Product feature",
                  type: "multi-input",
                  multiValues: productFeaturesArray,
                  onMultiChange: handleProductFeaturesChange,
                  onAddRow: addProductFeature,
                  onRemoveRow: removeProductFeature,
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("productFeatures"),
                  isChanged: modifiedFields.has("productFeatures"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                  onChange: () => {}, // Required but not used for multi-input
                },
              ]}
            />
          </CardContent>
        </Card>

        {/* Target FBA Fee & Packaging Dimensions*/}
        <Card>
          <CardHeader>
            <CardHeaderWithChanges
              icon={<DollarSign className="h-5 w-5 text-green-600" />}
              title="Target FBA Fee & Packaging Dimensions"
              hasChanges={hasCardChanges("packaging")}
            />
          </CardHeader>
          <CardContent>
            <EditableDataTable
              data={[
                {
                  id: "retailPackaging",
                  label: "Retail Packaging",
                  value: data.retailPackaging,
                  placeholder: "Retail packaging requirements",
                  onChange: (value) =>
                    handleInputChange("retailPackaging", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("retailPackaging"),
                  isChanged: modifiedFields.has("retailPackaging"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "packagingDimensions",
                  label: "Packaging Dimensions",
                  value: data.packagingDimensions,
                  placeholder: "Package dimensions (L x W x H)",
                  onChange: (value) =>
                    handleInputChange("packagingDimensions", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("packagingDimensions"),
                  isChanged: modifiedFields.has("packagingDimensions"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "packagingWeight",
                  label: "Packaging Weight",
                  value: data.packagingWeight,
                  placeholder: "Total package weight",
                  onChange: (value) =>
                    handleInputChange("packagingWeight", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("packagingWeight"),
                  isChanged: modifiedFields.has("packagingWeight"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "fbaFee",
                  label: "FBA Fee",
                  value: data.fbaFee,
                  placeholder: "Target FBA fee amount",
                  onChange: (value) => handleInputChange("fbaFee", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("fbaFee"),
                  isChanged: modifiedFields.has("fbaFee"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
              ]}
            />
          </CardContent>
        </Card>

        {/* Price / Variation */}
        <Card>
          <CardHeader>
            <CardHeaderWithChanges
              icon={<TrendingUp className="h-5 w-5 text-orange-600" />}
              title="Price / Variation"
              hasChanges={hasCardChanges("pricing")}
            />
          </CardHeader>
          <CardContent>
            <EditableDataTable
              data={[
                {
                  id: "targetProductAsin",
                  label: "Target Product / ASIN",
                  value: data.targetProductAsin,
                  placeholder: "Amazon ASIN of target product",
                  onChange: (value) =>
                    handleInputChange("targetProductAsin", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("targetProductAsin"),
                  isChanged: modifiedFields.has("targetProductAsin"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "gravitiqRetailPrice",
                  label: "Gravitiq Retail Price",
                  value: data.gravitiqRetailPrice,
                  placeholder: "Target retail price",
                  onChange: (value) =>
                    handleInputChange("gravitiqRetailPrice", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("gravitiqRetailPrice"),
                  isChanged: modifiedFields.has("gravitiqRetailPrice"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "monthlyTargetSales",
                  label: "Monthly Target Sales",
                  value: data.monthlyTargetSales,
                  placeholder: "Monthly sales target",
                  onChange: (value) =>
                    handleInputChange("monthlyTargetSales", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("monthlyTargetSales"),
                  isChanged: modifiedFields.has("monthlyTargetSales"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "variation",
                  label: "Variation",
                  value: data.variation,
                  placeholder: "Product variations or options",
                  onChange: (value) => handleInputChange("variation", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("variation"),
                  isChanged: modifiedFields.has("variation"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
              ]}
            />
          </CardContent>
        </Card>

        {/* Target Competitor Analysis */}
        <Card>
          <CardHeader>
            <CardHeaderWithChanges
              icon={<BarChart3 className="h-5 w-5 text-red-600" />}
              title="Target Competitor Analysis"
              hasChanges={hasCardChanges("competitor")}
            />
          </CardHeader>
          <CardContent>
            <EditableDataTable
              data={[
                {
                  id: "upgradeAddedValue",
                  label: "Upgrade / Added Value",
                  value: data.upgradeAddedValue,
                  placeholder: "Value-added features or improvements",
                  type: "textarea",
                  rows: 3,
                  onChange: (value) =>
                    handleInputChange("upgradeAddedValue", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("upgradeAddedValue"),
                  isChanged: modifiedFields.has("upgradeAddedValue"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                  helpText:
                    "Clearly articulate the unique value proposition or enhancement that distinguishes your product from competitors. Consider strategic upgrades such as: increasing quantity, reducing price, or improving key features—such as materials, formulation, functionality, or durability. What tangible improvements can we make that would create a meaningful competitive edge?",
                },
                {
                  id: "competitorRating",
                  label: "Competitor Rating",
                  value: data.competitorRating,
                  placeholder: "Competitor product rating",
                  onChange: (value) =>
                    handleInputChange("competitorRating", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("competitorRating"),
                  isChanged: modifiedFields.has("competitorRating"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                },
                {
                  id: "issues",
                  label: "Issues",
                  value: data.issues,
                  placeholder: "Known issues with competitor products",
                  type: "textarea",
                  rows: 3,
                  onChange: (value) => handleInputChange("issues", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("issues"),
                  isChanged: modifiedFields.has("issues"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                  helpText:
                    "Identify recurring customer complaints in competitor reviews—such as poor quality, performance issues, confusing usability, weak customer service, or lack of value—to uncover pain points your product can address.",
                },
                {
                  id: "issuesResolved",
                  label: "Issues Resolved",
                  value: data.issuesResolved,
                  placeholder:
                    "How these issues will be resolved in our product",
                  type: "textarea",
                  rows: 3,
                  onChange: (value) =>
                    handleInputChange("issuesResolved", value),
                  disabled: !canEditOtherSections(),
                  className: getFieldStyles("issuesResolved"),
                  isChanged: modifiedFields.has("issuesResolved"),
                  isEditing: canEditOtherSections(),
                  isPermissionRestricted: !canEditOtherSections() && isEditing,
                  helpText:
                    "Which of the above issues does your product effectively resolve or improve—such as superior quality, more reliable performance, easier usability, better customer support, or stronger value for money—and how does it do so in a way that competitors don't?",
                },
              ]}
            />
          </CardContent>
        </Card>

        {/* Images */}
        <EnhancedImageCard
          title="Reference Images"
          icon={<Upload className="h-5 w-5 text-indigo-600" />}
          images={previewUrls}
          isEditing={canEditOtherSections()}
          onImagesChange={(images) => {
            setPreviewUrls(images);
            setModifiedFields((prev) => new Set(prev).add("images"));
            setHasUnsavedChanges(true);
          }}
          productSlug={productName}
          tabType="sourcing-brief"
          maxFileSize={5}
          allowMultiple={true}
          showUploadButton={true}
          showAddUrlButton={true}
          onNewFiles={(files) => {
            // Store new files for upload on save
            const existingNewFiles = data.newImageFiles || [];
            const updatedNewFiles = [...existingNewFiles, ...files];
            updateData({ newImageFiles: updatedNewFiles });
          }}
          onImageOperationComplete={(uploadedUrls, deletedUrls) => {
            if (uploadedUrls) {
              // Handle successful uploads
              const existingImages = normalizeImages(data.images).filter(
                (url: string) => !url.startsWith("blob:")
              );
              const finalImages = [...existingImages, ...uploadedUrls];
              updateData({ images: finalImages });
            }
            if (deletedUrls) {
              // Handle successful deletions
              const remainingImages = normalizeImages(data.images).filter(
                (url: string) => !deletedUrls.includes(url)
              );
              updateData({ images: remainingImages });
            }
          }}
        />

        {/* Tab Danger Zone */}
        {isEditing && (
          <TabDangerZone
            tabId={tabId}
            tabName={tabName || "Sourcing Brief"}
            productId={productId}
            productName={productName}
            productSlug={productSlug}
            tabCreatorId={tabCreatorId}
            productOwnerId={productOwnerId}
            isEditMode={isEditing}
          />
        )}
      </div>

      {/* Full-Screen Image Modal */}
      {selectedImageIndex !== null && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-50">
          {/* Close Button */}
          <button
            className="absolute top-6 right-6 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-3 transition-all duration-200 z-20"
            onClick={() => setSelectedImageIndex(null)}
            aria-label="Close image viewer"
          >
            <X className="h-6 w-6" />
          </button>

          {/* Previous Button */}
          {selectedImageIndex > 0 && (
            <button
              className="absolute left-6 top-1/2 -translate-y-1/2 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-4 transition-all duration-200 z-20"
              onClick={() => setSelectedImageIndex(selectedImageIndex - 1)}
              aria-label="Previous image"
            >
              <span className="text-2xl font-bold">‹</span>
            </button>
          )}

          {/* Next Button */}
          {selectedImageIndex < previewUrls.length - 1 && (
            <button
              className="absolute right-6 top-1/2 -translate-y-1/2 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-4 transition-all duration-200 z-20"
              onClick={() => setSelectedImageIndex(selectedImageIndex + 1)}
              aria-label="Next image"
            >
              <span className="text-2xl font-bold">›</span>
            </button>
          )}

          {/* Image Container */}
          <div className="relative w-full h-full flex items-center justify-center p-16">
            <Image
              src={previewUrls[selectedImageIndex]}
              alt={`Reference Photo ${selectedImageIndex + 1}`}
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain"
              style={{
                maxWidth: "calc(100vw - 8rem)",
                maxHeight: "calc(100vh - 8rem)",
                width: "auto",
                height: "auto",
              }}
              unoptimized={previewUrls[selectedImageIndex]?.startsWith("blob:")}
            />
          </div>

          {/* Image Counter */}
          <div className="absolute bottom-6 left-1/2 -translate-x-1/2 text-white/90 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium">
            {selectedImageIndex + 1} of {previewUrls.length}
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setSelectedImageIndex(null)}
            aria-label="Click to close"
          />
        </div>
      )}
    </div>
  );
});
