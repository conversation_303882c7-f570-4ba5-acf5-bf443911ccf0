"use client";

import { useTheme } from "next-themes";
import { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  SunIcon,
  MoonIcon,
  MonitorIcon,
  CircleIcon,
  Palette,
  Stars,
  Sparkles,
} from "lucide-react";
import { useThemeConfig } from "@/components/active-theme";
import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";

const COLOR_THEMES = [
  {
    name: "Default",
    value: "default",
    icon: Palette,
    previewColors: ["bg-neutral-600", "bg-neutral-400", "bg-neutral-200"],
  },
  {
    name: "Blue",
    value: "blue",
    icon: Sparkles,
    previewColors: ["bg-blue-600", "bg-blue-400", "bg-blue-200"],
  },
  {
    name: "Green",
    value: "green",
    icon: Stars,
    previewColors: ["bg-lime-600", "bg-lime-400", "bg-lime-200"],
  },
  {
    name: "Amber",
    value: "amber",
    icon: CircleIcon,
    previewColors: ["bg-amber-600", "bg-amber-400", "bg-amber-200"],
  },
];

export interface Theme {
  name: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  previewColors: string[];
}

export const SettingsContent = () => {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const { activeTheme, setActiveTheme } = useThemeConfig();

  const [isCompact, setIsCompact] = useState(false);

  useEffect(() => {
    setMounted(true);
    const currentTheme = activeTheme || "";
    setIsCompact(currentTheme.includes("compact"));
  }, [activeTheme]);

  if (!mounted) return null;

  const handleThemeChange = (baseTheme: string) => {
    let newTheme = baseTheme;
    if (isCompact) newTheme += " compact";
    setActiveTheme(newTheme.trim());
  };

  const handleCompactToggle = (checked: boolean) => {
    setIsCompact(checked);
    const baseTheme = activeTheme.replace(/\s*compact/g, "").trim();
    let newTheme = baseTheme;
    if (checked) newTheme += " compact";
    setActiveTheme(newTheme.trim());
  };

  const baseTheme = activeTheme.replace(/\s*compact/g, "").trim();

  return (
    <div className="h-full w-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div>
            <h1 className="text-2xl font-bold">Settings</h1>
            <p className="text-muted-foreground">
              Customize your application preferences
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-2xl mx-auto space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="space-y-1">
                  <h2 className="text-xl font-semibold tracking-tight">
                    Appearance
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Customize the appearance of the application
                  </p>
                </div>
              </CardHeader>
              <CardContent className="grid gap-6">
                <div className="space-y-2">
                  <Label>Theme Mode</Label>
                  <RadioGroup
                    defaultValue={theme}
                    onValueChange={(value: string) => setTheme(value)}
                    className="grid grid-cols-3 gap-2"
                  >
                    <div>
                      <RadioGroupItem
                        value="light"
                        id="light"
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor="light"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                      >
                        <SunIcon className="mb-2 h-5 w-5" />
                        <span className="text-xs">Light</span>
                      </Label>
                    </div>
                    <div>
                      <RadioGroupItem
                        value="dark"
                        id="dark"
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor="dark"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                      >
                        <MoonIcon className="mb-2 h-5 w-5" />
                        <span className="text-xs">Dark</span>
                      </Label>
                    </div>
                    <div>
                      <RadioGroupItem
                        value="system"
                        id="system"
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor="system"
                        className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                      >
                        <MonitorIcon className="mb-2 h-5 w-5" />
                        <span className="text-xs">System</span>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label>Color Theme</Label>
                  <RadioGroup
                    value={baseTheme}
                    onValueChange={handleThemeChange}
                    className="grid grid-cols-4 gap-2"
                  >
                    {COLOR_THEMES.map((theme) => (
                      <div key={theme.value}>
                        <RadioGroupItem
                          value={theme.value}
                          id={theme.value}
                          className="peer sr-only"
                        />
                        <Label
                          htmlFor={theme.value}
                          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                        >
                          <div className="mb-2 flex flex-col items-center gap-1.5">
                            <theme.icon className="h-5 w-5" />
                            <div className="flex gap-1">
                              {theme.previewColors.map((color, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "h-1.5 w-1.5 rounded-full",
                                    color
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                          <span className="text-xs">{theme.name}</span>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Interface layout density</Label>
                      <p className="text-[0.8rem] text-muted-foreground">
                        Make the interface compact
                      </p>
                    </div>
                    <Switch
                      checked={isCompact}
                      onCheckedChange={handleCompactToggle}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
