"use client";

import React from "react";

interface User {
  id: string;
  name: string;
  email?: string;
}

interface MessageContentProps {
  content: string;
  onMentionClick?: (username: string) => void;
  users?: User[];
}

export function MessageContent({
  content,
  onMentionClick,
  users = [],
}: MessageContentProps) {
  // Function to parse @mentions and make them clickable
  const parseContent = (text: string) => {
    if (!users.length) {
      // Fallback to simple regex if no users provided
      const mentionRegex = /@([a-zA-Z0-9\s]+?)(?=\s|$|[.,!?;:])/g;
      const parts = [];
      let lastIndex = 0;
      let match;

      while ((match = mentionRegex.exec(text)) !== null) {
        if (match.index > lastIndex) {
          parts.push(text.slice(lastIndex, match.index));
        }

        const username = match[1].trim();
        parts.push(
          <button
            key={`mention-${match.index}`}
            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer font-medium bg-blue-50 px-1 py-0.5 rounded-sm mr-1"
            onClick={() => onMentionClick?.(username)}
          >
            @{username}
          </button>
        );

        lastIndex = match.index + match[0].length;
      }

      if (lastIndex < text.length) {
        parts.push(text.slice(lastIndex));
      }

      return parts.length > 0 ? parts : [text];
    }

    // Use actual usernames for precise matching
    const mentions: Array<{
      start: number;
      end: number;
      text: string;
      username: string;
    }> = [];

    // Find all @mentions that match actual usernames
    users.forEach((user) => {
      const mentionPattern = new RegExp(
        `@${user.name.replace(
          /[.*+?^${}()|[\]\\]/g,
          "\\$&"
        )}(?=\\s|$|[.,!?;:])`,
        "g"
      );
      let match;

      while ((match = mentionPattern.exec(text)) !== null) {
        mentions.push({
          start: match.index,
          end: match.index + match[0].length,
          text: match[0],
          username: user.name,
        });
      }
    });

    // Sort mentions by start position and remove overlaps
    mentions.sort((a, b) => a.start - b.start);
    const uniqueMentions: Array<{
      start: number;
      end: number;
      text: string;
      username: string;
    }> = [];
    for (const mention of mentions) {
      if (
        !uniqueMentions.some(
          (m) =>
            (mention.start >= m.start && mention.start < m.end) ||
            (mention.end > m.start && mention.end <= m.end)
        )
      ) {
        uniqueMentions.push(mention);
      }
    }

    // Build parts array
    const parts = [];
    let lastIndex = 0;

    for (const mention of uniqueMentions) {
      // Add text before mention
      if (mention.start > lastIndex) {
        parts.push(text.slice(lastIndex, mention.start));
      }

      // Add clickable mention
      parts.push(
        <button
          key={`mention-${mention.start}`}
          className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer font-medium bg-blue-50 px-1 py-0.5 rounded-sm mr-1"
          onClick={() => onMentionClick?.(mention.username)}
        >
          {mention.text}
        </button>
      );

      lastIndex = mention.end;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts.length > 0 ? parts : [text];
  };

  const parsedContent = parseContent(content);

  return (
    <span className="text-sm text-foreground whitespace-pre-wrap break-words">
      {parsedContent.map((part, index) => (
        <React.Fragment key={index}>{part}</React.Fragment>
      ))}
    </span>
  );
}
