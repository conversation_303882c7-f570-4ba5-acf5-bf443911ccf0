"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tab<PERSON>eader, LoadingButton } from "../shared";

import {
  TrendingUp,
  Activity,
  GitBranch,
  ArrowRight,
  Image as ImageIcon,
} from "lucide-react";
import { getTabOrder } from "@/lib/constants/tab-order";
import { toast } from "sonner";

interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

interface AddOverviewTabProps {
  productId: string;
  productName: string;
  onBack: () => void;
  onSave: (tabData: TabData) => Promise<void>;
}

export function AddOverviewTab({
  productName,
  onBack,
  onSave,
}: AddOverviewTabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const formData = {
    progressSummary: {
      overallProgress: 0,
      milestones: [],
    },
    keyActions: [],
    dependencies: [],
    nextSteps: [],
    images: [],
  };

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsLoading(true);

    try {
      await onSave({
        tabName: "Overview",
        order: getTabOrder("Overview"),
        fields: formData,
      });
      // Success toast will be shown after page reload via URL parameters
    } catch (error) {
      console.error("Error creating overview tab:", error);
      toast.error("Failed to create overview tab. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <TabHeader
        title="Add Overview Tab"
        description="Create an overview tab for {productName}"
        productName={productName}
        onBack={onBack}
        actionButton={
          <LoadingButton
            isLoading={isLoading}
            onClick={handleSubmit}
            loadingText="Creating..."
            defaultText="Create Overview"
          />
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Tab Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Overview Tab
              </CardTitle>
              <CardDescription>
                This tab will provide a comprehensive overview of your product
                with progress tracking, key actions, dependencies, and next
                steps.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium">Progress Summary</h3>
                    <p className="text-sm text-muted-foreground">
                      Track overall progress and milestones
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <Activity className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium">Key Actions</h3>
                    <p className="text-sm text-muted-foreground">
                      Document important actions taken
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <GitBranch className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-medium">Dependencies</h3>
                    <p className="text-sm text-muted-foreground">
                      Track project dependencies
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <ArrowRight className="h-8 w-8 text-orange-600" />
                  <div>
                    <h3 className="font-medium">Next Steps</h3>
                    <p className="text-sm text-muted-foreground">
                      Plan upcoming actions
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <ImageIcon className="h-8 w-8 text-indigo-600" />
                  <div>
                    <h3 className="font-medium">Images</h3>
                    <p className="text-sm text-muted-foreground">
                      Upload relevant images
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your overview tab will include these sections that you can edit
                and customize after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Progress Summary</Badge>
                  <span className="text-sm text-muted-foreground">
                    Visual progress tracking with milestones
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Key Actions Made</Badge>
                  <span className="text-sm text-muted-foreground">
                    Chronological list of important actions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Dependencies</Badge>
                  <span className="text-sm text-muted-foreground">
                    Internal and external dependencies tracking
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Next Steps</Badge>
                  <span className="text-sm text-muted-foreground">
                    Actionable next steps with assignees
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Images</Badge>
                  <span className="text-sm text-muted-foreground">
                    Visual documentation and references
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
