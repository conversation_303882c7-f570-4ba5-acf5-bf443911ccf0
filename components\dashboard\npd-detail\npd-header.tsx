"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { NPDSubscriptionToggle } from "@/components/dashboard/npd-subscription-toggle";
import {
  getProductStageColor,
  formatProductStageLabel,
} from "@/lib/constants/product-stages";
import { PackageIcon, X, Settings, Edit } from "lucide-react";
import { TabProgressIndicator } from "./tab-progress-indicator";
import { TabCompletionControls } from "./tab-completion-controls";
import { LoadingButton } from "../npd-tabs/shared/loading-button";
import type { ProductWithTabs } from "@/types";

interface ProductHeaderProps {
  product: ProductWithTabs;
  isEditing: boolean;
  hasUnsavedChanges: boolean;
  isSaving?: boolean;
  onEditToggle: () => void;
  onCancelEdit: () => void;
  onNavigateToSettings: () => void;
  getEditButtonText: () => string;
  hasTabsToEdit: boolean;
}

export function ProductHeader({
  product,
  isEditing,
  hasUnsavedChanges,
  isSaving = false,
  onEditToggle,
  onCancelEdit,
  onNavigateToSettings,
  getEditButtonText,
  hasTabsToEdit,
}: ProductHeaderProps) {
  // State to force re-calculation when tab data changes
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle refresh when tab completion is updated
  const handleProgressUpdate = () => {
    setRefreshTrigger((prev) => prev + 1);
  };
  return (
    <div className="flex-shrink-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex gap-4">
            {/* Product image thumbnail */}
            <div className="flex-shrink-0">
              {product.imageUrl ? (
                <Image
                  src={product.imageUrl}
                  alt={`${product.name} image`}
                  width={64}
                  height={64}
                  className="w-16 h-16 rounded-lg object-cover border border-border/50 shadow-sm"
                />
              ) : (
                <div className="w-16 h-16 rounded-lg bg-muted/50 border border-border/50 flex items-center justify-center">
                  <PackageIcon className="h-8 w-8 text-muted-foreground" />
                </div>
              )}
            </div>

            {/* Product info */}
            <div className="space-y-1.5 flex-1">
              {/* Product name with brand and mini stage indicator */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold tracking-tight">
                    {product.name}
                  </h1>
                  {/* Mini stage indicator */}
                  <div
                    className={`w-3 h-3 rounded-full ${getProductStageColor(
                      product.stage
                    )} border border-background/50 shadow-sm`}
                    title={`Stage: ${formatProductStageLabel(product.stage)}`}
                  />
                </div>
                <Badge variant="secondary" className="text-sm px-3 py-1">
                  {product.brand}
                </Badge>
                {/* Subscription toggle */}
                <NPDSubscriptionToggle
                  productId={product.id}
                  productName={product.name}
                  isSubscribed={product.isSubscribed || false}
                  size="md"
                />
              </div>

              {/* Product description */}
              {product.description && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <p className="text-muted-foreground text-base max-w-3xl line-clamp-1 cursor-default">
                      {product.description}
                    </p>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    align="start"
                    className="max-w-md"
                  >
                    <p className="text-sm">{product.description}</p>
                  </TooltipContent>
                </Tooltip>
              )}

              {/* Tab Progress Indicator */}
              {product.tabs && product.tabs.length > 0 && (
                <div className="flex items-center gap-2">
                  <TabProgressIndicator
                    tabs={product.tabs}
                    productId={product.id}
                    refreshTrigger={refreshTrigger}
                  />
                  {/* Progress completion controls - now a permanent feature */}
                  <TabCompletionControls
                    productId={product.id}
                    tabs={product.tabs}
                    onUpdate={handleProgressUpdate}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            {/* Only show edit controls when there are tabs to edit */}
            {hasTabsToEdit && (
              <>
                {isEditing && (
                  <Button
                    variant={hasUnsavedChanges ? "destructive" : "ghost"}
                    onClick={onCancelEdit}
                    className="cursor-pointer"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel {hasUnsavedChanges && "(Unsaved changes)"}
                  </Button>
                )}
                {isEditing ? (
                  <LoadingButton
                    isLoading={isSaving}
                    onClick={onEditToggle}
                    loadingText="Saving..."
                    defaultText={getEditButtonText()}
                    icon={<PackageIcon className="h-4 w-4" />}
                    className="cursor-pointer"
                  />
                ) : (
                  <Button
                    onClick={onEditToggle}
                    variant="outline"
                    className="cursor-pointer"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    {getEditButtonText()}
                  </Button>
                )}
              </>
            )}
            <Button
              onClick={onNavigateToSettings}
              variant="outline"
              disabled={isEditing}
              title={
                isEditing
                  ? "Exit edit mode to access settings"
                  : "Product settings"
              }
              className="cursor-pointer"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
