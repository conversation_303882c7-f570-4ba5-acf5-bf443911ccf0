/**
 * Centralized styling utilities for form fields with change indicators
 * Single source of truth for all form field visual feedback
 */

// Base styling configuration
const FORM_FIELD_STYLES = {
  // Default styling for unmodified fields
  base: {
    border: "border-blue-300",
    focusBorder: "focus:border-blue-500",
    background: "bg-blue-50/50",
  },

  // Styling for modified fields (orange theme)
  modified: {
    ring: "ring-2 ring-orange-200",
    border: "border-orange-400",
    focusBorder: "focus:border-orange-500",
    spacing: "mt-1", // Prevents border from touching labels
  },

  // Special styling for table cells (keeps background for visual context)
  tableCell: {
    base: {
      border: "border-blue-300",
      focusBorder: "focus:border-blue-500",
      background: "bg-blue-50/50",
    },
    modified: {
      ring: "ring-2 ring-orange-200",
      border: "border-orange-400",
      focusBorder: "focus:border-orange-500",
      spacing: "mt-1",
      // Note: No background for table cells to maintain consistency
    },
  },

  // Error styling (for validation errors)
  error: {
    border: "border-red-300",
    focusBorder: "focus:border-red-500",
    background: "bg-red-50/50",
  },
} as const;

/**
 * Get styling classes for regular form fields (Input, Textarea)
 * @param isModified - Whether the field has been modified
 * @param hasError - Whether the field has validation errors
 * @returns CSS class string
 */
export function getFormFieldStyles(
  isModified: boolean,
  hasError?: boolean
): string {
  if (hasError) {
    return [
      FORM_FIELD_STYLES.error.border,
      FORM_FIELD_STYLES.error.focusBorder,
      FORM_FIELD_STYLES.error.background,
    ].join(" ");
  }

  if (isModified) {
    return [
      FORM_FIELD_STYLES.modified.ring,
      FORM_FIELD_STYLES.modified.border,
      FORM_FIELD_STYLES.modified.focusBorder,
      FORM_FIELD_STYLES.modified.spacing,
    ].join(" ");
  }

  return ""; // Let default Tailwind styles handle unmodified state
}

/**
 * Get styling classes for table cell fields (special handling)
 * @param isModified - Whether the cell has been modified
 * @param hasError - Whether the cell has validation errors
 * @returns CSS class string
 */
export function getTableCellStyles(
  isModified: boolean,
  hasError?: boolean
): string {
  const baseClasses = [
    FORM_FIELD_STYLES.tableCell.base.border,
    FORM_FIELD_STYLES.tableCell.base.focusBorder,
    FORM_FIELD_STYLES.tableCell.base.background,
  ].join(" ");

  if (hasError) {
    return [
      FORM_FIELD_STYLES.error.border,
      FORM_FIELD_STYLES.error.focusBorder,
      FORM_FIELD_STYLES.error.background,
    ].join(" ");
  }

  if (isModified) {
    return [
      FORM_FIELD_STYLES.tableCell.modified.ring,
      FORM_FIELD_STYLES.tableCell.modified.border,
      FORM_FIELD_STYLES.tableCell.modified.focusBorder,
      FORM_FIELD_STYLES.tableCell.modified.spacing,
    ].join(" ");
  }

  return baseClasses;
}

/**
 * Helper function for components that need to check if a field is modified
 * @param fieldName - Name of the field
 * @param modifiedFields - Set of modified field names
 * @returns boolean indicating if field is modified
 */
export function isFieldModified(
  fieldName: string,
  modifiedFields: Set<string>
): boolean {
  return modifiedFields.has(fieldName);
}

/**
 * Configuration object for easy customization
 * Change these values to update styling across the entire application
 */
export const FORM_STYLING_CONFIG = {
  // Change these colors to update the theme
  modifiedColor: "orange", // orange, blue, green, etc.
  errorColor: "red",

  // Change these to update spacing
  ringWidth: "2", // 1, 2, 4, etc.
  topSpacing: "1", // 0, 1, 2, etc.

  // Enable/disable features
  showRing: true,
  showSpacing: true,
  showBackground: false, // Set to true to add background to modified fields
} as const;

/**
 * Generate dynamic styles based on configuration
 * This allows for easy theme changes without touching individual components
 */
export function getDynamicFormFieldStyles(isModified: boolean): string {
  if (!isModified) return "";

  const styles = [];

  if (FORM_STYLING_CONFIG.showRing) {
    styles.push(`ring-${FORM_STYLING_CONFIG.ringWidth}`);
    styles.push(`ring-${FORM_STYLING_CONFIG.modifiedColor}-200`);
  }

  styles.push(`border-${FORM_STYLING_CONFIG.modifiedColor}-400`);
  styles.push(`focus:border-${FORM_STYLING_CONFIG.modifiedColor}-500`);

  if (FORM_STYLING_CONFIG.showSpacing) {
    styles.push(`mt-${FORM_STYLING_CONFIG.topSpacing}`);
  }

  if (FORM_STYLING_CONFIG.showBackground) {
    styles.push(`bg-${FORM_STYLING_CONFIG.modifiedColor}-50/50`);
  }

  return styles.join(" ");
}
