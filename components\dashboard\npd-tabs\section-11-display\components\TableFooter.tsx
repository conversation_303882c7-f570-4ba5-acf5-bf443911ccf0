import React from "react";
import type { Table } from "@tanstack/react-table";

interface Competitor {
  productDetails: string;
  asin: string;
  url: string;
  imageUrl: string;
  brand: string;
  price: string;
  asinSales: string;
  asinRevenue: string;
  bsr: string;
  sellerCountry: string;
  fees: string;
  ratings: string;
  reviewCount: string;
  fulfillment: string;
  dimensions: string;
  weight: string;
  creationDate: string;
  isVisible?: boolean; // New field for visibility toggle
}

type TableRow = Competitor & { index: number; [key: string]: unknown };

interface TableFooterProps {
  table: Table<TableRow>;
  competitors: Competitor[];
  onCopy?: (text: string) => void;
}

export function TableFooter({ table, competitors, onCopy }: TableFooterProps) {
  // Filter out hidden competitors for calculations
  const visibleCompetitors = competitors.filter(
    (comp) => comp.isVisible !== false
  );

  // Helper to calculate median
  function median(arr: number[]): number | null {
    if (!arr.length) return null;
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    if (sorted.length % 2 === 0) {
      return (sorted[mid - 1] + sorted[mid]) / 2;
    } else {
      return sorted[mid];
    }
  }

  // Helper to calculate average
  function average(arr: number[]): number | null {
    if (!arr.length) return null;
    const sum = arr.reduce((a, b) => a + b, 0);
    return sum / arr.length;
  }

  // Gather values for calculations
  const annualRevenueArr = visibleCompetitors
    .map((comp) => {
      const revenue = parseFloat(
        (comp.asinRevenue || "").toString().replace(/[^\d.]/g, "")
      );
      return isNaN(revenue) ? 0 : revenue * 12;
    })
    .filter((v) => v > 0);

  const reviewCountArr = visibleCompetitors
    .map((comp) => {
      const reviews = parseFloat(
        (comp.reviewCount || "").toString().replace(/[^\d.]/g, "")
      );
      return isNaN(reviews) ? 0 : reviews;
    })
    .filter((v) => v > 0);

  const priceArr = visibleCompetitors
    .map((comp) => {
      const price = parseFloat(
        (comp.price || "").toString().replace(/[^\d.]/g, "")
      );
      return isNaN(price) ? 0 : price;
    })
    .filter((v) => v > 0);

  const salesToReviewArr = visibleCompetitors
    .map((comp) => {
      const sales = parseFloat(
        (comp.asinSales || "").toString().replace(/[^\d.]/g, "")
      );
      const reviews = parseFloat(
        (comp.reviewCount || "").toString().replace(/[^\d.]/g, "")
      );
      return !isNaN(sales) && !isNaN(reviews) && reviews > 0
        ? sales / reviews
        : null;
    })
    .filter((v): v is number => v !== null && !isNaN(v));

  const ratingsArr = visibleCompetitors
    .map((comp) => {
      const ratings = parseFloat(
        (comp.ratings || "").toString().replace(/[^\d.]/g, "")
      );
      return isNaN(ratings) ? 0 : ratings;
    })
    .filter((v) => v > 0);

  // Market share calculations (each competitor's % of total annual revenue)
  const totalAnnualRevenue = visibleCompetitors.reduce((sum, comp) => {
    const revenue = parseFloat(
      (comp.asinRevenue || "").toString().replace(/[^\d.]/g, "")
    );
    return sum + (isNaN(revenue) ? 0 : revenue * 12);
  }, 0);

  const marketShareArr = visibleCompetitors
    .map((comp) => {
      const revenue = parseFloat(
        (comp.asinRevenue || "").toString().replace(/[^\d.]/g, "")
      );
      const annualRevenue = isNaN(revenue) ? 0 : revenue * 12;
      return totalAnnualRevenue > 0
        ? (annualRevenue / totalAnnualRevenue) * 100
        : 0;
    })
    .filter((v) => v > 0);

  const renderStatRow = (
    type: "SUM" | "MEDIAN" | "AVERAGE",
    bgClass: string
  ) => {
    const headers = table.getFlatHeaders();
    const cells = [];

    for (let idx = 0; idx < headers.length; idx++) {
      const header = headers[idx];
      const colId =
        "id" in header.column
          ? header.column.id
          : "accessorKey" in header.column
          ? (header.column as { accessorKey: string }).accessorKey
          : undefined;

      let content = null;

      // Place label in the cell immediately before the first calculation (annualRevenue)
      if (headers[idx + 1]?.column.id === "annualRevenue") {
        content = (
          <span className="flex items-center justify-end font-semibold">
            {type}
          </span>
        );
      } else if (colId === "annualRevenue") {
        if (type === "SUM") {
          const totalAnnual = visibleCompetitors.reduce((sum, comp) => {
            const revenue = parseFloat(
              (comp.asinRevenue || "").toString().replace(/[^\d.]/g, "")
            );
            return sum + (isNaN(revenue) ? 0 : revenue * 12);
          }, 0);

          // Format as millions with 1 decimal place
          const totalInMillions = totalAnnual / 1000000;
          const formattedTotal =
            totalInMillions >= 1
              ? `$${totalInMillions.toFixed(1)}M`
              : `$${totalAnnual.toLocaleString()}`;

          content = <span className="font-semibold">{formattedTotal}</span>;
        } else if (type === "MEDIAN") {
          const med = median(annualRevenueArr);
          content = (
            <span className="font-semibold">
              {med !== null ? `$${med.toLocaleString()}` : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(annualRevenueArr);
          content = (
            <span className="font-semibold">
              {avg !== null
                ? `$${avg.toLocaleString(undefined, {
                    maximumFractionDigits: 0,
                  })}`
                : "-"}
            </span>
          );
        }
      } else if (colId === "reviewCount") {
        if (type === "SUM") {
          const totalReviews = visibleCompetitors.reduce((sum, comp) => {
            const reviews = parseFloat(
              (comp.reviewCount || "").toString().replace(/[^\d.]/g, "")
            );
            return sum + (isNaN(reviews) ? 0 : reviews);
          }, 0);
          content = (
            <span className="font-semibold">
              {totalReviews.toLocaleString()}
            </span>
          );
        } else if (type === "MEDIAN") {
          const med = median(reviewCountArr);
          content = (
            <span className="font-semibold">
              {med !== null ? med.toLocaleString() : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(reviewCountArr);
          content = (
            <span className="font-semibold">
              {avg !== null
                ? avg.toLocaleString(undefined, {
                    maximumFractionDigits: 0,
                  })
                : "-"}
            </span>
          );
        }
      } else if (colId === "marketShare" && type !== "SUM") {
        if (type === "MEDIAN") {
          const med = median(marketShareArr);
          content = (
            <span className="font-semibold">
              {med !== null ? `${med.toFixed(1)}%` : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(marketShareArr);
          content = (
            <span className="font-semibold">
              {avg !== null ? `${avg.toFixed(1)}%` : "-"}
            </span>
          );
        }
      } else if (colId === "salesToReviewRatio" && type !== "SUM") {
        if (type === "MEDIAN") {
          const med = median(salesToReviewArr);
          content = (
            <span className="font-semibold">
              {med !== null ? med.toFixed(1) : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(salesToReviewArr);
          content = (
            <span className="font-semibold">
              {avg !== null ? avg.toFixed(1) : "-"}
            </span>
          );
        }
      } else if (colId === "ratings" && type !== "SUM") {
        if (type === "MEDIAN") {
          const med = median(ratingsArr);
          content = (
            <span className="font-semibold">
              {med !== null ? `${med.toFixed(1)} ⭐` : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(ratingsArr);
          content = (
            <span className="font-semibold">
              {avg !== null ? `${avg.toFixed(1)} ⭐` : "-"}
            </span>
          );
        }
      } else if (colId === "price" && type !== "SUM") {
        if (type === "MEDIAN") {
          const med = median(priceArr);
          content = (
            <span className="font-semibold">
              {med !== null ? `$${med.toFixed(2)}` : "-"}
            </span>
          );
        } else if (type === "AVERAGE") {
          const avg = average(priceArr);
          content = (
            <span className="font-semibold">
              {avg !== null ? `$${avg.toFixed(2)}` : "-"}
            </span>
          );
        }
      }

      // Center align for salesToReviewRatio and ratings columns
      let alignClass = `border-r px-2 py-2 align-middle text-foreground ${bgClass}`;
      if (
        colId === "salesToReviewRatio" ||
        colId === "ratings" ||
        colId === "marketShare"
      ) {
        alignClass += " text-center";
      } else {
        alignClass += " text-right";
        if (idx === 0) alignClass += " text-left";
      }

      // Check if this is a label cell (SUM, MEDIAN, AVERAGE)
      const isLabelCell =
        headers[idx + 1]?.column.id === "annualRevenue" && content;

      // Wrap content in clickable div if onCopy is provided, content exists, and it's not a label cell
      const clickableContent =
        content && onCopy && !isLabelCell ? (
          <div
            className="cursor-pointer rounded px-1 border-2 border-transparent hover:border-blue-300 transition-all"
            onClick={() => {
              // Extract text content by getting the text from the element
              if (React.isValidElement(content)) {
                // For React elements, try to extract the text content
                const span = content as React.ReactElement<{
                  children: string;
                }>;
                const textContent = span.props.children;
                if (typeof textContent === "string") {
                  onCopy(textContent);
                }
              }
            }}
            title="Click to copy"
          >
            {content}
          </div>
        ) : content && !isLabelCell ? (
          <div className="rounded px-1 border-2 border-transparent hover:border-blue-300 transition-all">
            {content}
          </div>
        ) : (
          content
        );

      cells.push(
        <td
          key={header.id}
          className={alignClass}
          style={{
            width: header.getSize(),
            minWidth: header.getSize(),
            fontSize: "0.75rem",
          }}
        >
          {clickableContent}
        </td>
      );
    }
    return cells;
  };

  return (
    <tfoot>
      {/* Sum row */}
      <tr className="bg-muted/70 border-t" style={{ fontSize: "0.75rem" }}>
        {renderStatRow("SUM", "bg-muted/70")}
      </tr>

      {/* Median row */}
      <tr className="bg-muted/50 border-t" style={{ fontSize: "0.75rem" }}>
        {renderStatRow("MEDIAN", "bg-muted/50")}
      </tr>

      {/* Average row */}
      <tr className="bg-muted/40 border-t" style={{ fontSize: "0.75rem" }}>
        {renderStatRow("AVERAGE", "bg-muted/40")}
      </tr>
    </tfoot>
  );
}
