"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, UserCheck, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Spinner from "@/components/spinner";

export default function PendingApprovalPage() {
  const { user, isLoading, refetch } = useAuth();
  const router = useRouter();
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    // If not authenticated, redirect to home
    if (!isLoading && !user) {
      router.push("/");
      return;
    }

    // If user has roles, redirect to dashboard
    if (!isLoading && user?.roles && user.roles.length > 0) {
      router.push("/dashboard");
      return;
    }
  }, [user, isLoading, router]);

  const handleCheckStatus = async () => {
    setChecking(true);
    try {
      // First, refresh the token to get updated user data from database
      await fetch("/api/auth/refresh", {
        method: "POST",
      });

      // Then refetch the user data in the context
      await refetch();

      // The useEffect will handle redirection if user now has roles
    } catch (error) {
      console.error("Error refreshing status:", error);
    } finally {
      setChecking(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <Spinner loading={true} size={12} />
            <span className="ml-2">Checking status...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Don't render if no user (will redirect)
  if (!user) {
    return null;
  }

  // Don't render if user has roles (will redirect)
  if (user.roles && user.roles.length > 0) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-2xl">Pending Approval</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-center">
          <div className="space-y-2">
            <p className="text-muted-foreground">
              Welcome{" "}
              <span className="font-semibold text-foreground">{user.name}</span>
              !
            </p>
            <p className="text-sm text-muted-foreground">
              Your account has been created successfully, but you need to be
              assigned a role by an administrator before you can access the
              dashboard.
            </p>
          </div>

          <div className="rounded-lg bg-muted p-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserCheck className="h-4 w-4" />
              <span>Account Status: Awaiting Role Assignment</span>
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-xs text-muted-foreground">
              Please contact your system administrator to assign you the
              appropriate role. You&apos;ll receive access once your role has
              been configured.
            </p>

            <div className="flex flex-col gap-2">
              <Button asChild>
                <Link href="/">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Return to Home
                </Link>
              </Button>

              <Button
                variant="outline"
                onClick={handleCheckStatus}
                disabled={checking}
              >
                {checking ? "Checking..." : "Check Status"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
