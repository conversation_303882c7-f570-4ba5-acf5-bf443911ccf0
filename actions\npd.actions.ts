"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";
import { getCurrentUser } from "@/lib/auth";
import {
  createActivityLog,
  createProductActivityLog,
} from "@/actions/activity-log.actions";
import { createProductAssignmentNotification } from "@/lib/utils/notifications";
// Removed request cache import for now - will implement simpler caching
import type { ProductWithTabs } from "@/types";
import { getTabOrder } from "@/lib/constants/tab-order";
import type { Prisma } from "@/lib/generated/prisma";
import type { ProductStage } from "@/lib/constants/product-stages";
import { revalidatePath } from "next/cache";

// Fetch all NPD projects - requires authentication (serverless optimized)
export async function fetchAllNPDs() {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    // Optimized query: Get NPD projects and user subscriptions in parallel
    const [npds, userSubscriptions] = await Promise.all([
      prisma.nPDProduct.findMany({
        where: {
          archived: false, // Exclude archived NPD projects
        },
        select: {
          id: true,
          name: true,
          brand: true,
          slug: true,
          stage: true,
          description: true,
          imageUrl: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: [{ brand: "asc" }, { name: "asc" }],
      }),
      prisma.nPDProductSubscription.findMany({
        where: { userId: user.userId },
        select: { npdProductId: true },
      }),
    ]);

    // Create a Set for O(1) lookup performance
    const subscribedNPDIds = new Set(
      userSubscriptions.map((sub) => sub.npdProductId)
    );

    // Add isSubscribed field efficiently
    return npds.map((npd) => ({
      ...npd,
      isSubscribed: subscribedNPDIds.has(npd.id),
    }));
  } catch (error) {
    console.error("Error fetching NPD projects:", error);
    return [];
  }
}

// Fetch single NPD with full details including tabs
export async function fetchNPDById(
  id: string
): Promise<ProductWithTabs | null> {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    const npd = await prisma.nPDProduct.findFirst({
      where: {
        id,
        archived: false, // Exclude archived NPD projects
      },
      include: {
        tabs: {
          where: {
            archived: false, // Exclude archived tabs
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            order: "asc",
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        subscriptions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!npd) {
      return null;
    }

    // Add isSubscribed field
    const isSubscribed = npd.subscriptions.some(
      (sub) => sub.userId === user.userId
    );
    return {
      ...npd,
      isSubscribed,
    } as unknown as ProductWithTabs;
  } catch (error) {
    console.error("Error fetching NPD by ID:", error);
    return null;
  }
}

// Fetch single NPD by slug with full details
export async function fetchNPDBySlug(
  slug: string
): Promise<ProductWithTabs | null> {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    const product = await prisma.nPDProduct.findFirst({
      where: {
        slug,
        archived: false, // Exclude archived products
      },
      include: {
        tabs: {
          where: {
            archived: false, // Exclude archived tabs
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            order: "asc",
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        subscriptions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    // Add isSubscribed field
    const isSubscribed = product.subscriptions.some(
      (sub) => sub.userId === user.userId
    );
    return {
      ...product,
      isSubscribed,
    } as unknown as ProductWithTabs;
  } catch (error) {
    console.error("Error fetching product by slug:", error);
    return null;
  }
}

// Add a new tab to an NPD
export async function addTabToNPD(
  npdProductId: string,
  tabData: {
    tabName: string;
    order: number;
    fields: Record<string, unknown>;
  }
) {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, slug: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Check if tab with same name already exists (including archived)
    const existingTab = await prisma.nPDProductTab.findFirst({
      where: {
        npdProductId: npdProductId,
        tabName: tabData.tabName,
      },
    });

    // If an active tab exists, throw error
    if (existingTab && !existingTab.archived) {
      throw new Error(
        `Tab "${tabData.tabName}" already exists for this product`
      );
    }

    // If an archived tab exists, restore it instead of creating new one
    if (existingTab && existingTab.archived) {
      const restoredTab = await prisma.nPDProductTab.update({
        where: { id: existingTab.id },
        data: {
          archived: false,
          archivedAt: null,
          archivedBy: null,
          // Update with new data
          order: tabData.order,
          fields: tabData.fields as Prisma.JsonObject,
          userId: user.userId, // Set current user as creator
        },
      });

      // Create activity log for the restoration
      await createActivityLog({
        npdProductTabId: restoredTab.id,
        npdProductId: npdProductId,
        userId: user.userId,
        action: "restored",
        description: `Restored archived ${tabData.tabName} tab with new data`,
        metadata: {
          tabName: tabData.tabName,
          order: tabData.order,
          fieldCount: Object.keys(tabData.fields).length,
          restoredFrom: "auto_restore_on_create",
        },
      });

      // Auto-subscribe user to product when they restore a tab
      await autoSubscribeToNPD(npdProductId, user.userId);

      // Revalidate to update subscription status immediately
      revalidatePath(`/dashboard/npd/${product.slug}`);

      return {
        ...restoredTab,
        _isRestored: true,
        _message: `Restored archived "${tabData.tabName}" tab`,
      };
    }

    // Create the new tab
    const newTab = await prisma.nPDProductTab.create({
      data: {
        npdProductId: npdProductId,
        tabName: tabData.tabName,
        order: tabData.order,
        fields: tabData.fields as Prisma.JsonObject, // Prisma JSON type
      },
    });

    // Create activity log for the new tab
    await createActivityLog({
      npdProductTabId: newTab.id,
      npdProductId: npdProductId,
      userId: user.userId,
      action: "created",
      description: `Created ${tabData.tabName} tab with initial data`,
      metadata: {
        tabName: tabData.tabName,
        order: tabData.order,
        fieldCount: Object.keys(tabData.fields).length,
      },
    });

    // Auto-subscribe user to product when they create a tab
    await autoSubscribeToNPD(npdProductId, user.userId);

    // Revalidate to update subscription status immediately
    revalidatePath(`/dashboard/npd/${product.slug}`);

    return {
      ...newTab,
      _isRestored: false,
      _message: `Created new "${tabData.tabName}" tab`,
    };
  } catch (error) {
    console.error("Error adding tab to product:", error);
    throw error;
  }
}

// Fetch tab data by product ID and tab name
export async function fetchTabDataByName(
  npdProductId: string,
  tabName: string
): Promise<any | null> {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    const tab = await prisma.nPDProductTab.findFirst({
      where: {
        npdProductId,
        tabName,
        archived: false,
      },
      select: {
        id: true,
        tabName: true,
        fields: true,
        order: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return tab;
  } catch (error) {
    console.error("Error fetching tab data by name:", error);
    return null;
  }
}

// Update an existing tab's data
export async function updateTabData(
  npdProductTabId: string,
  fields: Record<string, unknown>
) {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    // Check if tab exists and user has access
    const existingTab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: { id: true, name: true, slug: true },
        },
      },
    });

    if (!existingTab) {
      throw new Error("Tab not found");
    }

    // Update the tab
    const updatedTab = await prisma.nPDProductTab.update({
      where: { id: npdProductTabId },
      data: {
        fields: fields as Prisma.JsonObject,
        updatedAt: new Date(),
      },
    });

    // Create activity log for the tab update
    await createActivityLog({
      npdProductTabId: npdProductTabId,
      npdProductId: existingTab.npdProductId,
      userId: user.userId,
      action: "updated",
      description: `Updated ${existingTab.tabName} tab data`,
      metadata: {
        tabName: existingTab.tabName,
        fieldCount: Object.keys(fields).length,
        productName: existingTab.npdProduct.name,
      },
    });

    // Auto-subscribe user to product when they edit a tab
    await autoSubscribeToNPD(existingTab.npdProductId, user.userId);

    // Revalidate to update subscription status immediately
    revalidatePath(`/dashboard/products/${existingTab.npdProduct.slug}`);

    return updatedTab;
  } catch (error) {
    console.error("Error updating tab data:", error);
    throw error;
  }
}

// Update NPD stage
export async function updateNPDStage(
  npdProductId: string,
  stage: ProductStage
) {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, stage: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Update the product stage
    const updatedProduct = await prisma.nPDProduct.update({
      where: { id: npdProductId },
      data: { stage },
    });

    // Create activity log for the stage change
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "updated",
      description: `Updated product stage from "${product.stage}" to "${stage}"`,
      metadata: {
        field: "stage",
        oldValue: product.stage,
        newValue: stage,
        productName: product.name,
      },
    });

    return { success: true, product: updatedProduct };
  } catch (error) {
    console.error("Error updating product stage:", error);
    throw error;
  }
}

// Update NPD brand
export async function updateNPDBrand(npdProductId: string, brand: string) {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, brand: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Update the product brand
    const updatedProduct = await prisma.nPDProduct.update({
      where: { id: npdProductId },
      data: { brand },
    });

    // Create activity log for the brand change
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "updated",
      description: `Updated product brand from "${product.brand}" to "${brand}"`,
      metadata: {
        field: "brand",
        oldValue: product.brand,
        newValue: brand,
        productName: product.name,
      },
    });

    return { success: true, product: updatedProduct };
  } catch (error) {
    console.error("Error updating product brand:", error);
    throw error;
  }
}

// Create new NPD - requires authentication
export async function createNPD(data: {
  name: string;
  brand: string;
  stage: string;
  description?: string;
  imageUrl?: string;
  subscribedUserIds?: string[];
}) {
  const user = await validateServerAction();

  try {
    // Generate a unique slug from the product name
    const baseSlug = data.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens

    // Check if slug already exists and make it unique if needed
    let slug = baseSlug;
    let counter = 1;

    while (await prisma.nPDProduct.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create the product
    const product = await prisma.nPDProduct.create({
      data: {
        name: data.name,
        brand: data.brand,
        stage: data.stage,
        slug: slug,
        userId: user.userId,
        ...(data.imageUrl && { imageUrl: data.imageUrl }),
        ...(data.description && { description: data.description }),
      },
      include: {
        tabs: {
          where: {
            archived: false, // Exclude archived tabs
          },
          orderBy: {
            order: "asc",
          },
        },
      },
    });

    // Auto-subscribe the creator and any additional specified users
    const subscribedUserIds = Array.from(
      new Set([user.userId, ...(data.subscribedUserIds || [])])
    );

    if (subscribedUserIds.length > 0) {
      await prisma.nPDProductSubscription.createMany({
        data: subscribedUserIds.map((userId) => ({
          userId,
          npdProductId: product.id,
        })),
        skipDuplicates: true,
      });
    }

    // Create activity log for the product creation
    await createProductActivityLog({
      npdProductId: product.id,
      userId: user.userId,
      action: "created",
      description: `Created product "${product.name}" with brand "${product.brand}" and stage "${product.stage}"`,
      metadata: {
        field: "product",
        newValue: {
          name: product.name,
          brand: product.brand,
          stage: product.stage,
          slug: product.slug,
        },
        productName: product.name,
      },
    });

    // Auto-create Overview tab for the new product
    const overviewTabData = {
      keyActions: [],
      dependencies: [],
      nextSteps: [],
      images: [],
    };

    const overviewTab = await prisma.nPDProductTab.create({
      data: {
        npdProductId: product.id,
        tabName: "Overview",
        order: getTabOrder("Overview"),
        fields: overviewTabData as Prisma.JsonObject,
      },
    });

    // Note: No activity log for auto-created Overview tab to avoid clutter
    // Activity logs will be created when Overview tab is manually added/restored later

    // Return the product with the newly created Overview tab
    const productWithOverviewTab = {
      ...product,
      tabs: [overviewTab],
    };

    return {
      success: true,
      product: productWithOverviewTab as ProductWithTabs,
    };
  } catch (error) {
    console.error("Error creating product:", error);
    throw error;
  }
}

// Auto-subscribe user to NPD (silent, used for tab creation/editing)
export async function autoSubscribeToNPD(
  npdProductId: string,
  userId?: string
) {
  try {
    const currentUser = await getCurrentUser();
    if (!currentUser) return;

    const userIdToSubscribe = userId || currentUser.userId;

    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, slug: true },
    });

    if (!product) return;

    // Check if user is already subscribed
    const existingSubscription = await prisma.nPDProductSubscription.findUnique(
      {
        where: {
          userId_npdProductId: {
            userId: userIdToSubscribe,
            npdProductId: npdProductId,
          },
        },
      }
    );

    if (existingSubscription) return; // Already subscribed

    // Create subscription
    await prisma.nPDProductSubscription.create({
      data: {
        userId: userIdToSubscribe,
        npdProductId: npdProductId,
      },
    });

    // Create notification for auto-subscription (same as manual subscription)
    try {
      await createProductAssignmentNotification(
        userIdToSubscribe,
        product.name,
        product.slug
      );
    } catch (notificationError) {
      console.error(
        `🔔 Auto-subscription successful but notification failed for user ${userIdToSubscribe}:`,
        notificationError
      );
    }
  } catch (error) {
    // Fail silently for auto-subscription
    console.error("Error auto-subscribing to product:", error);
  }
}

// Subscribe a user to an NPD
export async function subscribeToNPD(npdProductId: string) {
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, slug: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Check if user is already subscribed
    const existingSubscription = await prisma.nPDProductSubscription.findUnique(
      {
        where: {
          userId_npdProductId: {
            userId: user.userId,
            npdProductId: npdProductId,
          },
        },
      }
    );

    if (existingSubscription) {
      return { success: true, message: "Already subscribed" };
    }

    // Create subscription
    await prisma.nPDProductSubscription.create({
      data: {
        userId: user.userId,
        npdProductId: npdProductId,
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "subscribed",
      description: `Subscribed to product "${product.name}"`,
      metadata: {
        field: "subscription",
        newValue: "subscribed",
        productName: product.name,
      },
    });

    // Create notification for the user
    try {
      await createProductAssignmentNotification(
        user.userId,
        product.name,
        product.slug || product.id
      );
    } catch (notificationError) {
      console.error(
        `🔔 [SUBSCRIPTION DEBUG] Failed to create subscription notification:`,
        notificationError
      );
      if (notificationError instanceof Error) {
        console.error(
          `🔔 [SUBSCRIPTION DEBUG] Error name: ${notificationError.name}`
        );
        console.error(
          `🔔 [SUBSCRIPTION DEBUG] Error message: ${notificationError.message}`
        );
        console.error(
          `🔔 [SUBSCRIPTION DEBUG] Error stack: ${notificationError.stack}`
        );
      }
    }
    return { success: true, message: "Subscribed successfully" };
  } catch (error) {
    console.error(
      "🔔 [SUBSCRIPTION DEBUG] Error subscribing to product:",
      error
    );
    if (error instanceof Error) {
      console.error("🔔 [SUBSCRIPTION DEBUG] Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }
    throw error;
  }
}

// Unsubscribe a user from an NPD
export async function unsubscribeFromNPD(npdProductId: string) {
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Check if user is subscribed
    const existingSubscription = await prisma.nPDProductSubscription.findUnique(
      {
        where: {
          userId_npdProductId: {
            userId: user.userId,
            npdProductId: npdProductId,
          },
        },
      }
    );

    if (!existingSubscription) {
      return { success: true, message: "Not subscribed" };
    }

    // Delete subscription
    await prisma.nPDProductSubscription.delete({
      where: {
        userId_npdProductId: {
          userId: user.userId,
          npdProductId: npdProductId,
        },
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "unsubscribed",
      description: `Unsubscribed from product "${product.name}"`,
      metadata: {
        field: "subscription",
        newValue: "unsubscribed",
        productName: product.name,
      },
    });

    return { success: true, message: "Unsubscribed successfully" };
  } catch (error) {
    console.error("Error unsubscribing from product:", error);
    throw error;
  }
}

// Add multiple users to an NPD subscription (for settings page)
export async function addUsersToNPDSubscription(
  npdProductId: string,
  userIds: string[]
) {
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true, slug: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Create subscriptions for all users
    await prisma.nPDProductSubscription.createMany({
      data: userIds.map((userId) => ({
        userId,
        npdProductId: npdProductId,
      })),
      skipDuplicates: true,
    });

    // Create notifications for each newly subscribed user

    for (const userId of userIds) {
      try {
        await createProductAssignmentNotification(
          userId,
          product.name,
          product.slug
        );
        // Notification sent successfully
      } catch (notificationError) {
        console.error(
          `Failed to create notification for user ${userId}:`,
          notificationError
        );
      }
    }

    // Create activity log
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "updated",
      description: `Added ${userIds.length} users to product subscription`,
      metadata: {
        field: "subscriptions",
        newValue: userIds,
        productName: product.name,
      },
    });

    return { success: true, message: "Users added successfully" };
  } catch (error) {
    console.error("Error adding users to product subscription:", error);
    throw error;
  }
}

// Remove multiple users from an NPD subscription (for settings page)
export async function removeUsersFromNPDSubscription(
  npdProductId: string,
  userIds: string[]
) {
  const user = await validateServerAction();

  try {
    // Check if product exists
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: { id: true, name: true },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Remove subscriptions for all users
    await prisma.nPDProductSubscription.deleteMany({
      where: {
        npdProductId: npdProductId,
        userId: {
          in: userIds,
        },
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId: npdProductId,
      userId: user.userId,
      action: "updated",
      description: `Removed ${userIds.length} users from product subscription`,
      metadata: {
        field: "subscriptions",
        newValue: userIds,
        productName: product.name,
      },
    });

    return { success: true, message: "Users removed successfully" };
  } catch (error) {
    console.error("Error removing users from product subscription:", error);
    throw error;
  }
}

// Atomic server action to update all NPD settings in a single transaction
export async function updateNPDSettings(
  npdProductId: string,
  updates: {
    brand?: string;
    stage?: ProductStage;
    name?: string;
    slug?: string;
    description?: string;
    imageUrl?: string;
    usersToAdd?: string[];
    usersToRemove?: string[];
  }
) {
  // Validate that user is authenticated
  const user = await validateServerAction();

  try {
    const result = await prisma.$transaction(async (tx) => {
      // First, get the current product to compare changes
      const currentProduct = await tx.nPDProduct.findUnique({
        where: { id: npdProductId },
        include: {
          subscriptions: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!currentProduct) {
        throw new Error("Product not found");
      }

      const changes: Record<string, { from: string; to: string }> = {};
      const updateData: {
        brand?: string;
        stage?: ProductStage;
        name?: string;
        slug?: string;
        description?: string;
        imageUrl?: string;
      } = {};

      // Track changes for basic fields
      if (updates.brand && updates.brand !== currentProduct.brand) {
        changes.brand = { from: currentProduct.brand, to: updates.brand };
        updateData.brand = updates.brand;
      }

      if (updates.stage && updates.stage !== currentProduct.stage) {
        changes.stage = { from: currentProduct.stage, to: updates.stage };
        updateData.stage = updates.stage;
      }

      if (updates.name && updates.name !== currentProduct.name) {
        changes.name = { from: currentProduct.name, to: updates.name };
        updateData.name = updates.name;
      }

      if (updates.slug && updates.slug !== currentProduct.slug) {
        // Validate slug uniqueness before updating
        const existingProduct = await tx.nPDProduct.findUnique({
          where: { slug: updates.slug },
        });

        if (existingProduct) {
          throw new Error(
            `A product with the slug "${updates.slug}" already exists. Please choose a different slug.`
          );
        }

        changes.slug = { from: currentProduct.slug, to: updates.slug };
        updateData.slug = updates.slug;
      }

      if (
        updates.description !== undefined &&
        updates.description !== currentProduct.description
      ) {
        changes.description = {
          from: currentProduct.description || "",
          to: updates.description,
        };
        updateData.description = updates.description;
      }

      if (
        updates.imageUrl !== undefined &&
        updates.imageUrl !== currentProduct.imageUrl
      ) {
        changes.imageUrl = {
          from: currentProduct.imageUrl || "",
          to: updates.imageUrl,
        };
        updateData.imageUrl = updates.imageUrl;
      }

      // Update basic product fields if there are any changes
      let updatedProduct = currentProduct;
      if (Object.keys(updateData).length > 0) {
        updatedProduct = await tx.nPDProduct.update({
          where: { id: npdProductId },
          data: updateData,
          include: {
            subscriptions: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        });
      }

      // Handle subscription changes
      const subscriptionChanges: string[] = [];

      // Add new users to subscriptions
      if (updates.usersToAdd && updates.usersToAdd.length > 0) {
        // Check which users aren't already subscribed
        const currentSubscriberIds = new Set(
          currentProduct.subscriptions.map((sub) => sub.userId)
        );
        const newUserIds = updates.usersToAdd.filter(
          (userId) => !currentSubscriberIds.has(userId)
        );

        if (newUserIds.length > 0) {
          await tx.nPDProductSubscription.createMany({
            data: newUserIds.map((userId) => ({
              npdProductId: npdProductId,
              userId,
            })),
            skipDuplicates: true,
          });

          // Get user names for activity log
          const newUsers = await tx.user.findMany({
            where: { id: { in: newUserIds } },
            select: { name: true },
          });

          subscriptionChanges.push(
            `Added ${newUsers.map((u) => u.name).join(", ")} to subscriptions`
          );

          // Create notifications for each newly subscribed user
          for (const userId of newUserIds) {
            try {
              await createProductAssignmentNotification(
                userId,
                currentProduct.name,
                currentProduct.slug
              );
              // Notification sent successfully
            } catch (notificationError) {
              console.error(
                `Failed to create notification for user ${userId}:`,
                notificationError
              );
            }
          }
        }
      }

      // Remove users from subscriptions
      if (updates.usersToRemove && updates.usersToRemove.length > 0) {
        // Get user names before deletion for activity log
        const usersToRemove = await tx.user.findMany({
          where: { id: { in: updates.usersToRemove } },
          select: { name: true },
        });

        await tx.nPDProductSubscription.deleteMany({
          where: {
            npdProductId: npdProductId,
            userId: { in: updates.usersToRemove },
          },
        });

        if (usersToRemove.length > 0) {
          subscriptionChanges.push(
            `Removed ${usersToRemove
              .map((u) => u.name)
              .join(", ")} from subscriptions`
          );
        }
      }

      // Create activity logs for all changes
      const activityPromises: Promise<unknown>[] = [];

      // Log basic field changes
      for (const [field, change] of Object.entries(changes)) {
        activityPromises.push(
          createProductActivityLog({
            npdProductId,
            userId: user.userId,
            action: "updated",
            description: `Updated ${field} from "${change.from}" to "${change.to}"`,
            changes: { [field]: change },
            metadata: { field },
          })
        );
      }

      // Log subscription changes
      for (const changeDescription of subscriptionChanges) {
        activityPromises.push(
          createProductActivityLog({
            npdProductId,
            userId: user.userId,
            action: "updated",
            description: changeDescription,
            changes: { subscriptions: changeDescription },
            metadata: { field: "subscription" },
          })
        );
      }

      // Wait for all activity logs to be created
      await Promise.all(activityPromises);

      return {
        success: true,
        updatedProduct,
        changesCount: Object.keys(changes).length + subscriptionChanges.length,
      };
    });

    return result;
  } catch (error) {
    console.error("Error updating product settings:", error);
    throw error;
  }
}

// Archive an NPD (soft delete) - requires authentication and ownership or admin role
export async function deleteNPD(npdProductId: string) {
  // Import the archiveProduct function to maintain backward compatibility
  const { archiveProduct } = await import("@/actions/npd-deletion.actions");
  return await archiveProduct(npdProductId);
}

// Validate NPD slug uniqueness
export async function validateNPDSlug(
  slug: string,
  currentId?: string
): Promise<{ exists: boolean }> {
  // Validate authentication
  await validateServerAction();

  if (!slug) {
    throw new Error("Slug is required");
  }

  try {
    // Check if a product with this slug exists
    const existingProduct = await prisma.nPDProduct.findUnique({
      where: { slug },
      select: { id: true },
    });

    // If product exists and it's not the current product being edited
    const exists = existingProduct && existingProduct.id !== currentId;

    return { exists: !!exists };
  } catch (error) {
    console.error("Error validating slug:", error);
    throw new Error("Failed to validate slug");
  }
}
