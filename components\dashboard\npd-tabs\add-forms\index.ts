// Export all add tab form components
export { AddOverviewTab } from "./add-overview-tab";
export { AddSection11Tab } from "./add-section-11-tab";
export { AddSection12Tab } from "./add-section-12-tab";
export { AddSection13Tab } from "./add-section-13-tab";
export { AddSourcingBriefTab } from "./add-sourcing-brief-tab";

import { AddOverviewTab } from "./add-overview-tab";
import { AddSection11Tab } from "./add-section-11-tab";
import { AddSection12Tab } from "./add-section-12-tab";
import { AddSection13Tab } from "./add-section-13-tab";
import { AddSourcingBriefTab } from "./add-sourcing-brief-tab";

// Tab component mapping for dynamic imports
export const TAB_COMPONENTS = {
  Overview: AddOverviewTab,
  "1.1": AddSection11Tab,
  "1.2": AddSection12Tab,
  "1.3": AddSection13Tab,
  "Sourcing Brief": AddSourcingBriefTab,
} as const;

export type TabComponentName = keyof typeof TAB_COMPONENTS;
