import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

// PUT /api/admin/roles/[id] - Update role
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await validateServerAction();
    const { id } = await params;
    
    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(role => role.name === "SUPER_ADMIN");
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    const { name, permissionIds } = await request.json();

    if (!name || typeof name !== "string") {
      return NextResponse.json(
        { message: "Role name is required" },
        { status: 400 }
      );
    }

    // Validate role name format
    const roleName = name.trim().toUpperCase();
    if (!/^[A-Z_&]+$/.test(roleName)) {
      return NextResponse.json(
        { message: "Role name must contain only uppercase letters, underscores, and ampersands" },
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 404 }
      );
    }

    // Check if another role with the same name exists
    const duplicateRole = await prisma.role.findFirst({
      where: {
        name: roleName,
        id: { not: id },
      },
    });

    if (duplicateRole) {
      return NextResponse.json(
        { message: "Role with this name already exists" },
        { status: 409 }
      );
    }

    // Validate permission IDs if provided
    if (permissionIds && Array.isArray(permissionIds) && permissionIds.length > 0) {
      const validPermissions = await prisma.permission.findMany({
        where: { id: { in: permissionIds } },
      });

      if (validPermissions.length !== permissionIds.length) {
        return NextResponse.json(
          { message: "One or more permission IDs are invalid" },
          { status: 400 }
        );
      }
    }

    const role = await prisma.role.update({
      where: { id },
      data: {
        name: roleName,
        permissions: {
          set: permissionIds && permissionIds.length > 0 
            ? permissionIds.map((permId: string) => ({ id: permId }))
            : [],
        },
      },
      include: {
        permissions: {
          select: {
            id: true,
            code: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    return NextResponse.json(role);
  } catch (error) {
    console.error("Error updating role:", error);
    return NextResponse.json(
      { message: "Failed to update role" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/roles/[id] - Delete role
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await validateServerAction();
    const { id } = await params;
    
    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(role => role.name === "SUPER_ADMIN");
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        users: true,
      },
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "Role not found" },
        { status: 404 }
      );
    }

    // Check if role is being used by any users
    if (existingRole.users.length > 0) {
      return NextResponse.json(
        { 
          message: `Cannot delete role. It is currently assigned to ${existingRole.users.length} user(s).`,
          users: existingRole.users.map(user => user.name)
        },
        { status: 409 }
      );
    }

    // Prevent deletion of SUPER_ADMIN role
    if (existingRole.name === "SUPER_ADMIN") {
      return NextResponse.json(
        { message: "Cannot delete SUPER_ADMIN role" },
        { status: 409 }
      );
    }

    await prisma.role.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Role deleted successfully" });
  } catch (error) {
    console.error("Error deleting role:", error);
    return NextResponse.json(
      { message: "Failed to delete role" },
      { status: 500 }
    );
  }
}
