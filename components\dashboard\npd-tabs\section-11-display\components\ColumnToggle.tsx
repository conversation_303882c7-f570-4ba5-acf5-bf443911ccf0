"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Settings2, RotateCcw } from "lucide-react";
import { columnDisplayNames } from "../utils/columnSettings";

interface ColumnToggleProps {
  columnVisibility: Record<string, boolean>;
  onVisibilityChange: (visibility: Record<string, boolean>) => void;
  onResetVisibility: () => void;
  onResetWidths: () => void;
}

export function ColumnToggle({
  columnVisibility,
  onVisibilityChange,
  onResetVisibility,
  onResetWidths,
}: ColumnToggleProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggleColumn = (columnId: string, checked: boolean) => {
    const newVisibility = {
      ...columnVisibility,
      [columnId]: checked,
    };
    onVisibilityChange(newVisibility);
  };

  const handleToggleAll = (checked: boolean) => {
    const newVisibility = Object.keys(columnVisibility).reduce((acc, key) => {
      acc[key] = checked;
      return acc;
    }, {} as Record<string, boolean>);
    onVisibilityChange(newVisibility);
  };

  const visibleCount = Object.values(columnVisibility).filter(Boolean).length;
  const totalCount = Object.keys(columnVisibility).length;

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Settings2 className="h-4 w-4" />
          Columns ({visibleCount}/{totalCount})
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-80 sm:w-96 p-6">
        <SheetTitle>Column Settings</SheetTitle>
        <SheetDescription className="text-sm text-muted-foreground mb-4">
          Show or hide columns to customize your table view. Your preferences
          will be saved automatically.
        </SheetDescription>

        <div className="space-y-4">
          {/* Reset buttons */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onResetVisibility}
                className="flex items-center gap-2 flex-1"
              >
                <RotateCcw className="h-4 w-4" />
                Reset Visibility
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onResetWidths}
                className="flex items-center gap-2 flex-1"
              >
                <RotateCcw className="h-4 w-4" />
                Reset Widths
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Reset column visibility or widths
            </p>
          </div>

          {/* Toggle all */}
          <div className="border-b pb-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="toggle-all"
                checked={visibleCount === totalCount}
                onCheckedChange={handleToggleAll}
              />
              <label
                htmlFor="toggle-all"
                className="text-sm font-medium cursor-pointer"
              >
                {visibleCount === totalCount ? "Hide All" : "Show All"}
              </label>
            </div>
          </div>

          {/* Individual column toggles - Two column layout */}
          <div className="max-h-96 overflow-y-auto">
            <div className="grid grid-cols-2 gap-x-6">
              {(() => {
                const columnEntries = Object.entries(columnVisibility);
                const midPoint = Math.ceil(columnEntries.length / 2);
                const firstHalf = columnEntries.slice(0, midPoint);
                const secondHalf = columnEntries.slice(midPoint);

                return (
                  <>
                    {/* First Column */}
                    <div className="space-y-3">
                      {firstHalf.map(([columnId, isVisible]) => (
                        <div
                          key={columnId}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={columnId}
                            checked={isVisible}
                            onCheckedChange={(checked) =>
                              handleToggleColumn(columnId, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={columnId}
                            className="text-sm cursor-pointer flex-1 truncate"
                            title={columnDisplayNames[columnId] || columnId}
                          >
                            {columnDisplayNames[columnId] || columnId}
                          </label>
                        </div>
                      ))}
                    </div>

                    {/* Second Column */}
                    <div className="space-y-3">
                      {secondHalf.map(([columnId, isVisible]) => (
                        <div
                          key={columnId}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={columnId}
                            checked={isVisible}
                            onCheckedChange={(checked) =>
                              handleToggleColumn(columnId, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={columnId}
                            className="text-sm cursor-pointer flex-1 truncate"
                            title={columnDisplayNames[columnId] || columnId}
                          >
                            {columnDisplayNames[columnId] || columnId}
                          </label>
                        </div>
                      ))}
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
        </div>

        {/* Summary footer */}
        <div className="mt-6 pt-4 border-t">
          <div className="text-xs text-muted-foreground">
            <p>
              Showing {visibleCount} of {totalCount} columns
            </p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
