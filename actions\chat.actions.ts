"use server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";
import { revalidatePath } from "next/cache";
import { createUserMentionNotification } from "@/lib/utils/notifications";

// Types for chat operations
export interface ChatMessage {
  id: string;
  npdProductId: string;
  userId: string;
  message: string;
  mentions: string[];
  createdAt: string; // ISO string for compatibility with shared components
  updatedAt: string; // ISO string for compatibility with shared components
  user: {
    id: string;
    email: string;
    name: string;
    image?: string | null;
  };
  isRead: boolean;
  readAt?: string | null; // ISO string for compatibility
}

export interface ChatMessagesResponse {
  messages: ChatMessage[];
  hasMore: boolean;
}

export interface ChatUnreadData {
  unreadCount: number;
  totalCount: number;
  readCount: number;
}

// Get chat messages for a product
export async function getChatMessages(
  npdProductId: string,
  limit: number = 50,
  before?: string
): Promise<ChatMessagesResponse> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (!npdProductId) {
    throw new Error("npdProductId is required");
  }

  // Verify product exists
  const product = await prisma.nPDProduct.findUnique({
    where: { id: npdProductId },
  });

  if (!product) {
    throw new Error("Product not found");
  }

  // Build where condition for pagination
  const whereCondition = {
    npdProductId: npdProductId,
    ...(before && { createdAt: { lt: new Date(before) } }),
  };

  const messages = await prisma.nPDProductChat.findMany({
    where: whereCondition,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
        },
      },
      readStatus: {
        where: { userId: currentUser.userId },
        select: {
          readAt: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: limit,
  });

  // Transform messages to include read status
  const messagesWithReadStatus: ChatMessage[] = messages.map((message) => ({
    id: message.id,
    npdProductId: message.npdProductId,
    userId: message.userId,
    message: message.message,
    mentions: message.mentions,
    createdAt: message.createdAt.toISOString(),
    updatedAt: message.updatedAt.toISOString(),
    user: message.user,
    isRead: message.readStatus.length > 0,
    readAt: message.readStatus[0]?.readAt?.toISOString() || null,
  }));

  return {
    messages: messagesWithReadStatus.reverse(), // Return in chronological order
    hasMore: messages.length === limit,
  };
}

// Send a new chat message
export async function sendChatMessage(
  npdProductId: string,
  message: string,
  mentions: string[] = []
): Promise<ChatMessage> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  // Ensure user exists in database
  let userExists;
  try {
    // First check if user exists by ID
    userExists = await prisma.user.findUnique({
      where: { id: currentUser.userId },
      select: { id: true, name: true, email: true },
    });

    if (!userExists) {
      // Check if user exists by email (might be duplicate email issue)
      const existingUserByEmail = await prisma.user.findUnique({
        where: { email: currentUser.email },
        select: { id: true, name: true, email: true },
      });

      if (existingUserByEmail) {
        // Use the existing user instead of creating a new one
        userExists = existingUserByEmail;
      } else {
        // Create new user
        userExists = await prisma.user.create({
          data: {
            id: currentUser.userId,
            name: currentUser.name,
            email: currentUser.email,
            image: currentUser.image,
            provider: currentUser.provider,
            lastLoginAt: new Date(),
          },
          select: { id: true, name: true, email: true },
        });
      }
    } else {
      // Update last login time for existing user
      await prisma.user.update({
        where: { id: currentUser.userId },
        data: { lastLoginAt: new Date() },
      });
    }
  } catch (userError) {
    console.error("Failed to ensure user exists:", userError);
    throw new Error("Database user synchronization failed. Please try again.");
  }

  if (
    !npdProductId ||
    !message ||
    typeof message !== "string" ||
    message.trim().length === 0
  ) {
    throw new Error("npdProductId and message are required");
  }

  if (!Array.isArray(mentions)) {
    throw new Error("mentions must be an array");
  }

  // Verify product exists
  const product = await prisma.nPDProduct.findUnique({
    where: { id: npdProductId },
  });

  if (!product) {
    throw new Error("Product not found");
  }

  // Create the chat message (use the actual user ID from database)
  const chatMessage = await prisma.nPDProductChat.create({
    data: {
      npdProductId: npdProductId,
      userId: userExists.id,
      message: message.trim(),
      mentions,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
        },
      },
    },
  });

  // Automatically mark as read for the sender
  await prisma.nPDProductChatReadStatus.create({
    data: {
      userId: currentUser.userId,
      chatId: chatMessage.id,
      npdProductId: npdProductId,
    },
  });

  // Create notifications for mentioned users
  if (mentions.length > 0) {
    try {
      // Find users by their names from the mentions array
      const mentionedUsers = await prisma.user.findMany({
        where: {
          name: {
            in: mentions,
          },
        },
        select: {
          id: true,
          name: true,
        },
      });

      // Create notifications for each mentioned user (except the sender)
      const notificationPromises = mentionedUsers
        .filter((user) => user.id !== currentUser.userId) // Don't notify yourself
        .map((user) =>
          createUserMentionNotification(
            user.id,
            userExists.name || currentUser.name,
            `${product.name} chat`,
            `/dashboard/products/${product.slug}?openChat=true&messageId=${chatMessage.id}`,
            chatMessage.id,
            message
          )
        );

      await Promise.all(notificationPromises);
    } catch (error) {
      console.error("Error creating mention notifications:", error);
      // Don't fail the message send if notifications fail
    }
  }

  // Revalidate the product page to show new message
  revalidatePath(`/dashboard/products/${product.slug}`);

  return {
    ...chatMessage,
    createdAt: chatMessage.createdAt.toISOString(),
    updatedAt: chatMessage.updatedAt.toISOString(),
    isRead: true,
    readAt: new Date().toISOString(),
  };
}

// Update a chat message
export async function updateChatMessage(
  messageId: string,
  message: string,
  mentions: string[] = []
): Promise<ChatMessage> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (
    !messageId ||
    !message ||
    typeof message !== "string" ||
    message.trim().length === 0
  ) {
    throw new Error("messageId and message are required");
  }

  if (!Array.isArray(mentions)) {
    throw new Error("mentions must be an array");
  }

  // Verify user owns this message
  const existingMessage = await prisma.nPDProductChat.findFirst({
    where: {
      id: messageId,
      userId: currentUser.userId,
    },
    include: {
      npdProduct: {
        select: {
          id: true,
          slug: true,
        },
      },
    },
  });

  if (!existingMessage) {
    throw new Error("Message not found or access denied");
  }

  // Update the message
  const updatedMessage = await prisma.nPDProductChat.update({
    where: { id: messageId },
    data: {
      message: message.trim(),
      mentions,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
        },
      },
      readStatus: {
        where: { userId: currentUser.userId },
        select: {
          readAt: true,
        },
      },
    },
  });

  // Revalidate the product page
  revalidatePath(`/dashboard/products/${existingMessage.npdProduct.slug}`);

  return {
    ...updatedMessage,
    createdAt: updatedMessage.createdAt.toISOString(),
    updatedAt: updatedMessage.updatedAt.toISOString(),
    isRead: updatedMessage.readStatus.length > 0,
    readAt: updatedMessage.readStatus[0]?.readAt?.toISOString() || null,
  };
}

// Delete a chat message
export async function deleteChatMessage(messageId: string): Promise<void> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (!messageId) {
    throw new Error("messageId is required");
  }

  // Verify user owns this message
  const existingMessage = await prisma.nPDProductChat.findFirst({
    where: {
      id: messageId,
      userId: currentUser.userId,
    },
    include: {
      npdProduct: {
        select: {
          slug: true,
        },
      },
    },
  });

  if (!existingMessage) {
    throw new Error("Message not found or access denied");
  }

  // Delete the message (this will cascade delete read statuses)
  await prisma.nPDProductChat.delete({
    where: { id: messageId },
  });

  // Revalidate the product page
  revalidatePath(`/dashboard/npd/${existingMessage.npdProduct.slug}`);
}

// Get unread count for chat messages in a product
export async function getChatUnreadCount(
  npdProductId: string
): Promise<ChatUnreadData> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (!npdProductId) {
    throw new Error("npdProductId is required");
  }

  // Get total chat messages count
  const totalCount = await prisma.nPDProductChat.count({
    where: { npdProductId: npdProductId },
  });

  // Get read chat messages count for this user
  const readCount = await prisma.nPDProductChatReadStatus.count({
    where: {
      userId: currentUser.userId,
      npdProductId: npdProductId,
    },
  });

  const unreadCount = Math.max(0, totalCount - readCount);

  return {
    unreadCount,
    totalCount,
    readCount,
  };
}

// Mark chat message as read or unread
export async function updateChatReadStatus(
  chatId: string,
  npdProductId: string,
  isRead: boolean
): Promise<void> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (!chatId || !npdProductId) {
    throw new Error("chatId and npdProductId are required");
  }

  // Verify the chat message exists and belongs to the specified product
  const chatMessage = await prisma.nPDProductChat.findFirst({
    where: {
      id: chatId,
      npdProductId: npdProductId,
    },
    include: {
      npdProduct: {
        select: {
          slug: true,
        },
      },
    },
  });

  if (!chatMessage) {
    throw new Error("Chat message not found");
  }

  if (isRead) {
    // Mark as read - create or update read status
    await prisma.nPDProductChatReadStatus.upsert({
      where: {
        userId_chatId: {
          userId: currentUser.userId,
          chatId,
        },
      },
      update: {
        readAt: new Date(),
      },
      create: {
        userId: currentUser.userId,
        chatId,
        npdProductId: npdProductId,
        readAt: new Date(),
      },
    });
  } else {
    // Mark as unread - delete read status
    await prisma.nPDProductChatReadStatus.deleteMany({
      where: {
        userId: currentUser.userId,
        chatId,
      },
    });
  }

  // Revalidate the product page
  revalidatePath(`/dashboard/products/${chatMessage.npdProduct.slug}`);
}

// Mark all chat messages as read for a product
export async function markAllChatMessagesAsRead(
  npdProductId: string
): Promise<void> {
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    throw new Error("Unauthorized");
  }

  if (!npdProductId) {
    throw new Error("npdProductId is required");
  }

  // Verify NPD exists
  const product = await prisma.nPDProduct.findUnique({
    where: { id: npdProductId },
    select: { slug: true },
  });

  if (!product) {
    throw new Error("NPD not found");
  }

  // Get all chat messages for this NPD
  const chatMessages = await prisma.nPDProductChat.findMany({
    where: { npdProductId: npdProductId },
    select: { id: true },
  });

  // Create read status for each chat message that doesn't have one
  const readStatusData = chatMessages.map((message) => ({
    userId: currentUser.userId,
    chatId: message.id,
    npdProductId: npdProductId,
    readAt: new Date(),
  }));

  // Use createMany with skipDuplicates to avoid conflicts
  await prisma.nPDProductChatReadStatus.createMany({
    data: readStatusData,
    skipDuplicates: true,
  });

  // Revalidate the product page
  revalidatePath(`/dashboard/products/${product.slug}`);
}
