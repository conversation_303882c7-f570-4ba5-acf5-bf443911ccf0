import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";

// GET - Fetch notifications for the current user
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const unreadOnly = searchParams.get("unreadOnly") === "true";
    const includeHidden = searchParams.get("includeHidden") === "true";
    const limit = parseInt(searchParams.get("limit") || "10");

    const notifications = await prisma.notification.findMany({
      where: {
        userId: currentUser.userId,
        ...(unreadOnly && { read: false }),
        ...(!includeHidden && { hidden: false }),
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });

    return NextResponse.json({ notifications });
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Failed to fetch notifications" },
      { status: 500 }
    );
  }
}

// POST - Mark notifications as read or toggle hidden status
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { notificationIds, action } = await request.json();

    if (!Array.isArray(notificationIds)) {
      return NextResponse.json(
        { error: "notificationIds must be an array" },
        { status: 400 }
      );
    }

    // Default action is to mark as read
    const actionType = action || "read";

    if (actionType === "read") {
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: currentUser.userId,
        },
        data: {
          read: true,
        },
      });
    } else if (actionType === "unread") {
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: currentUser.userId,
        },
        data: {
          read: false,
        },
      });
    } else if (actionType === "hide") {
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: currentUser.userId,
        },
        data: {
          hidden: true,
        },
      });
    } else if (actionType === "unhide") {
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: currentUser.userId,
        },
        data: {
          hidden: false,
        },
      });
    } else {
      return NextResponse.json(
        { error: "Invalid action. Must be 'read', 'hide', or 'unhide'" },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating notifications:", error);
    return NextResponse.json(
      { error: "Failed to update notifications" },
      { status: 500 }
    );
  }
}
