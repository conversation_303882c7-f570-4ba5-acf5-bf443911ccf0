"use client";

import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { ChevronRight, Copy, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useNavigationGuard } from "@/context/navigation-guard-context";

interface BreadcrumbsProps {
  className?: string;
}

const Breadcrumbs = ({ className }: BreadcrumbsProps) => {
  const pathname = usePathname();
  const [copied, setCopied] = React.useState(false);
  const [tooltipText, setTooltipText] = React.useState(
    "Copy full URL to clipboard"
  );
  const { navigateWithConfirmation } = useNavigationGuard();

  // Update tooltip text on client side only
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      const currentSearchParams = new URLSearchParams(window.location.search);
      const hasTab =
        currentSearchParams.has("tab") && pathname.includes("/dashboard/npd/");
      setTooltipText(
        hasTab
          ? "Copy URL with current tab to clipboard"
          : "Copy full URL to clipboard"
      );
    }
  }, [pathname]);

  // Custom navigation handler for breadcrumb links
  const handleBreadcrumbNavigation =
    (href: string) => (e: React.MouseEvent) => {
      e.preventDefault();
      navigateWithConfirmation(href);
    };

  // Split path and filter out empty segments
  const pathSegments = pathname.split("/").filter(Boolean);

  // Don't show breadcrumbs on root dashboard
  if (pathSegments.length <= 1) {
    return null;
  }

  // Build breadcrumb items
  const breadcrumbItems = pathSegments.map((segment, index) => {
    const href = "/" + pathSegments.slice(0, index + 1).join("/");
    const isLast = index === pathSegments.length - 1;

    // Format segment name (capitalize and replace hyphens with spaces)
    let displayName;

    // Special handling for specific segments
    if (segment.toLowerCase() === "npd") {
      displayName = "NPD";
    } else {
      displayName = segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    }

    return {
      href,
      name: displayName,
      isLast,
      segment,
    };
  });

  const handleCopyToClipboard = async () => {
    try {
      // Start with the current pathname
      let fullUrl = `${window.location.origin}${pathname}`;

      // Check if we're on a product page and there's a tab parameter in the URL
      const currentSearchParams = new URLSearchParams(window.location.search);
      const tabParam = currentSearchParams.get("tab");

      if (tabParam && pathname.includes("/dashboard/npd/")) {
        fullUrl += `?tab=${encodeURIComponent(tabParam)}`;
      }

      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <nav className={cn("flex items-center space-x-1 text-sm", className)}>
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}

          <div className="flex items-center">
            {item.isLast ? (
              <div className="flex items-center space-x-1">
                <Link
                  href={item.href}
                  className="font-medium text-foreground hover:text-foreground/80 transition-colors cursor-pointer"
                  onClick={handleBreadcrumbNavigation(item.href)}
                >
                  {item.name}
                </Link>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-muted"
                  onClick={() => handleCopyToClipboard()}
                  title={tooltipText}
                >
                  {copied ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
            ) : (
              <Link
                href={item.href}
                className="text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                onClick={handleBreadcrumbNavigation(item.href)}
              >
                {item.name}
              </Link>
            )}
          </div>
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumbs;
