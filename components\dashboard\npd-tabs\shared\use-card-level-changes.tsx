import React from "react";
import { CardTitle } from "@/components/ui/card";

// Type for defining card field groups
export interface CardFieldGroup {
  [cardName: string]: string[];
}

// Hook for managing card-level unsaved changes
export function useCardLevelChanges(
  modifiedFields: Set<string>,
  cardFieldGroups: CardFieldGroup
) {
  // Helper function to check if a specific card has changes
  const hasCardChanges = (cardName: string): boolean => {
    const fields = cardFieldGroups[cardName] || [];
    return fields.some(field => modifiedFields.has(field));
  };

  // Helper function to get all cards with changes
  const getCardsWithChanges = (): string[] => {
    return Object.keys(cardFieldGroups).filter(cardName => hasCardChanges(cardName));
  };

  // Helper function to check if any card has changes
  const hasAnyChanges = (): boolean => {
    return Object.keys(cardFieldGroups).some(cardName => hasCardChanges(cardName));
  };

  return {
    hasCardChanges,
    getCardsWithChanges,
    hasAnyChanges,
  };
}

// Reusable component for card headers with unsaved changes indicator
export const CardHeaderWithChanges = ({ 
  icon, 
  title, 
  hasChanges 
}: { 
  icon: React.ReactNode; 
  title: string; 
  hasChanges: boolean;
}) => (
  <CardTitle className="flex items-center gap-2">
    {icon}
    {title}
    {hasChanges && (
      <div className="flex items-center gap-1 ml-auto">
        <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
        <span className="text-xs text-amber-600 font-normal">
          Unsaved changes
        </span>
      </div>
    )}
  </CardTitle>
);
