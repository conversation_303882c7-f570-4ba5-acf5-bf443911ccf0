"use client";

import React from "react";
import { PackageIcon } from "lucide-react";
import Spinner from "@/components/spinner";

// Use the main Spinner component for consistency
export function ThemeSpinner() {
  return <Spinner loading={true} size={10} />;
}

interface LoadingStateProps {
  message: string;
  description: string;
}

export function LoadingState({ message, description }: LoadingStateProps) {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="text-center">
            <div className="h-12 w-12 mx-auto mb-4 flex items-center justify-center">
              <ThemeSpinner />
            </div>
            <h1 className="text-2xl font-bold mb-2">{message}</h1>
            <p className="text-muted-foreground">{description}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ErrorStateProps {
  error: string;
}

export function ErrorState({ error }: ErrorStateProps) {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="text-center">
            <PackageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">Product not found</h1>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export function EmptyState() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="text-center">
            <PackageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">Select a product</h1>
            <p className="text-muted-foreground">
              Choose a product from the sidebar to view details
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
