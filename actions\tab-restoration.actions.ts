"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

/**
 * Check if a tab type has an archived version for a product
 */
export async function checkArchivedTab(npdProductId: string, tabName: string) {
  await validateServerAction();

  try {
    const archivedTab = await prisma.nPDProductTab.findFirst({
      where: {
        npdProductId,
        tabName,
        archived: true,
      },
      select: {
        id: true,
        tabName: true,
        archivedAt: true,
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return {
      hasArchivedTab: !!archivedTab,
      archivedTab: archivedTab || null,
    };
  } catch (error) {
    console.error("Error checking archived tab:", error);
    return {
      hasArchivedTab: false,
      archivedTab: null,
    };
  }
}

/**
 * Get all archived tabs for a product (for UI display)
 */
export async function getArchivedTabsForProduct(npdProductId: string) {
  await validateServerAction();

  try {
    const archivedTabs = await prisma.nPDProductTab.findMany({
      where: {
        npdProductId,
        archived: true,
      },
      select: {
        id: true,
        tabName: true,
        order: true,
        archivedAt: true,
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        archivedAt: "desc",
      },
    });

    return archivedTabs;
  } catch (error) {
    console.error("Error fetching archived tabs for product:", error);
    return [];
  }
}

/**
 * Manual restore tab (for admin use or explicit user action)
 */
export async function manualRestoreTab(npdProductTabId: string) {
  const user = await validateServerAction();

  try {
    // Get tab details
    const tab = await prisma.nPDProductTab.findUnique({
      where: { id: npdProductTabId },
      include: {
        npdProduct: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!tab) {
      throw new Error("Tab not found");
    }

    if (!tab.archived) {
      throw new Error("Tab is not archived");
    }

    // Check if an active tab with the same name already exists
    const existingActiveTab = await prisma.nPDProductTab.findFirst({
      where: {
        npdProductId: tab.npdProductId,
        tabName: tab.tabName,
        archived: false,
      },
    });

    if (existingActiveTab) {
      throw new Error(
        `An active tab named "${tab.tabName}" already exists. Please archive or rename the existing tab first.`
      );
    }

    // Restore the tab
    await prisma.nPDProductTab.update({
      where: { id: npdProductTabId },
      data: {
        archived: false,
        archivedAt: null,
        archivedBy: null,
      },
    });

    // Create activity log
    await prisma.nPDProductActivityLog.create({
      data: {
        npdProductId: tab.npdProductId,
        npdProductTabId: tab.id,
        userId: user.userId,
        action: "restored",
        description: `Manually restored tab "${tab.tabName}" from archive`,
        metadata: {
          field: "tab_restored",
          npdProductTabId: tab.id,
          tabName: tab.tabName,
          restoredAt: new Date().toISOString(),
          restoredBy: user.userId,
          restoreMethod: "manual",
        },
      },
    });

    return {
      success: true,
      message: `Tab "${tab.tabName}" has been restored`,
      tabName: tab.tabName,
      productSlug: tab.npdProduct.slug,
    };
  } catch (error) {
    console.error("Error manually restoring tab:", error);
    throw error;
  }
}
