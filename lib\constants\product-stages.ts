// Product stage constants
export const PRODUCT_STAGES = [
  { value: "Ideation", label: "Ideation" },
  { value: "Paused", label: "Paused" },
  { value: "Dropped", label: "Dropped" },
  { value: "Sourcing", label: "Sourcing" },
  { value: "Packaging", label: "Packaging" },
  { value: "PO", label: "PO" },
  { value: "Launched", label: "Launched" },
  { value: "Discontinued", label: "Discontinued" },
] as const;

// Product stage values array for backward compatibility
export const PRODUCT_STAGE_VALUES = [
  "Ideation",
  "Paused",
  "Dropped",
  "Sourcing",
  "Packaging",
  "PO",
  "Launched",
  "Discontinued",
] as const;

// Type for product stage
export type ProductStage = (typeof PRODUCT_STAGE_VALUES)[number];

// Helper function to check if a stage is valid
export const isValidProductStage = (stage: string): stage is ProductStage => {
  return PRODUCT_STAGE_VALUES.includes(stage as ProductStage);
};

// Default stage
export const DEFAULT_PRODUCT_STAGE: ProductStage = "Ideation";

// Stage color mapping
export const PRODUCT_STAGE_COLORS: Record<ProductStage, string> = {
  Ideation: "bg-amber-500",
  Sourcing: "bg-cyan-500",
  Packaging: "bg-blue-500",
  PO: "bg-purple-500",
  Launched: "bg-green-600",
  Paused: "bg-gray-400",
  Dropped: "bg-gray-400",
  Discontinued: "bg-gray-400",
} as const;

// Stage border color mapping (for left borders)
export const PRODUCT_STAGE_BORDER_COLORS: Record<ProductStage, string> = {
  Ideation: "border-l-amber-500",
  Sourcing: "border-l-cyan-500",
  Packaging: "border-l-blue-500",
  PO: "border-l-purple-500",
  Launched: "border-l-green-600",
  Paused: "border-l-gray-400",
  Dropped: "border-l-gray-400",
  Discontinued: "border-l-gray-400",
} as const;

// Helper function to get stage color
export const getProductStageColor = (stage: string): string => {
  if (isValidProductStage(stage)) {
    return PRODUCT_STAGE_COLORS[stage];
  }
  return "bg-gray-400"; // fallback color for unknown stage
};

// Helper function to get stage border color
export const getProductStageBorderColor = (stage: string): string => {
  if (isValidProductStage(stage)) {
    return PRODUCT_STAGE_BORDER_COLORS[stage];
  }
  return "border-l-gray-400"; // fallback color for unknown stage
};

// Helper function to format stage labels for display
export const formatProductStageLabel = (stage: string): string => {
  return stage
    .toLowerCase()
    .replace(/_/g, " ")
    .replace(/\b\w/g, (l) => l.toUpperCase());
};
