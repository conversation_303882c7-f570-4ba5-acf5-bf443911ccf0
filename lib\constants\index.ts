// Environment constants
export const isDevelopment = process.env.NODE_ENV === "development";
export const isProduction = process.env.NODE_ENV === "production";
export const isTest = process.env.NODE_ENV === "test";

// App constants
export const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || "Quick Ticket";
export const APP_DESCRIPTION =
  process.env.NEXT_PUBLIC_APP_DESCRIPTION ||
  "Efficient ticketing system for teams.";
// In production, we'll use NEXT_PUBLIC_SERVER_URL from Vercel
// In development, we'll use localhost
export const SERVER_URL =
  process.env.NEXT_PUBLIC_SERVER_URL ||
  (process.env.NODE_ENV === "production"
    ? "https://quickt.vercel.app"
    : "http://localhost:3000");

// Re-export from other constant files for convenience
export * from "./brands";
export * from "./product-stages";
