"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowRight,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  Circle,
  User,
  Calendar,
  ChevronDown,
  ChevronRight,
  History,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { UserMentionInput } from "@/components/ui/user-mention-input";
import { DeletionRequestDialog } from "@/components/ui/deletion-request-dialog";
import { toast } from "sonner";
import { sendDeletionRequestNotification } from "@/actions/overview-notifications.actions";

interface NextStepVersion {
  id: string;
  title: string;
  assignees?: string[];
  dueDate?: string;
  description?: string;
  status: "not-started" | "in-progress" | "completed";
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface NextStep {
  id: string;
  title: string;
  assignees?: string[];
  dueDate?: string;
  description?: string;
  status: "not-started" | "in-progress" | "completed";
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: NextStepVersion[];
}

interface NextStepsCardProps {
  data?: NextStep[];
  isEditing: boolean;
  onUpdate: (data: NextStep[]) => void;
  onUserMention?: (username: string) => void;
  productId?: string;
  productName?: string;
  productSlug?: string;
  tabId?: string;
  onAssigneeNotifications?: (
    newSteps: NextStep[],
    oldSteps: NextStep[]
  ) => Promise<void>;
}

const statusIcons = {
  "not-started": <Circle className="h-4 w-4" />,
  "in-progress": <Clock className="h-4 w-4" />,
  completed: <CheckCircle className="h-4 w-4" />,
};

const statusColors = {
  "not-started": "bg-gray-100 text-gray-800 border-gray-200",
  "in-progress": "bg-blue-100 text-blue-800 border-blue-200",
  completed: "bg-green-100 text-green-800 border-green-200",
};

export function NextStepsCard({
  data,
  isEditing,
  onUpdate,
  onUserMention,
  productId,
  productName,
  productSlug,
  tabId,
  onAssigneeNotifications,
}: NextStepsCardProps) {
  const [localData, setLocalData] = useState<NextStep[]>(data || []);
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(
    new Set()
  );
  const [showDeletionDialog, setShowDeletionDialog] = useState(false);
  const [stepToDelete, setStepToDelete] = useState<NextStep | null>(null);
  const { user } = useAuth();

  const handleUserMention = (username: string) => {
    if (onUserMention) {
      onUserMention(username);
    }
  };

  // Expose notification handler to parent
  React.useEffect(() => {
    if (onAssigneeNotifications) {
      onAssigneeNotifications(localData, data || []);
    }
  }, [localData, data, onAssigneeNotifications]);

  const updateLocalData = (newData: NextStep[]) => {
    setLocalData(newData);
    onUpdate(newData);
  };

  const addStep = () => {
    const newStep: NextStep = {
      id: Date.now().toString(),
      title: "",
      status: "not-started",
      description: "",
      assignees: [],
      user: user?.name || "", // Automatically populate with current user's name
      isEdited: false, // Explicitly set as not edited for new entries
    };
    updateLocalData([...localData, newStep]);
  };

  const updateStep = (id: string, updates: Partial<NextStep>) => {
    const updatedSteps = localData.map((s) =>
      s.id === id ? { ...s, ...updates } : s
    );
    updateLocalData(updatedSteps);
  };

  const toggleVersions = (stepId: string) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedVersions(newExpanded);
  };

  const removeStep = (id: string) => {
    const step = localData.find((s) => s.id === id);
    if (!step || !user) return;

    const isOwnEntry = step.user === user.name;

    if (isOwnEntry) {
      // User can delete their own entry
      const filteredSteps = localData.filter((s) => s.id !== id);
      updateLocalData(filteredSteps);
    } else {
      // Show deletion request dialog for others' entries
      setStepToDelete(step);
      setShowDeletionDialog(true);
    }
  };

  const handleSendDeletionRequest = async () => {
    if (
      !stepToDelete ||
      !productId ||
      !productName ||
      !productSlug ||
      !tabId ||
      !stepToDelete.user
    ) {
      return;
    }

    const result = await sendDeletionRequestNotification(
      stepToDelete.user,
      "next-step",
      stepToDelete.title,
      productId,
      productName,
      productSlug,
      tabId,
      stepToDelete.id
    );

    // Reset state
    setStepToDelete(null);

    // Show result message
    if (result.success) {
      toast.success("Deletion request sent", {
        description: result.message,
      });
    } else {
      toast.error("Failed to send deletion request", {
        description: result.message,
      });
    }
  };

  // Sort steps by status
  const sortedSteps = [...localData].sort((a, b) => {
    const statusOrder = { "not-started": 3, "in-progress": 2, completed: 1 };
    return statusOrder[b.status] - statusOrder[a.status];
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <ArrowRight className="h-5 w-5 text-purple-500" />
            Next Steps
          </CardTitle>
          {isEditing && (
            <Button size="sm" variant="outline" onClick={addStep}>
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sortedSteps.map((step) => (
            <div
              key={step.id}
              id={`entry-next-step-${step.id}`}
              className="border rounded-lg p-3 space-y-3"
            >
              {isEditing ? (
                <div className="space-y-3">
                  {/* Title */}
                  <Input
                    placeholder="Step title"
                    value={step.title}
                    onChange={(e) =>
                      updateStep(step.id, { title: e.target.value })
                    }
                  />

                  {/* Status and Assignee */}
                  <div className="grid grid-cols-2 gap-2">
                    <Select
                      value={step.status}
                      onValueChange={(value: NextStep["status"]) =>
                        updateStep(step.id, { status: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="not-started">Not Started</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                      </SelectContent>
                    </Select>

                    <UserMentionInput
                      placeholder="Assignees (use @username, separate multiple with commas)"
                      value={
                        step.assignees?.map((a) => `@${a}`).join(", ") || ""
                      }
                      onChange={(value, mentions) => {
                        updateStep(step.id, { assignees: mentions });
                      }}
                    />
                  </div>

                  {/* Due Date */}
                  <Input
                    type="date"
                    placeholder="Due date"
                    value={step.dueDate || ""}
                    onChange={(e) =>
                      updateStep(step.id, { dueDate: e.target.value })
                    }
                  />

                  {/* Description */}
                  <Textarea
                    placeholder="Description (optional)"
                    value={step.description || ""}
                    onChange={(e) =>
                      updateStep(step.id, { description: e.target.value })
                    }
                    rows={2}
                  />

                  {/* Remove Button */}
                  <div className="flex justify-end">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeStep(step.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {/* Version toggle icon on the left */}
                      {step.versions && step.versions.length > 0 && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => toggleVersions(step.id)}
                          className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
                        >
                          {expandedVersions.has(step.id) ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronRight className="h-3 w-3" />
                          )}
                        </Button>
                      )}
                      {statusIcons[step.status]}
                      <h4 className="font-medium">{step.title}</h4>
                      {step.isEdited && (
                        <Badge variant="secondary" className="text-xs">
                          edited
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge className={statusColors[step.status]}>
                        {step.status.replace("-", " ")}
                      </Badge>
                    </div>
                  </div>

                  {/* Details */}
                  {(step.assignees?.length ||
                    step.dueDate ||
                    step.user ||
                    (step.editedAt && step.editedBy)) && (
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {step.assignees && step.assignees.length > 0 && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <div className="flex gap-1">
                            {step.assignees.map((assignee, index) => (
                              <button
                                key={assignee}
                                className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm bg-blue-50 px-1 py-0.5 rounded-sm"
                                onClick={() => handleUserMention(assignee)}
                              >
                                @{assignee}
                                {index < (step.assignees?.length || 0) - 1
                                  ? ","
                                  : ""}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                      {step.dueDate && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(step.dueDate).toLocaleDateString()}
                        </div>
                      )}
                      {step.user && (
                        <div className="flex items-center gap-1">
                          <button
                            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm bg-blue-50 px-1 py-0.5 rounded-sm"
                            onClick={() =>
                              step.user && handleUserMention(step.user)
                            }
                          >
                            @{step.user}
                          </button>
                        </div>
                      )}
                      {step.editedAt &&
                        step.editedBy &&
                        step.editedBy !== step.user && (
                          <div className="text-xs text-muted-foreground">
                            edited by @{step.editedBy} on{" "}
                            {new Date(step.editedAt).toLocaleDateString()}
                          </div>
                        )}
                    </div>
                  )}

                  {/* Description */}
                  {step.description && (
                    <p className="text-sm text-muted-foreground">
                      {step.description}
                    </p>
                  )}

                  {/* Previous versions */}
                  {step.versions &&
                    step.versions.length > 0 &&
                    expandedVersions.has(step.id) && (
                      <div className="mt-3 pt-3 border-t border-dashed">
                        <div className="flex items-center gap-2 mb-2">
                          <History className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground font-medium">
                            Previous Versions
                          </span>
                        </div>
                        <div className="space-y-2 ml-4">
                          {[...step.versions].reverse().map((version) => (
                            <div
                              key={version.id}
                              className="p-2 bg-muted/30 rounded text-sm"
                            >
                              <div className="flex items-center gap-2">
                                {statusIcons[version.status]}
                                <div className="font-medium text-muted-foreground">
                                  {version.title}
                                </div>
                                <Badge variant="outline">
                                  {version.status.replace("-", " ")}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                <span>by @{version.user}</span>
                                {version.assignees &&
                                  version.assignees.length > 0 && (
                                    <>
                                      <span>•</span>
                                      <span>
                                        assigned to{" "}
                                        {version.assignees
                                          .map((a) => `@${a}`)
                                          .join(", ")}
                                      </span>
                                    </>
                                  )}
                                {version.dueDate && (
                                  <>
                                    <span>•</span>
                                    <span>
                                      due{" "}
                                      {new Date(
                                        version.dueDate
                                      ).toLocaleDateString()}
                                    </span>
                                  </>
                                )}
                              </div>
                              {version.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {version.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          ))}

          {localData.length === 0 && !isEditing && (
            <div className="text-center py-6 text-muted-foreground">
              <ArrowRight className="h-6 w-6 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No next steps defined</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Deletion Request Dialog */}
      <DeletionRequestDialog
        open={showDeletionDialog}
        onOpenChange={setShowDeletionDialog}
        creatorUsername={stepToDelete?.user || ""}
        entryType="next step"
        entryTitle={stepToDelete?.title || ""}
        onSendRequest={handleSendDeletionRequest}
        onCancel={() => setStepToDelete(null)}
      />
    </Card>
  );
}
