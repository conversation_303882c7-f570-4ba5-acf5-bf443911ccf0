"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import {
  getPollingInterval,
  isPollingEnabled,
  POLLING_CONFIG,
} from "@/lib/constants/polling";

interface SmartPollingOptions {
  enabled?: boolean;
  pollingType: keyof typeof import("@/lib/constants/polling").POLLING_INTERVALS;
  onPoll: () => Promise<void> | void;
  dependencies?: unknown[];
}

/**
 * Smart polling hook that optimizes polling based on user activity and visibility
 */
export function useSmartPolling({
  enabled = true,
  pollingType,
  onPoll,
  dependencies = [],
}: SmartPollingOptions) {
  const [isPolling, setIsPolling] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isIdle, setIsIdle] = useState(false);
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Track user activity
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    if (isIdle) {
      setIsIdle(false);
    }
  }, [isIdle]);

  // Track page visibility
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, []);

  // Track user activity for idle detection
  useEffect(() => {
    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "scroll",
      "touchstart",
    ];

    events.forEach((event) => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // Check for idle state every minute
    const idleCheckInterval = setInterval(() => {
      const timeSinceActivity = Date.now() - lastActivityRef.current;
      const shouldBeIdle = timeSinceActivity > POLLING_CONFIG.IDLE_TIMEOUT;

      if (shouldBeIdle !== isIdle) {
        setIsIdle(shouldBeIdle);
      }
    }, 60000); // Check every minute

    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, updateActivity);
      });
      clearInterval(idleCheckInterval);
    };
  }, [updateActivity, isIdle]);

  // Determine if polling should be active
  const shouldPoll = useCallback(() => {
    if (!enabled || !isPollingEnabled(pollingType)) {
      return false;
    }

    // Don't poll if tab is hidden and background polling is disabled
    if (!isVisible && POLLING_CONFIG.PAUSE_ON_HIDDEN) {
      return false;
    }

    // Don't poll if user is idle and idle polling is disabled
    if (isIdle && !POLLING_CONFIG.POLL_WHEN_IDLE) {
      return false;
    }

    return true;
  }, [enabled, pollingType, isVisible, isIdle]);

  // Get adaptive polling interval based on conditions
  const getAdaptiveInterval = useCallback(() => {
    let baseInterval = getPollingInterval(pollingType);

    // Increase interval if tab is not visible
    if (!isVisible) {
      baseInterval *= 2;
    }

    // Increase interval if user is idle
    if (isIdle) {
      baseInterval *= 3;
    }

    // Check for slow connection and adjust
    if (POLLING_CONFIG.REDUCE_ON_SLOW_CONNECTION && "connection" in navigator) {
      const connection = (
        navigator as { connection?: { effectiveType: string } }
      ).connection;
      if (
        connection &&
        (connection.effectiveType === "slow-2g" ||
          connection.effectiveType === "2g")
      ) {
        baseInterval *= 2;
      }
    }

    return baseInterval;
  }, [pollingType, isVisible, isIdle]);

  // Polling function with error handling and backoff
  const poll = useCallback(async () => {
    if (!shouldPoll()) {
      return;
    }

    try {
      setIsPolling(true);
      await onPoll();
    } catch {
      // Silently handle polling errors
      // Could implement exponential backoff here if needed
    } finally {
      setIsPolling(false);
    }
  }, [shouldPoll, onPoll]);

  // Setup polling loop
  useEffect(() => {
    if (!shouldPoll()) {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
        pollTimeoutRef.current = null;
      }
      return;
    }

    const interval = getAdaptiveInterval();

    const scheduleNextPoll = () => {
      pollTimeoutRef.current = setTimeout(async () => {
        await poll();
        if (shouldPoll()) {
          scheduleNextPoll();
        }
      }, interval);
    };

    scheduleNextPoll();

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
        pollTimeoutRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldPoll, getAdaptiveInterval, poll, ...dependencies]);

  // Manual trigger function
  const trigger = useCallback(async () => {
    await poll();
  }, [poll]);

  // Stop polling function
  const stop = useCallback(() => {
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
      pollTimeoutRef.current = null;
    }
  }, []);

  return {
    isPolling,
    isVisible,
    isIdle,
    trigger,
    stop,
    shouldPoll: shouldPoll(),
  };
}

/**
 * Simplified hook for basic polling needs
 */
export function usePolling(
  pollingType: keyof typeof import("@/lib/constants/polling").POLLING_INTERVALS,
  onPoll: () => Promise<void> | void,
  enabled: boolean = true
) {
  return useSmartPolling({
    enabled,
    pollingType,
    onPoll,
  });
}
