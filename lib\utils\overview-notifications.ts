/**
 * Notification templates and utilities for Overview tab activities
 */

import { createNotification, createBulkNotifications } from "./notifications";

export interface OverviewNotificationData {
  productId: string;
  productName: string;
  productSlug: string;
  tabId: string;
  entryType: "key-action" | "next-step" | "dependency" | "milestone";
  entryId: string;
  entryTitle: string;
  actionType: "assigned" | "deletion-request" | "mention";
  requestedBy?: string;
  assignees?: string[];
  [key: string]: unknown; // Add index signature for compatibility with Record<string, unknown>
}

/**
 * Create notification for Next Steps assignment
 */
export async function createNextStepAssignmentNotification(
  assigneeUserIds: string[],
  assignedByUserName: string,
  stepTitle: string,
  productId: string,
  productName: string,
  productSlug: string,
  tabId: string,
  stepId: string
) {
  const notifications = assigneeUserIds.map((userId) => ({
    userId,
    type: "next_step_assignment",
    title: "New Task Assignment",
    message: `${assignedByUserName} assigned you to: "${stepTitle}" in ${productName}`,
    data: {
      productId,
      productName,
      productSlug,
      tabId,
      entryType: "next-step" as const,
      entryId: stepId,
      entryTitle: stepTitle,
      actionType: "assigned" as const,
      assignedBy: assignedByUserName,
    } as OverviewNotificationData,
  }));

  return createBulkNotifications(notifications);
}

/**
 * Create notification for deletion request
 */
export async function createDeletionRequestNotification(
  creatorUserId: string,
  requesterUserName: string,
  entryType: "key-action" | "next-step" | "dependency" | "milestone",
  entryTitle: string,
  productId: string,
  productName: string,
  productSlug: string,
  tabId: string,
  entryId: string
) {
  const entryTypeLabels = {
    "key-action": "Key Action",
    "next-step": "Next Step",
    dependency: "Dependency",
    milestone: "Milestone",
  };

  return createNotification({
    userId: creatorUserId,
    type: "deletion_request",
    title: "Deletion Request",
    message: `${requesterUserName} requests deletion of your ${entryTypeLabels[entryType]}: "${entryTitle}" in ${productName}`,
    data: {
      productId,
      productName,
      productSlug,
      tabId,
      entryType,
      entryId,
      entryTitle,
      actionType: "deletion-request" as const,
      requestedBy: requesterUserName,
    } as OverviewNotificationData,
  });
}

/**
 * Create notification for user mention in overview entries
 */
export async function createOverviewMentionNotification(
  mentionedUserId: string,
  mentionedByUserName: string,
  entryType: "key-action" | "next-step" | "dependency" | "milestone",
  entryTitle: string,
  productId: string,
  productName: string,
  productSlug: string,
  tabId: string,
  entryId: string
) {
  const entryTypeLabels = {
    "key-action": "Key Action",
    "next-step": "Next Step",
    dependency: "Dependency",
    milestone: "Milestone",
  };

  return createNotification({
    userId: mentionedUserId,
    type: "overview_mention",
    title: "You were mentioned",
    message: `${mentionedByUserName} mentioned you in ${entryTypeLabels[entryType]}: "${entryTitle}" in ${productName}`,
    data: {
      productId,
      productName,
      productSlug,
      tabId,
      entryType,
      entryId,
      entryTitle,
      actionType: "mention" as const,
      requestedBy: mentionedByUserName,
    } as OverviewNotificationData,
  });
}

/**
 * Get user ID by username
 */
export async function getUserIdByUsername(
  username: string
): Promise<string | null> {
  try {
    const { prisma } = await import("@/lib/db/prisma");
    const user = await prisma.user.findFirst({
      where: {
        name: {
          equals: username,
          mode: "insensitive",
        },
      },
      select: {
        id: true,
      },
    });

    return user?.id || null;
  } catch (error) {
    console.error("Error finding user by username:", error);
    return null;
  }
}

/**
 * Get multiple user IDs by usernames
 */
export async function getUserIdsByUsernames(
  usernames: string[]
): Promise<Record<string, string>> {
  try {
    const { prisma } = await import("@/lib/db/prisma");
    const users = await prisma.user.findMany({
      where: {
        name: {
          in: usernames,
          mode: "insensitive",
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const userMap: Record<string, string> = {};
    users.forEach((user) => {
      userMap[user.name.toLowerCase()] = user.id;
    });

    return userMap;
  } catch (error) {
    console.error("Error finding users by usernames:", error);
    return {};
  }
}

/**
 * Generate navigation URL for overview notifications
 */
export function generateOverviewNotificationUrl(
  productSlug: string,
  tabId: string,
  entryType: string,
  entryId: string
): string {
  return `/dashboard/npd/${productSlug}?tab=${tabId}&highlight=${entryType}-${entryId}`;
}
