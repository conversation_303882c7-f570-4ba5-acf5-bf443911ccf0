import { LucideIcon } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface PreviewFeature {
  icon: LucideIcon;
  title: string;
  description: string;
  color: string;
}

interface PreviewCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  features: PreviewFeature[];
}

export function PreviewCard({
  title,
  description,
  icon: Icon,
  features,
}: PreviewCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {features.map((feature, index) => (
            <div
              key={index}
              className="flex items-center gap-3 p-4 border rounded-lg"
            >
              <feature.icon className={`h-8 w-8 ${feature.color}`} />
              <div>
                <h3 className="font-medium">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
