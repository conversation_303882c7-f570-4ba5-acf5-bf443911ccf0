import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";
import { notifyUserRoleChange } from "@/lib/auth/notifications";

// Add a role to a user
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { userId } = await context.params;
    const { roleId } = await request.json();

    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    // Check if the role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent non-super admins from assigning super admin role
    if (
      role.name === "SUPER_ADMIN" &&
      !currentUser.roles.includes("SUPER_ADMIN")
    ) {
      return NextResponse.json(
        { error: "Only super admins can assign super admin role" },
        { status: 403 }
      );
    }

    // Check if user already has this role
    const hasRole = user.roles.some((userRole) => userRole.id === roleId);
    if (hasRole) {
      return NextResponse.json(
        { error: "User already has this role" },
        { status: 400 }
      );
    }

    // Add the role to the user
    const oldRoles = user.roles.map((role) => role.name);

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roles: {
          connect: { id: roleId },
        },
      },
      include: { roles: true },
    });

    // Notify the user about the role change
    const currentRoles = updatedUser.roles.map((role) => role.name);
    notifyUserRoleChange(userId, currentRoles, oldRoles);

    return NextResponse.json({
      success: true,
      message: "Role added successfully",
    });
  } catch (error) {
    console.error("Error adding user role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Remove a specific role from a user
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { userId } = await context.params;
    const { roleId } = await request.json();

    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Find the role to be removed
    const roleToRemove = user.roles.find((role) => role.id === roleId);
    if (!roleToRemove) {
      return NextResponse.json(
        { error: "User doesn't have this role" },
        { status: 400 }
      );
    }

    // Prevent removing super admin role unless current user is super admin
    if (
      roleToRemove.name === "SUPER_ADMIN" &&
      !currentUser.roles.includes("SUPER_ADMIN")
    ) {
      return NextResponse.json(
        { error: "Only super admins can remove super admin role" },
        { status: 403 }
      );
    }

    // Remove the role from the user
    const oldRoles = user.roles.map((role) => role.name);

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roles: {
          disconnect: { id: roleId },
        },
      },
      include: { roles: true },
    });

    // Notify the user about the role change
    const currentRoles = updatedUser.roles.map((role) => role.name);
    notifyUserRoleChange(userId, currentRoles, oldRoles);

    return NextResponse.json({
      success: true,
      message: "Role removed successfully",
    });
  } catch (error) {
    console.error("Error removing user role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Set multiple roles for a user (replaces all existing roles)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { userId } = await context.params;
    const { roleIds } = await request.json();

    if (!Array.isArray(roleIds)) {
      return NextResponse.json(
        { error: "Role IDs must be an array" },
        { status: 400 }
      );
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if all roles exist and validate permissions
    if (roleIds.length > 0) {
      const roles = await prisma.role.findMany({
        where: { id: { in: roleIds } },
      });

      if (roles.length !== roleIds.length) {
        return NextResponse.json(
          { error: "One or more roles not found" },
          { status: 404 }
        );
      }

      // Prevent non-super admins from assigning super admin role
      const hasSuperAdminRole = roles.some(
        (role) => role.name === "SUPER_ADMIN"
      );
      if (hasSuperAdminRole && !currentUser.roles.includes("SUPER_ADMIN")) {
        return NextResponse.json(
          { error: "Only super admins can assign super admin role" },
          { status: 403 }
        );
      }
    }

    // Set the roles for the user
    const oldRoles = user.roles.map((role) => role.name);

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roles: {
          set: roleIds.map((roleId) => ({ id: roleId })),
        },
      },
      include: { roles: true },
    });

    // Notify the user about the role change
    const currentRoles = updatedUser.roles.map((role) => role.name);
    notifyUserRoleChange(userId, currentRoles, oldRoles);

    return NextResponse.json({
      success: true,
      message: "User roles updated successfully",
    });
  } catch (error) {
    console.error("Error updating user roles:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
