"use client";

import React, {
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  forwardRef,
} from "react";
import { updateTabData, fetchTabDataByName } from "@/actions/npd.actions";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import { TabDangerZone } from "../shared/tab-danger-zone";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Download,
  BarChart3,
  ExternalLink,
  TrendingUp,
  Plus,
  Trash2,
} from "lucide-react";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useFormFieldStyles } from "@/hooks/use-form-field-styles";
import { TABLE_STYLES, TabsTableContainer } from "../shared";

// Competitor interface from tab 1.1
interface Competitor {
  productDetails: string;
  asin: string;
  url: string;
  imageUrl: string;
  brand: string;
  price: string;
  asinSales: string;
  asinRevenue: string;
  bsr: string;
  sellerCountry: string;
  fees: string;
  ratings: string;
  reviewCount: string;
  fulfillment: string;
  dimensions: string;
  weight: string;
  creationDate: string;
  isVisible?: boolean;
}

// Transposed row structure - each field becomes a row
interface TransposedRow {
  field: string;
  label: string;
  [key: string]: string; // Dynamic competitor columns
}

// Key Product Features structure
interface ProductFeature {
  field: string;
  label: string;
  [key: string]: string; // Dynamic competitor columns
}

// Product Analysis structure
interface ProductAnalysis {
  field: string;
  label: string;
  [key: string]: string; // Dynamic competitor columns
}

// Differentiation Idea structure
interface DifferentiationIdea {
  id: string;
  title: string;
  description: string;
  user: string;
  createdAt: string;
  isEdited?: boolean;
}

// Data structure for Section 1.3
interface Section13Data {
  transposedData: TransposedRow[];
  productFeatures: ProductFeature[];
  productAnalysis: ProductAnalysis[];
  differentiationIdeas: DifferentiationIdea[];
  user?: string | null;
  editedAt?: string;
  editedBy?: string | null;
  isEdited?: boolean;
  versions?: unknown[];
}

interface Section13DisplayProps {
  tabId: string;
  initialData: Section13Data;
  isEditing: boolean;
  onDirtyChange: (isDirty: boolean) => void;
  onUserMention?: (username: string) => void;
  productId: string;
  productName: string;
  productSlug: string;
  // Props for TabDangerZone
  tabName?: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
}

export interface Section13DisplayRef {
  save: () => Promise<void>;
}

export const Section13Display = forwardRef<
  Section13DisplayRef,
  Section13DisplayProps
>(function Section13Display(
  {
    tabId,
    initialData,
    isEditing,
    onDirtyChange,
    productId,
    productName,
    productSlug,
    tabName,
    tabCreatorId,
    productOwnerId,
  },
  ref
) {
  const [data, setData] = useState<Section13Data>({
    transposedData: initialData.transposedData || [],
    productFeatures: initialData.productFeatures || [],
    productAnalysis: initialData.productAnalysis || [],
    differentiationIdeas: initialData.differentiationIdeas || [],
    user: initialData.user,
    editedAt: initialData.editedAt,
    editedBy: initialData.editedBy,
    isEdited: initialData.isEdited,
    versions: initialData.versions,
  });
  const [originalData, setOriginalData] = useState<Section13Data>(data);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [modifiedFields, setModifiedFields] = useState<Set<string>>(new Set());
  const [prevIsEditing, setPrevIsEditing] = useState(isEditing);
  const [section11Data, setSection11Data] = useState<{
    competitors: Competitor[];
  } | null>(null);
  const [isLoadingSection11, setIsLoadingSection11] = useState(false);
  const [hasAttemptedAutoImport, setHasAttemptedAutoImport] = useState(false);

  const [hoveredColumn, setHoveredColumn] = useState<string | null>(null);
  const { user } = useAuth();

  // Function to calculate listing age from creation date
  const calculateListingAge = useCallback((creationDate: string): string => {
    if (!creationDate || creationDate === "-") return "-";

    const creation = new Date(creationDate);
    if (isNaN(creation.getTime())) return "-";

    const today = new Date();
    const diffTime = today.getTime() - creation.getTime();
    const ageInDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (ageInDays >= 365) {
      const years = Math.floor(ageInDays / 365);
      const remainingMonths = Math.floor((ageInDays % 365) / 30);
      if (remainingMonths > 0) {
        return years === 1
          ? `1y ${remainingMonths}m`
          : `${years}y ${remainingMonths}m`;
      } else {
        return years === 1 ? `1y` : `${years}y`;
      }
    } else if (ageInDays >= 30) {
      const months = Math.floor(ageInDays / 30);
      return months === 1 ? `1m` : `${months}m`;
    } else {
      return `<1m`; // Less than 1 month
    }
  }, []);

  // Function to calculate sales/review ratio
  const calculateSalesReviewRatio = useCallback(
    (asinSales: string, reviewCount: string): string => {
      const salesValue = parseFloat(asinSales.replace(/[^\d.]/g, "") || "0");
      const reviewsValue = parseFloat(
        reviewCount.replace(/[^\d.]/g, "") || "0"
      );
      const ratio = reviewsValue > 0 ? salesValue / reviewsValue : 0;
      return ratio > 0 ? ratio.toFixed(1) : "-";
    },
    []
  );

  // Function to create product features data from tab 1.1
  const createProductFeaturesData = useCallback(
    (competitors: Competitor[]): ProductFeature[] => {
      if (!competitors || competitors.length === 0) {
        return [];
      }

      const featureFields = [
        { key: "ingredients", label: "Ingredients" },
        { key: "packaging", label: "Packaging" },
        { key: "dimensions", label: "Dimensions" },
        { key: "weight", label: "Weight (lbs)" },
      ];

      return featureFields.map(({ key, label }) => {
        const row: ProductFeature = {
          field: key,
          label: label,
        };

        // Add competitor columns
        competitors.forEach((competitor, index) => {
          const competitorKey = `competitor_${index + 1}`;
          if (key === "dimensions") {
            row[competitorKey] = competitor.dimensions || "";
          } else if (key === "weight") {
            row[competitorKey] = competitor.weight || "";
          } else {
            row[competitorKey] = ""; // Empty for ingredients and packaging
          }
        });

        return row;
      });
    },
    []
  );

  // Function to create product analysis data
  const createProductAnalysisData = useCallback(
    (competitors: Competitor[]): ProductAnalysis[] => {
      if (!competitors || competitors.length === 0) {
        return [];
      }

      const analysisFields = [
        { key: "positive1", label: "#1 positive" },
        { key: "positive2", label: "#2 positive" },
        { key: "positive3", label: "#3 positive" },
        { key: "negative1", label: "#1 negative" },
        { key: "negative2", label: "#2 negative" },
        { key: "negative3", label: "#3 negative" },
      ];

      return analysisFields.map(({ key, label }) => {
        const row: ProductAnalysis = {
          field: key,
          label: label,
        };

        // Add empty competitor columns
        competitors.forEach((_, index) => {
          const competitorKey = `competitor_${index + 1}`;
          row[competitorKey] = "";
        });

        return row;
      });
    },
    []
  );

  // Function to transpose competitor data from tab 1.1
  const transposeCompetitorData = useCallback(
    (competitors: Competitor[]): TransposedRow[] => {
      if (!competitors || competitors.length === 0) {
        return [];
      }

      // Define the specific fields we want to import
      const fieldsToTranspose = [
        { key: "imageUrl", label: "", type: "image" },
        { key: "brand", label: "Brand", type: "direct" },
        { key: "asinWithLink", label: "ASIN", type: "asinLink" },
        { key: "ratings", label: "Rating", type: "direct" },
        { key: "reviewCount", label: "Review Count", type: "direct" },
        { key: "listingAge", label: "Listing Age", type: "calculated" },
        { key: "price", label: "Price", type: "direct" },
        { key: "annualSales", label: "Annual Sales", type: "calculated" },
        { key: "annualRevenue", label: "Annual Revenue", type: "calculated" },
        {
          key: "relevantVariations",
          label: "# of Relevant Variations",
          type: "manual",
        },
        {
          key: "salesToReviewRatio",
          label: "Sales/Review Ratio",
          type: "calculated",
        },
      ];

      return fieldsToTranspose.map(({ key, label, type }) => {
        const row: TransposedRow = {
          field: key,
          label: label,
        };

        // Add each competitor as a column
        competitors.forEach((competitor, index) => {
          const competitorKey = `competitor_${index}`;

          if (type === "calculated") {
            // Handle calculated fields
            if (key === "listingAge") {
              row[competitorKey] = calculateListingAge(competitor.creationDate);
            } else if (key === "salesToReviewRatio") {
              row[competitorKey] = calculateSalesReviewRatio(
                competitor.asinSales,
                competitor.reviewCount
              );
            }
          } else if (type === "asinLink") {
            // Handle ASIN with Link combination - store as JSON
            row[competitorKey] = JSON.stringify({
              asin: competitor.asin || "",
              url: competitor.url || "",
            });
          } else if (type === "image") {
            // Handle image URLs - store the imageUrl directly
            row[competitorKey] = competitor.imageUrl || "";
          } else if (type === "manual") {
            // Handle manual fields (leave empty for user to fill)
            row[competitorKey] = "";
          } else {
            // Handle direct field mapping
            row[competitorKey] =
              (competitor[key as keyof Competitor] as string) || "";
          }
        });

        return row;
      });
    },
    [calculateListingAge, calculateSalesReviewRatio]
  );

  // Function to fetch section 1.1 data from database
  const fetchSection11Data = useCallback(async () => {
    if (!productId) return;

    setIsLoadingSection11(true);
    try {
      const tab11Data = await fetchTabDataByName(productId, "1.1");
      if (tab11Data?.fields) {
        const competitors =
          (tab11Data.fields.competitors as Competitor[]) || [];
        setSection11Data({ competitors });
      } else {
        setSection11Data(null);
      }
    } catch (error) {
      console.error("Error fetching section 1.1 data:", error);
      setSection11Data(null);
    } finally {
      setIsLoadingSection11(false);
    }
  }, [productId]);

  // Fetch section 1.1 data on component mount
  useEffect(() => {
    fetchSection11Data();
  }, [fetchSection11Data]);

  // Initialize data on component mount
  useEffect(() => {
    setData(initialData);
    setOriginalData(initialData);
    setHasUnsavedChanges(false);
  }, [initialData]);

  // Auto-import data from tab 1.1 ONLY if tab 1.3 is completely empty (first time creation)
  useEffect(() => {
    // Only auto-import if:
    // 1. We haven't attempted auto-import yet
    // 2. Tab 1.3 has no transposed data (empty/new tab)
    // 3. Tab 1.1 has competitor data available
    if (
      !hasAttemptedAutoImport &&
      section11Data?.competitors &&
      section11Data.competitors.length > 0 &&
      (!initialData.transposedData || initialData.transposedData.length === 0)
    ) {
      const transposedData = transposeCompetitorData(section11Data.competitors);
      const productFeatures = createProductFeaturesData(
        section11Data.competitors
      );
      const productAnalysis = createProductAnalysisData(
        section11Data.competitors
      );
      const newData = {
        ...initialData,
        transposedData,
        productFeatures,
        productAnalysis,
      };
      setData(newData);
      setOriginalData(newData);
      setHasAttemptedAutoImport(true);
      console.log("Auto-imported competitor data from tab 1.1 on first load");
    } else if (section11Data !== null) {
      // Mark as attempted once we have section11Data (even if empty)
      setHasAttemptedAutoImport(true);
    }
  }, [
    section11Data,
    initialData,
    transposeCompetitorData,
    createProductFeaturesData,
    createProductAnalysisData,
    hasAttemptedAutoImport,
  ]);

  // Notify parent of changes
  useEffect(() => {
    onDirtyChange(hasUnsavedChanges);
  }, [hasUnsavedChanges, onDirtyChange]);

  // Check for changes
  const checkForChanges = useCallback(
    (newData: Section13Data) => {
      const hasChanges =
        JSON.stringify(newData) !== JSON.stringify(originalData);
      setHasUnsavedChanges(hasChanges);
      return hasChanges;
    },
    [originalData]
  );

  // Update data and check for changes
  const updateData = useCallback(
    (updates: Partial<Section13Data>) => {
      const newData = { ...data, ...updates };
      setData(newData);
      checkForChanges(newData);
    },
    [data, checkForChanges]
  );

  // Function to manually import data from tab 1.1
  const handleImportFromTab11 = useCallback(async () => {
    setIsLoadingSection11(true);
    try {
      // Refresh section 1.1 data from database first
      await fetchSection11Data();

      // Use the current section11Data state (will be updated by the fetch)
      const tab11Data = await fetchTabDataByName(productId, "1.1");
      if (tab11Data?.fields) {
        const competitors =
          (tab11Data.fields.competitors as Competitor[]) || [];
        if (competitors.length === 0) {
          toast.error("No competitor data found in tab 1.1 to import");
          return;
        }

        const transposedData = transposeCompetitorData(competitors);
        const productFeatures = createProductFeaturesData(competitors);
        const productAnalysis = createProductAnalysisData(competitors);

        updateData({
          transposedData,
          productFeatures,
          productAnalysis,
        });
        toast.success(
          `Imported ${competitors.length} competitors from tab 1.1`
        );
      } else {
        toast.error("No competitor data found in tab 1.1 to import");
      }
    } catch (error) {
      console.error("Error importing from tab 1.1:", error);
      toast.error("Failed to import data from tab 1.1");
    } finally {
      setIsLoadingSection11(false);
    }
  }, [
    fetchSection11Data,
    productId,
    transposeCompetitorData,
    createProductFeaturesData,
    createProductAnalysisData,
    updateData,
  ]);

  // Functions for managing differentiation ideas
  const addDifferentiationIdea = useCallback(() => {
    const newIdea: DifferentiationIdea = {
      id: Date.now().toString(),
      title: "",
      description: "",
      user: user?.name || "",
      createdAt: new Date().toISOString(),
      isEdited: false,
    };
    updateData({
      differentiationIdeas: [...(data.differentiationIdeas || []), newIdea],
    });
  }, [data.differentiationIdeas, updateData, user]);

  const updateDifferentiationIdea = useCallback(
    (id: string, updates: Partial<DifferentiationIdea>) => {
      const updatedIdeas = (data.differentiationIdeas || []).map((idea) =>
        idea.id === id ? { ...idea, ...updates } : idea
      );
      updateData({ differentiationIdeas: updatedIdeas });
    },
    [data.differentiationIdeas, updateData]
  );

  const removeDifferentiationIdea = useCallback(
    (id: string) => {
      const filteredIdeas = (data.differentiationIdeas || []).filter(
        (idea) => idea.id !== id
      );
      updateData({ differentiationIdeas: filteredIdeas });
    },
    [data.differentiationIdeas, updateData]
  );

  // Reset function to restore original data
  const resetToOriginalData = useCallback(() => {
    setData(originalData);
    setModifiedFields(new Set());
  }, [originalData]);

  // Handle edit mode transitions
  useEffect(() => {
    if (prevIsEditing && !isEditing) {
      // Exiting edit mode - reset to original data
      resetToOriginalData();
    }
    setPrevIsEditing(isEditing);
  }, [isEditing, prevIsEditing, resetToOriginalData]);

  // Save function
  const save = useCallback(async () => {
    if (!hasUnsavedChanges) return;

    try {
      const dataToSave = {
        ...data,
        user: user?.name || "",
        editedAt: new Date().toISOString(),
        editedBy: user?.name || "",
        isEdited: true,
      };

      await updateTabData(tabId, dataToSave as Record<string, unknown>);

      // Dispatch event to trigger progress indicator update
      window.dispatchEvent(new CustomEvent("refreshNotifications"));

      setOriginalData(dataToSave);
      setData(dataToSave);
      setHasUnsavedChanges(false);
      toast.success("Section 1.3 updated successfully");
    } catch (error) {
      console.error("Error saving section 1.3:", error);
      toast.error("Failed to save section 1.3 changes");
      throw error;
    }
  }, [hasUnsavedChanges, tabId, data, user]);

  // Expose save function to parent
  useImperativeHandle(
    ref,
    () => ({
      save,
    }),
    [save]
  );

  // Handle cell value changes in the transposed table
  const handleCellChange = useCallback(
    (rowIndex: number, competitorKey: string, value: string) => {
      const newTransposedData = [...data.transposedData];
      newTransposedData[rowIndex] = {
        ...newTransposedData[rowIndex],
        [competitorKey]: value,
      };
      updateData({ transposedData: newTransposedData });

      // Track modified fields
      const fieldKey = `${rowIndex}_${competitorKey}`;
      const originalValue =
        originalData.transposedData?.[rowIndex]?.[competitorKey] || "";
      setModifiedFields((prev) => {
        const newModified = new Set(prev);
        if (value !== originalValue) {
          newModified.add(fieldKey);
        } else {
          newModified.delete(fieldKey);
        }
        return newModified;
      });
    },
    [data.transposedData, updateData, originalData.transposedData]
  );

  // Use centralized form field styling
  useFormFieldStyles(modifiedFields);

  // Get competitor count for column headers
  const competitorCount = section11Data?.competitors?.length || 0;
  const hasTransposedData =
    data.transposedData && data.transposedData.length > 0;

  return (
    <div className="h-full w-full flex flex-col">
      {/* Competitor Reference Table - Fixed at Top */}
      {data.transposedData && data.transposedData.length > 0 && (
        <div className="flex-shrink-0 bg-background z-30">
          <div
            className="pt-6 px-6 overflow-y-scroll"
            style={{
              maxHeight: "600px",
              scrollbarWidth: "auto",
              scrollbarColor: "rgba(0,0,0,0.1) transparent",
            }}
          >
            <Card className="border-b-0 rounded-b-none">
              <CardContent className="pt-2 pb-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    <span className="text-lg font-semibold">
                      Competitor Analysis
                    </span>
                    {competitorCount > 0 && (
                      <Badge variant="secondary">
                        {competitorCount} competitor
                        {competitorCount !== 1 ? "s" : ""}
                      </Badge>
                    )}
                  </div>
                  {isEditing &&
                    section11Data?.competitors &&
                    section11Data.competitors.length > 0 && (
                      <Button
                        onClick={handleImportFromTab11}
                        variant="outline"
                        size="sm"
                        disabled={isLoadingSection11}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        {isLoadingSection11
                          ? "Loading..."
                          : "Import from Tab 1.1"}
                      </Button>
                    )}
                </div>
                <div>
                  <table className={TABLE_STYLES.table}>
                    <tbody>
                      <tr>
                        {/* Empty cell to match label column width */}
                        <td
                          className={`${TABLE_STYLES.bodyCell} font-medium w-40`}
                        >
                          <div className="px-2 py-2"></div>
                        </td>
                        {/* Competitor columns */}
                        {Array.from(
                          { length: competitorCount },
                          (_, competitorIndex) => {
                            const competitorKey = `competitor_${competitorIndex}`;
                            const competitorImage = data.transposedData?.find(
                              (row) => row.field === "imageUrl"
                            )?.[competitorKey];
                            const competitorBrand = data.transposedData?.find(
                              (row) => row.field === "brand"
                            )?.[competitorKey];

                            return (
                              <td
                                key={competitorIndex}
                                className={`${
                                  TABLE_STYLES.bodyCell
                                } text-center ${
                                  hoveredColumn === competitorKey
                                    ? "bg-muted/60"
                                    : "hover:bg-muted/40"
                                } transition-colors cursor-pointer`}
                                onMouseEnter={() =>
                                  setHoveredColumn(competitorKey)
                                }
                                onMouseLeave={() => setHoveredColumn(null)}
                              >
                                <div className="px-1 py-2 text-xs flex flex-col items-center gap-1">
                                  {/* Product Image */}
                                  {competitorImage ? (
                                    <Image
                                      src={competitorImage}
                                      alt={`Competitor ${competitorIndex + 1}`}
                                      width={80}
                                      height={80}
                                      className="w-20 h-20 object-cover rounded border"
                                      onError={(e) => {
                                        const target =
                                          e.target as HTMLImageElement;
                                        target.style.display = "none";
                                      }}
                                    />
                                  ) : (
                                    <div className="w-20 h-20 bg-muted rounded border flex items-center justify-center">
                                      <span className="text-xs text-muted-foreground">
                                        No Image
                                      </span>
                                    </div>
                                  )}
                                  {/* Brand Name */}
                                  <div className="text-xs font-bold text-foreground text-center leading-tight">
                                    {competitorBrand ||
                                      `Competitor ${competitorIndex + 1}`}
                                  </div>
                                </div>
                              </td>
                            );
                          }
                        )}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-6 pb-6">
          <Card className="border-t-0 rounded-t-none">
            <CardContent className="p-0">
              <div className="space-y-6 p-6 pt-0">
                {/* Transposed Competitor Analysis */}
                {hasTransposedData ? (
                  <TabsTableContainer maxHeight="600px" height="fit-content">
                    <table className={TABLE_STYLES.table}>
                      <tbody className={TABLE_STYLES.bodyContainer}>
                        {data.transposedData
                          .filter(
                            (row) =>
                              row.field !== "imageUrl" && row.field !== "brand"
                          )
                          .map((row, rowIndex) => (
                            <tr
                              key={row.field}
                              className={`${TABLE_STYLES.bodyRow.base} ${
                                rowIndex % 2 === 0
                                  ? TABLE_STYLES.bodyRow.even
                                  : TABLE_STYLES.bodyRow.odd
                              }`}
                            >
                              <td
                                className={`${TABLE_STYLES.bodyCell} bg-muted/40 font-medium w-40`}
                              >
                                <div className="px-2 py-2 text-foreground">
                                  <span>{row.label}</span>
                                </div>
                              </td>
                              {Array.from(
                                { length: competitorCount },
                                (_, competitorIndex) => {
                                  const competitorKey = `competitor_${competitorIndex}`;
                                  const cellValue = row[competitorKey] || "";
                                  const fieldKey = `${rowIndex}_${competitorKey}`;
                                  const isModified =
                                    modifiedFields.has(fieldKey);
                                  const isCalculatedField =
                                    row.field === "listingAge" ||
                                    row.field === "salesToReviewRatio";
                                  const isAsinLinkField =
                                    row.field === "asinWithLink";
                                  const isImageField = row.field === "imageUrl";
                                  const isBrandField = row.field === "brand";
                                  const isRevenueField =
                                    row.field === "asinRevenue";

                                  // Parse ASIN+Link data if it's the combined field
                                  let asinData = null;
                                  if (isAsinLinkField && cellValue) {
                                    try {
                                      asinData = JSON.parse(cellValue);
                                    } catch {
                                      asinData = { asin: cellValue, url: "" };
                                    }
                                  }

                                  return (
                                    <td
                                      key={competitorKey}
                                      className={`${
                                        TABLE_STYLES.bodyCell
                                      } text-center ${
                                        isImageField ? "h-32" : ""
                                      } ${
                                        hoveredColumn === competitorKey
                                          ? "bg-muted/60"
                                          : "hover:bg-muted/40"
                                      } transition-colors`}
                                      onMouseEnter={() =>
                                        setHoveredColumn(competitorKey)
                                      }
                                      onMouseLeave={() =>
                                        setHoveredColumn(null)
                                      }
                                    >
                                      {isEditing && !isCalculatedField ? (
                                        isImageField ? (
                                          <Input
                                            value={cellValue}
                                            onChange={(e) =>
                                              handleCellChange(
                                                rowIndex,
                                                competitorKey,
                                                e.target.value
                                              )
                                            }
                                            className={`${
                                              isModified
                                                ? "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200"
                                                : "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300"
                                            } h-6 text-xs`}
                                            style={{ fontSize: "12px" }}
                                            placeholder="Image URL"
                                          />
                                        ) : isAsinLinkField ? (
                                          <div className="flex items-center gap-1">
                                            <Input
                                              value={asinData?.asin || ""}
                                              onChange={(e) => {
                                                const newAsinData = {
                                                  asin: e.target.value,
                                                  url: asinData?.url || "",
                                                };
                                                handleCellChange(
                                                  rowIndex,
                                                  competitorKey,
                                                  JSON.stringify(newAsinData)
                                                );
                                              }}
                                              className={`${
                                                isModified
                                                  ? "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200"
                                                  : "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300"
                                              } h-6 text-xs flex-1`}
                                              style={{ fontSize: "12px" }}
                                              placeholder="ASIN"
                                            />
                                            <Input
                                              value={asinData?.url || ""}
                                              onChange={(e) => {
                                                const newAsinData = {
                                                  asin: asinData?.asin || "",
                                                  url: e.target.value,
                                                };
                                                handleCellChange(
                                                  rowIndex,
                                                  competitorKey,
                                                  JSON.stringify(newAsinData)
                                                );
                                              }}
                                              className={`${
                                                isModified
                                                  ? "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200"
                                                  : "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300"
                                              } h-6 text-xs flex-1`}
                                              style={{ fontSize: "12px" }}
                                              placeholder="URL"
                                            />
                                          </div>
                                        ) : (
                                          <Input
                                            value={cellValue}
                                            onChange={(e) =>
                                              handleCellChange(
                                                rowIndex,
                                                competitorKey,
                                                e.target.value
                                              )
                                            }
                                            className={`${
                                              isModified
                                                ? "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200"
                                                : "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300"
                                            } h-6 text-xs`}
                                            style={{ fontSize: "12px" }}
                                            placeholder="-"
                                          />
                                        )
                                      ) : isImageField ? (
                                        <div className="flex items-center justify-center p-3 h-full">
                                          {cellValue ? (
                                            <Image
                                              src={cellValue}
                                              alt="Product"
                                              width={96}
                                              height={96}
                                              className="h-24 w-24 object-cover rounded border cursor-pointer hover:border-blue-300 transition-all"
                                              onClick={() => {
                                                navigator.clipboard.writeText(
                                                  cellValue
                                                );
                                                toast.success(
                                                  "Image URL copied to clipboard!"
                                                );
                                              }}
                                              onError={(e) => {
                                                const target =
                                                  e.target as HTMLImageElement;
                                                target.style.display = "none";
                                                target.nextElementSibling?.classList.remove(
                                                  "hidden"
                                                );
                                              }}
                                              title="Click to copy image URL"
                                            />
                                          ) : null}
                                          <div
                                            className={`text-xs text-muted-foreground ${
                                              cellValue ? "hidden" : ""
                                            }`}
                                          >
                                            No image
                                          </div>
                                        </div>
                                      ) : isAsinLinkField ? (
                                        <div className="flex items-center justify-center gap-1">
                                          <div
                                            className="px-1 py-0.5 rounded border-2 border-transparent cursor-pointer hover:border-blue-300 transition-all h-6 flex items-center justify-center text-xs"
                                            style={{ fontSize: "12px" }}
                                            onClick={() => {
                                              if (asinData?.asin) {
                                                navigator.clipboard.writeText(
                                                  asinData.asin
                                                );
                                                toast.success(
                                                  "ASIN copied to clipboard!"
                                                );
                                              }
                                            }}
                                            title={`Click to copy ASIN: ${
                                              asinData?.asin || "-"
                                            }`}
                                          >
                                            {asinData?.asin || "-"}
                                          </div>
                                          {asinData?.url && (
                                            <button
                                              className="p-1 rounded hover:bg-muted transition-colors cursor-pointer"
                                              onClick={() =>
                                                window.open(
                                                  asinData.url,
                                                  "_blank"
                                                )
                                              }
                                              title="Open link in new tab"
                                            >
                                              <ExternalLink className="h-3 w-3 cursor-pointer" />
                                            </button>
                                          )}
                                        </div>
                                      ) : (
                                        <div
                                          className={`px-1 py-0.5 rounded border-2 border-transparent cursor-pointer hover:border-blue-300 focus:border-blue-500 transition-all h-6 flex items-center justify-center overflow-hidden ${
                                            isBrandField
                                              ? "text-sm font-bold"
                                              : "text-xs"
                                          }`}
                                          style={{
                                            fontSize: isBrandField
                                              ? "14px"
                                              : "12px",
                                          }}
                                          title={cellValue || "-"}
                                        >
                                          <div className="relative truncate w-full text-center">
                                            {isRevenueField
                                              ? (() => {
                                                  // Format revenue without decimal places
                                                  const revenueValue =
                                                    parseFloat(
                                                      cellValue?.replace(
                                                        /[$,]/g,
                                                        ""
                                                      ) || "0"
                                                    );
                                                  return revenueValue > 0
                                                    ? `$${revenueValue.toLocaleString(
                                                        undefined,
                                                        {
                                                          maximumFractionDigits: 0,
                                                        }
                                                      )}`
                                                    : cellValue || "-";
                                                })()
                                              : cellValue || "-"}
                                          </div>
                                        </div>
                                      )}
                                    </td>
                                  );
                                }
                              )}
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </TabsTableContainer>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium mb-2">
                      No competitor data available
                    </p>
                    {isLoadingSection11 ? (
                      <p className="text-sm">Loading tab 1.1 data...</p>
                    ) : section11Data?.competitors &&
                      section11Data.competitors.length > 0 ? (
                      <div>
                        <p className="text-sm mb-4">
                          Found {section11Data.competitors.length} competitors
                          in tab 1.1
                        </p>
                        {isEditing && (
                          <Button
                            onClick={handleImportFromTab11}
                            variant="outline"
                            disabled={isLoadingSection11}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            {isLoadingSection11
                              ? "Loading..."
                              : "Import from Tab 1.1"}
                          </Button>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm">
                        Add competitor data in tab 1.1 first, then import it
                        here.
                      </p>
                    )}
                  </div>
                )}

                {/* Key Product Features Card */}
                {data.productFeatures && data.productFeatures.length > 0 && (
                  <TabsTableContainer maxHeight="400px">
                    <table className={TABLE_STYLES.table}>
                      <tbody>
                        {data.productFeatures.map((row, rowIndex) => (
                          <tr
                            key={row.field}
                            className={
                              rowIndex % 2 === 0
                                ? TABLE_STYLES.bodyRow.even
                                : TABLE_STYLES.bodyRow.odd
                            }
                          >
                            {/* Label Column */}
                            <td
                              className={`${TABLE_STYLES.bodyCell} font-medium w-40`}
                            >
                              <div className="px-2 py-2 text-foreground flex items-center">
                                {row.label}
                              </div>
                            </td>

                            {/* Competitor Columns */}
                            {Array.from(
                              { length: competitorCount },
                              (_, competitorIndex) => {
                                const competitorKey = `competitor_${competitorIndex}`;
                                const cellValue = row[competitorKey] || "";
                                return (
                                  <td
                                    key={competitorKey}
                                    className={`${
                                      TABLE_STYLES.bodyCell
                                    } text-center ${
                                      hoveredColumn === competitorKey
                                        ? "bg-muted/60"
                                        : "hover:bg-muted/40"
                                    } transition-colors`}
                                    onMouseEnter={() =>
                                      setHoveredColumn(competitorKey)
                                    }
                                    onMouseLeave={() => setHoveredColumn(null)}
                                  >
                                    {isEditing ? (
                                      <Input
                                        value={cellValue}
                                        onChange={(e) => {
                                          const updatedFeatures =
                                            data.productFeatures.map(
                                              (feature, idx) =>
                                                idx === rowIndex
                                                  ? {
                                                      ...feature,
                                                      [competitorKey]:
                                                        e.target.value,
                                                    }
                                                  : feature
                                            );
                                          updateData({
                                            productFeatures: updatedFeatures,
                                          });
                                        }}
                                        className="h-6 text-xs border-0 bg-transparent focus:bg-white focus:border-blue-500"
                                      />
                                    ) : (
                                      <div className="px-1 py-0.5 text-xs h-6 flex items-center justify-center">
                                        {cellValue || "-"}
                                      </div>
                                    )}
                                  </td>
                                );
                              }
                            )}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </TabsTableContainer>
                )}

                {/* Product Analysis Card */}
                {data.productAnalysis && data.productAnalysis.length > 0 && (
                  <TabsTableContainer maxHeight="400px">
                    <table className={TABLE_STYLES.table}>
                      <tbody>
                        {data.productAnalysis.map((row, rowIndex) => (
                          <tr
                            key={row.field}
                            className={
                              rowIndex % 2 === 0
                                ? TABLE_STYLES.bodyRow.even
                                : TABLE_STYLES.bodyRow.odd
                            }
                          >
                            {/* Label Column */}
                            <td
                              className={`${TABLE_STYLES.bodyCell} font-medium w-40`}
                            >
                              <div className="px-2 py-2 text-foreground flex items-center">
                                <span
                                  className={
                                    row.field.includes("positive")
                                      ? "text-green-600"
                                      : "text-red-600"
                                  }
                                >
                                  {row.label}
                                </span>
                              </div>
                            </td>

                            {/* Competitor Columns */}
                            {Array.from(
                              { length: competitorCount },
                              (_, competitorIndex) => {
                                const competitorKey = `competitor_${competitorIndex}`;
                                const cellValue = row[competitorKey] || "";
                                return (
                                  <td
                                    key={competitorKey}
                                    className={`${
                                      TABLE_STYLES.bodyCell
                                    } text-center ${
                                      hoveredColumn === competitorKey
                                        ? "bg-muted/60"
                                        : "hover:bg-muted/40"
                                    } transition-colors`}
                                    onMouseEnter={() =>
                                      setHoveredColumn(competitorKey)
                                    }
                                    onMouseLeave={() => setHoveredColumn(null)}
                                  >
                                    {isEditing ? (
                                      <Textarea
                                        value={cellValue}
                                        onChange={(e) => {
                                          const updatedAnalysis =
                                            data.productAnalysis.map(
                                              (analysis, idx) =>
                                                idx === rowIndex
                                                  ? {
                                                      ...analysis,
                                                      [competitorKey]:
                                                        e.target.value,
                                                    }
                                                  : analysis
                                            );
                                          updateData({
                                            productAnalysis: updatedAnalysis,
                                          });
                                        }}
                                        className="min-h-[60px] text-xs border-0 bg-transparent focus:bg-white focus:border-blue-500 resize-none"
                                        placeholder="Enter analysis..."
                                      />
                                    ) : (
                                      <div className="px-1 py-2 text-xs min-h-[60px] flex items-start justify-center">
                                        <div className="text-center w-full">
                                          {cellValue || "-"}
                                        </div>
                                      </div>
                                    )}
                                  </td>
                                );
                              }
                            )}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </TabsTableContainer>
                )}

                {/* Differentiation Ideas Card */}
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-orange-600" />
                      <span className="text-lg font-semibold">
                        Differentiation Ideas
                      </span>
                      {data.differentiationIdeas &&
                        data.differentiationIdeas.length > 0 && (
                          <Badge variant="secondary">
                            {data.differentiationIdeas.length} idea
                            {data.differentiationIdeas.length !== 1 ? "s" : ""}
                          </Badge>
                        )}
                    </div>
                    {isEditing && (
                      <Button
                        onClick={addDifferentiationIdea}
                        variant="outline"
                        size="sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Idea
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {data.differentiationIdeas &&
                  data.differentiationIdeas.length > 0 ? (
                    <div className="space-y-4">
                      {data.differentiationIdeas.map((idea, index) => (
                        <div
                          key={idea.id}
                          className="border rounded-lg p-4 space-y-3"
                        >
                          {isEditing ? (
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">
                                  Idea #{index + 1}
                                </span>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() =>
                                    removeDifferentiationIdea(idea.id)
                                  }
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Remove
                                </Button>
                              </div>
                              <Input
                                value={idea.title}
                                onChange={(e) =>
                                  updateDifferentiationIdea(idea.id, {
                                    title: e.target.value,
                                  })
                                }
                                placeholder="Enter idea title..."
                                className="font-medium"
                              />
                              <Textarea
                                value={idea.description}
                                onChange={(e) =>
                                  updateDifferentiationIdea(idea.id, {
                                    description: e.target.value,
                                  })
                                }
                                placeholder="Describe your differentiation idea..."
                                rows={3}
                              />
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium">
                                  {idea.title || `Idea #${index + 1}`}
                                </h4>
                                <span className="text-xs text-muted-foreground">
                                  by {idea.user}
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {idea.description || "No description provided"}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <p className="text-lg font-medium mb-2">
                        No differentiation ideas yet
                      </p>
                      {isEditing ? (
                        <p className="text-sm">
                          Click &quot;Add Idea&quot; to start brainstorming ways
                          to improve your product.
                        </p>
                      ) : (
                        <p className="text-sm">
                          Edit this tab to add differentiation ideas.
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </div>
            </CardContent>
          </Card>

          {/* Tab Danger Zone - Only show in edit mode */}
          {isEditing && tabName && productId && productName && productSlug && (
            <TabDangerZone
              tabId={tabId}
              tabName={tabName}
              productId={productId}
              productName={productName}
              productSlug={productSlug}
              tabCreatorId={tabCreatorId}
              productOwnerId={productOwnerId}
              isEditMode={isEditing}
            />
          )}
        </div>
      </div>
    </div>
  );
});
