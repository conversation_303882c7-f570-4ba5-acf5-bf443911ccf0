"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { fetchAllUsers } from "@/actions/user.actions";

export interface User {
  id: string;
  name: string;
  email: string;
  roles: Array<{ name: string }>;
}

export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUsers = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const fetchedUsers = await fetchAllUsers();
        setUsers(fetchedUsers);
      } catch (err) {
        const errorMessage = "Failed to load users";
        console.error("Error loading users:", err);
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, []);

  return {
    users,
    isLoading,
    error,
    refetch: () => {
      setIsLoading(true);
      setError(null);
      // Re-trigger the effect by updating a dependency
    },
  };
}
