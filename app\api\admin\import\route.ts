import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { getCurrentUser } from "@/lib/auth";

interface ImportOptions {
  mode: "create" | "update" | "overwrite";
  validateData: boolean;
}

interface ImportResult {
  success: boolean;
  summary: {
    totalRecords: number;
    processed: number;
    created: number;
    updated: number;
    errors: number;
  };
  details: Record<
    string,
    { created: number; updated: number; errors: string[] }
  >;
  errors: string[];
}

interface ImportData {
  metadata: {
    exportedAt: string;
    version: string;
    totalRecords: number;
    models: Record<string, number>;
  };
  data: {
    permissions: Record<string, unknown>[];
    roles: Record<string, unknown>[];
    users: Record<string, unknown>[];
    npdProducts: Record<string, unknown>[];
    npdProductTabs: Record<string, unknown>[];
    npdProductTabHistory: Record<string, unknown>[];
    notifications: Record<string, unknown>[];
    npdProductActivityLogs: Record<string, unknown>[];
    npdProductActivityReadStatus: Record<string, unknown>[];
    npdProductChats: Record<string, unknown>[];
    npdProductChatReadStatus: Record<string, unknown>[];
    npdProductSubscriptions: Record<string, unknown>[];
  };
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin privileges
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const hasAdminRole =
      user.roles.includes("ADMIN") || user.roles.includes("SUPER_ADMIN");

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const {
      data: importData,
      options,
    }: { data: ImportData; options: ImportOptions } = body;

    if (!importData || !importData.data) {
      return NextResponse.json(
        { error: "Invalid import data format" },
        { status: 400 }
      );
    }

    const result: ImportResult = {
      success: false,
      summary: {
        totalRecords: 0,
        processed: 0,
        created: 0,
        updated: 0,
        errors: 0,
      },
      details: {},
      errors: [],
    };

    // Calculate total records
    const { data } = importData;
    result.summary.totalRecords = Object.values(data).reduce(
      (sum: number, records: unknown) =>
        sum + (Array.isArray(records) ? records.length : 0),
      0
    );

    try {
      await prisma.$transaction(
        async (tx) => {
          // Import in dependency order to respect foreign key constraints

          // 1. Permissions (no dependencies)
          if (data.permissions?.length > 0) {
            const modelResult = await importModel(
              tx.permission,
              data.permissions,
              "permissions",
              options
            );
            result.details.permissions = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 2. Roles (depends on permissions)
          if (data.roles?.length > 0) {
            const modelResult = await importRoles(tx, data.roles, options);
            result.details.roles = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 3. Users (depends on roles)
          if (data.users?.length > 0) {
            const modelResult = await importUsers(tx, data.users, options);
            result.details.users = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 4. NPD Products (depends on users)
          if (data.npdProducts?.length > 0) {
            const modelResult = await importProducts(
              tx,
              data.npdProducts,
              options
            );
            result.details.npdProducts = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 5. NPD Product Tabs (depends on products)
          if (data.npdProductTabs?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductTab,
              data.npdProductTabs,
              "npdProductTabs",
              options
            );
            result.details.npdProductTabs = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 6. NPD Product Tab History (depends on product tabs)
          if (data.npdProductTabHistory?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductTabHistory,
              data.npdProductTabHistory,
              "npdProductTabHistory",
              options
            );
            result.details.npdProductTabHistory = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 7. Notifications (depends on users)
          if (data.notifications?.length > 0) {
            const modelResult = await importModel(
              tx.notification,
              data.notifications,
              "notifications",
              options
            );
            result.details.notifications = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 8. NPD Product Activity Logs (depends on products, users, product tabs)
          if (data.npdProductActivityLogs?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductActivityLog,
              data.npdProductActivityLogs,
              "activityLogs",
              options
            );
            result.details.activityLogs = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 9. NPD Product Chat Messages (depends on products and users)
          if (data.npdProductChats?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductChat,
              data.npdProductChats,
              "npdProductChats",
              options
            );
            result.details.npdProductChats = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 10. NPD Product Activity Read Status (depends on users, activities, products)
          if (data.npdProductActivityReadStatus?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductActivityReadStatus,
              data.npdProductActivityReadStatus,
              "activityReadStatus",
              options
            );
            result.details.activityReadStatus = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 11. NPD Product Chat Read Status (depends on users and chat messages)
          if (data.npdProductChatReadStatus?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductChatReadStatus,
              data.npdProductChatReadStatus,
              "npdProductChatReadStatus",
              options
            );
            result.details.npdProductChatReadStatus = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }

          // 12. NPD Product Subscriptions (depends on users and products)
          if (data.npdProductSubscriptions?.length > 0) {
            const modelResult = await importModel(
              tx.nPDProductSubscription,
              data.npdProductSubscriptions,
              "npdProductSubscriptions",
              options
            );
            result.details.npdProductSubscriptions = modelResult;
            result.summary.created += modelResult.created;
            result.summary.updated += modelResult.updated;
            result.summary.errors += modelResult.errors.length;
          }
        },
        {
          timeout: 60000, // 60 seconds timeout for large imports
        }
      );

      result.summary.processed =
        result.summary.created + result.summary.updated;
      result.success = result.summary.errors === 0;

      return NextResponse.json(result);
    } catch (error) {
      console.error("Import transaction failed:", error);
      result.errors.push(
        error instanceof Error ? error.message : "Unknown transaction error"
      );
      result.success = false;
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error("Import error:", error);
    return NextResponse.json(
      { error: "Failed to import database" },
      { status: 500 }
    );
  }
}

// Generic import function for simple models
async function importModel(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  model: any,
  records: Record<string, unknown>[],
  modelName: string,
  options: ImportOptions
) {
  const result = { created: 0, updated: 0, errors: [] as string[] };

  for (const record of records) {
    try {
      const { id, ...data } = record;

      if (options.mode === "create") {
        // Create new record
        await model.create({ data });
        result.created++;
      } else if (options.mode === "update") {
        // Try to update existing, skip if not found
        try {
          await model.update({
            where: { id: id as string },
            data,
          });
          result.updated++;
        } catch {
          // Record doesn't exist, skip
        }
      } else if (options.mode === "overwrite") {
        // Upsert (create or update)
        await model.upsert({
          where: { id: id as string },
          update: data,
          create: { id, ...data },
        });
        result.updated++; // We'll count upserts as updates
      }
    } catch (error) {
      result.errors.push(
        `${modelName}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  return result;
}

// Special import function for roles (handles many-to-many with permissions)
async function importRoles(
  tx: Parameters<Parameters<typeof prisma.$transaction>[0]>[0],
  roles: Record<string, unknown>[],
  options: ImportOptions
) {
  const result = { created: 0, updated: 0, errors: [] as string[] };

  for (const role of roles) {
    let roleId = "unknown";
    try {
      const { id, permissions, ...data } = role;
      // Remove users field as it's handled during user import
      delete (data as Record<string, unknown>).users;
      roleId = id as string;

      // Convert permissions array to proper format for Prisma
      let permissionConnect = undefined;
      if (Array.isArray(permissions) && permissions.length > 0) {
        // Validate that all permissions exist before trying to connect them
        const permissionIds = (permissions as { id: string }[]).map(
          (p) => p.id
        );
        const existingPermissions = await tx.permission.findMany({
          where: { id: { in: permissionIds } },
          select: { id: true },
        });

        const existingIds = existingPermissions.map((p) => p.id);
        const validPermissions = (permissions as { id: string }[]).filter((p) =>
          existingIds.includes(p.id)
        );

        if (validPermissions.length !== permissions.length) {
          const missingIds = permissionIds.filter(
            (id) => !existingIds.includes(id)
          );
          console.warn(
            `Role ${roleId}: Missing permissions: ${missingIds.join(", ")}`
          );
        }

        permissionConnect =
          validPermissions.length > 0
            ? validPermissions.map((p: { id: string }) => ({ id: p.id }))
            : undefined;
      }

      // Validate required fields
      if (!data.name || typeof data.name !== "string") {
        throw new Error(`Role ${roleId} is missing required 'name' field`);
      }

      if (options.mode === "create") {
        await tx.role.create({
          data: {
            name: data.name as string,
            permissions: permissionConnect
              ? { connect: permissionConnect }
              : undefined,
          },
        });
        result.created++;
      } else if (options.mode === "update") {
        try {
          await tx.role.update({
            where: { id: roleId },
            data: {
              ...data,
              permissions: permissionConnect
                ? { set: permissionConnect }
                : undefined,
            },
          });
          result.updated++;
        } catch {
          // Record doesn't exist, skip
        }
      } else if (options.mode === "overwrite") {
        await tx.role.upsert({
          where: { id: roleId },
          update: {
            ...data,
            permissions: permissionConnect
              ? { set: permissionConnect }
              : undefined,
          },
          create: {
            id: roleId,
            name: data.name as string,
            permissions: permissionConnect
              ? { connect: permissionConnect }
              : undefined,
          },
        });
        result.updated++;
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`Role import error for ${roleId}:`, errorMessage);
      result.errors.push(`roles (${roleId}): ${errorMessage}`);
    }
  }

  return result;
}

// Special import function for users (handles many-to-many with roles)
async function importUsers(
  tx: Parameters<Parameters<typeof prisma.$transaction>[0]>[0],
  users: Record<string, unknown>[],
  options: ImportOptions
) {
  const result = { created: 0, updated: 0, errors: [] as string[] };

  for (const user of users) {
    try {
      const { id, roles, ...data } = user;

      // Convert roles array to proper format for Prisma
      const roleConnect = Array.isArray(roles)
        ? roles.map((r: { id: string }) => ({ id: r.id }))
        : undefined;

      if (options.mode === "create") {
        await tx.user.create({
          data: {
            name: (data.name as string) || "Unknown User",
            email:
              (data.email as string) || `unknown-${Date.now()}@example.com`,
            ...data,
            roles: roleConnect ? { connect: roleConnect } : undefined,
          },
        });
        result.created++;
      } else if (options.mode === "update") {
        try {
          await tx.user.update({
            where: { id: id as string },
            data: {
              ...data,
              roles: roleConnect ? { set: roleConnect } : undefined,
            },
          });
          result.updated++;
        } catch {
          // Record doesn't exist, skip
        }
      } else if (options.mode === "overwrite") {
        await tx.user.upsert({
          where: { id: id as string },
          update: {
            ...data,
            roles: roleConnect ? { set: roleConnect } : undefined,
          },
          create: {
            id: id as string,
            name: (data.name as string) || "Unknown User",
            email: (data.email as string) || `unknown-${id}@example.com`,
            ...data,
            roles: roleConnect ? { connect: roleConnect } : undefined,
          },
        });
        result.updated++;
      }
    } catch (error) {
      result.errors.push(
        `users: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  return result;
}

// Special import function for products (handles relationship with users)
async function importProducts(
  tx: Parameters<Parameters<typeof prisma.$transaction>[0]>[0],
  products: Record<string, unknown>[],
  options: ImportOptions
) {
  const result = { created: 0, updated: 0, errors: [] as string[] };

  for (const product of products) {
    try {
      const { id, user, ...data } = product;

      // Handle user relationship - if user is an object with id, convert to userId
      const productData = { ...data };
      if (user && typeof user === "object" && "id" in user) {
        productData.userId = (user as { id: string }).id;
      } else if (typeof user === "string") {
        productData.userId = user;
      }

      if (options.mode === "create") {
        await tx.nPDProduct.create({
          data: {
            name: (productData.name as string) || "Unknown",
            slug: (productData.slug as string) || `unknown-${Date.now()}`,
            brand: (productData.brand as string) || "Unknown",
            stage: (productData.stage as string) || "Ideation",
            ...productData,
          },
        });
        result.created++;
      } else if (options.mode === "update") {
        try {
          await tx.nPDProduct.update({
            where: { id: id as string },
            data: productData,
          });
          result.updated++;
        } catch {
          // Record doesn't exist, skip
        }
      } else if (options.mode === "overwrite") {
        await tx.nPDProduct.upsert({
          where: { id: id as string },
          update: productData,
          create: {
            id: id as string,
            name: (productData.name as string) || "Unknown",
            slug: (productData.slug as string) || `unknown-${id}`,
            brand: (productData.brand as string) || "Unknown",
            stage: (productData.stage as string) || "Ideation",
            ...productData,
          },
        });
        result.updated++;
      }
    } catch (error) {
      result.errors.push(
        `products: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  return result;
}
