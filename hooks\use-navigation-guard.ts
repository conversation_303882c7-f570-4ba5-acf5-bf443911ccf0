import { useEffect, useCallback, useState } from "react";
import { useRouter } from "next/navigation";

interface NavigationGuardOptions {
  hasUnsavedChanges: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export function useNavigationGuard({
  hasUnsavedChanges,
  onConfirm,
  onCancel,
}: NavigationGuardOptions) {
  const router = useRouter();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null
  );

  // Handle programmatic navigation with confirmation
  const navigateWithConfirmation = useCallback(
    (url: string) => {
      if (hasUnsavedChanges) {
        setPendingNavigation(url);
        setShowConfirmDialog(true);
      } else {
        router.push(url);
      }
    },
    [hasUnsavedChanges, router]
  );

  // Handle confirmation dialog result
  const handleConfirm = useCallback(() => {
    setShowConfirmDialog(false);
    if (pendingNavigation) {
      onConfirm?.();
      router.push(pendingNavigation);
      setPendingNavigation(null);
    }
  }, [pendingNavigation, router, onConfirm]);

  const handleCancel = useCallback(() => {
    setShowConfirmDialog(false);
    setPendingNavigation(null);
    onCancel?.();
  }, [onCancel]);

  // Still keep beforeunload for external navigation (browser back/forward, typing URL, etc.)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue =
          "You have unsaved changes. Are you sure you want to leave?";
        return "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges]);

  return {
    navigateWithConfirmation,
    showConfirmDialog,
    handleConfirm,
    handleCancel,
    pendingNavigation,
  };
}
