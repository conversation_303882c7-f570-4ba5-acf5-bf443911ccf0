"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tab<PERSON>eader, LoadingButton } from "../shared";
import {
  Package,
  Shield,
  Settings,
  DollarSign,
  TrendingUp,
  BarChart3,
  ImageIcon,
} from "lucide-react";
import { getTabOrder } from "@/lib/constants/tab-order";
import { toast } from "sonner";

interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

interface AddSourcingBriefTabProps {
  productName: string;
  onBack: () => void;
  onSave: (tabData: TabData) => Promise<void>;
}

export function AddSourcingBriefTab({
  productName,
  onBack,
  onSave,
}: AddSourcingBriefTabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const formData = {
    // Compliance Section
    certification: null,
    patent: null,
    instructionsManual: null,
    packagingType: null,

    // Product Specifications Section
    material: null,
    formShape: null,
    color: null,
    netContentCapacity: null,
    dimensions: null,
    formulation: null,
    application: null,
    productFeatures: null,
    productFeaturesArray: null,

    // Target FBA Fee Section
    retailPackaging: null,
    packagingDimensions: null,
    packagingWeight: null,
    fbaFee: null,

    // Price / Variation Section
    targetProductAsin: null,
    gravitiqRetailPrice: null,
    monthlyTargetSales: null,
    variation: null,

    // Competitor Analysis Section
    competitorRating: null,
    upgradeAddedValue: null,
    issues: null,
    issuesResolved: null,

    // Reference Images Section
    images: [],

    // Meta fields
    user: null,
    editedAt: new Date().toISOString(),
    editedBy: null,
    isEdited: false,
    versions: [],
  };

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsLoading(true);

    try {
      await onSave({
        tabName: "Sourcing Brief",
        order: getTabOrder("Sourcing Brief"),
        fields: formData,
      });
      // Success toast will be shown after page reload via URL parameters
    } catch (error) {
      console.error("Error creating sourcing brief tab:", error);
      toast.error("Failed to create sourcing brief tab. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <TabHeader
        title="Add Sourcing Brief Tab"
        description="Create a sourcing brief tab for {productName}"
        productName={productName}
        onBack={onBack}
        actionButton={
          <LoadingButton
            isLoading={isLoading}
            onClick={handleSubmit}
            loadingText="Creating..."
            defaultText="Create Sourcing Brief"
          />
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Tab Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                Sourcing Brief Tab
              </CardTitle>
              <CardDescription>
                This tab will provide comprehensive sourcing requirements with
                detailed compliance, specifications, pricing, and competitor
                analysis for your product.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <Shield className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-medium">Compliance</h3>
                    <p className="text-sm text-muted-foreground">
                      Certifications, patents, and packaging requirements
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <Settings className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium">Product Specifications</h3>
                    <p className="text-sm text-muted-foreground">
                      Materials, dimensions, features, and formulation
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium">Target FBA Fee</h3>
                    <p className="text-sm text-muted-foreground">
                      Packaging details and FBA fee calculations
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <TrendingUp className="h-8 w-8 text-orange-600" />
                  <div>
                    <h3 className="font-medium">Price / Variation</h3>
                    <p className="text-sm text-muted-foreground">
                      Target pricing, sales goals, and variations
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-8 w-8 text-red-600" />
                  <div>
                    <h3 className="font-medium">Competitor Analysis</h3>
                    <p className="text-sm text-muted-foreground">
                      Competitive advantages and market positioning
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <ImageIcon className="h-8 w-8 text-indigo-600" />
                  <div>
                    <h3 className="font-medium">Reference Images</h3>
                    <p className="text-sm text-muted-foreground">
                      Visual references and product images
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What you'll get */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your sourcing brief tab will include these sections that you can
                edit and customize after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Compliance</Badge>
                  <span className="text-sm text-muted-foreground">
                    Certifications, patents, instructions manual, and packaging
                    type
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Product Specifications</Badge>
                  <span className="text-sm text-muted-foreground">
                    Materials, form/shape, color, dimensions, formulation,
                    application, and multiple product features
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Target FBA Fee</Badge>
                  <span className="text-sm text-muted-foreground">
                    Retail packaging, dimensions, weight, and FBA fee
                    calculations
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Price / Variation</Badge>
                  <span className="text-sm text-muted-foreground">
                    Target product ASIN, Gravitiq retail price, monthly sales
                    targets, and variations
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Competitor Analysis</Badge>
                  <span className="text-sm text-muted-foreground">
                    Competitor ratings, upgrade added value, issues, and
                    resolutions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Reference Images</Badge>
                  <span className="text-sm text-muted-foreground">
                    Upload and manage visual references and product images
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
