import { ReactNode } from "react";
import { Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import Spinner from "@/components/spinner";

interface LoadingButtonProps {
  isLoading: boolean;
  onClick: (e?: React.FormEvent | React.MouseEvent) => void | Promise<void>;
  disabled?: boolean;
  loadingText: string;
  defaultText: string;
  icon?: ReactNode;
  className?: string;
}

export function LoadingButton({
  isLoading,
  onClick,
  disabled = false,
  loadingText,
  defaultText,
  icon = <Save className="h-4 w-4" />,
  className = "flex items-center gap-2",
}: LoadingButtonProps) {
  return (
    <Button
      onClick={onClick}
      disabled={isLoading || disabled}
      className={className}
    >
      {isLoading ? <Spinner loading={true} size={8} /> : icon}
      {isLoading ? loadingText : defaultText}
    </Button>
  );
}
