"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Settings, Plus } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import Spinner from "@/components/spinner";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string | null;
  roles: { name: string }[];
  createdAt: string;
  lastLoginAt?: string | null;
}

interface Role {
  id: string;
  name: string;
  description?: string | null;
  permissions?: { id: string; code: string }[];
  _count?: { users: number };
}

export function UserRoleManager() {
  const { user: currentUser, refreshUserData } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  // Check if user has admin access
  const hasAdminAccess =
    currentUser?.roles?.includes("ADMIN") ||
    currentUser?.roles?.includes("SUPER_ADMIN");

  // Filter roles based on current user permissions
  const availableRoles = (roles || []).filter((role) => {
    // Super admins can see all roles
    if (currentUser?.roles?.includes("SUPER_ADMIN")) {
      return true;
    }
    // Regular admins cannot see SUPER_ADMIN role
    return role.name !== "SUPER_ADMIN";
  });

  const fetchUsersAndRoles = useCallback(async () => {
    try {
      const [usersResponse, rolesResponse] = await Promise.all([
        fetch("/api/admin/users"),
        fetch("/api/admin/roles"),
      ]);

      if (usersResponse.ok && rolesResponse.ok) {
        const usersData = await usersResponse.json();
        const rolesData = await rolesResponse.json();

        // Filter users based on current user permissions
        const filteredUsers = usersData.filter((user: User) => {
          // Super admins can see all users
          if (currentUser?.roles?.includes("SUPER_ADMIN")) {
            return true;
          }
          // Regular admins cannot see users with SUPER_ADMIN role
          return !user.roles.some((role) => role.name === "SUPER_ADMIN");
        });

        setUsers(filteredUsers);
        setRoles(rolesData);
      } else {
        console.error("Failed to fetch users or roles");
        console.error("Users response status:", usersResponse.status);
        console.error("Roles response status:", rolesResponse.status);

        // Try to get error details
        try {
          if (!usersResponse.ok) {
            const usersError = await usersResponse.text();
            console.error("Users API error:", usersError);
          }
          if (!rolesResponse.ok) {
            const rolesError = await rolesResponse.text();
            console.error("Roles API error:", rolesError);
          }
        } catch (e) {
          console.error("Error reading response details:", e);
        }

        toast.error("Failed to load data. Check console for details.");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Error loading users and roles");
    } finally {
      setLoading(false);
    }
  }, [currentUser?.roles]); // Add currentUser dependencies

  useEffect(() => {
    fetchUsersAndRoles();
  }, [fetchUsersAndRoles]);

  const openRoleDialog = (user: User) => {
    setSelectedUser(user);
    // Filter roles based on current user permissions
    const visibleRoles = user.roles.filter((role) => {
      // Super admins can see all roles
      if (currentUser?.roles?.includes("SUPER_ADMIN")) {
        return true;
      }
      // Regular admins cannot see SUPER_ADMIN role
      return role.name !== "SUPER_ADMIN";
    });
    setSelectedRoles(visibleRoles.map((role) => role.name));
    setDialogOpen(true);
  };

  const addRole = async (userId: string, roleId: string) => {
    setUpdating(userId);
    try {
      const response = await fetch(`/api/admin/users/${userId}/roles`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ roleId }),
      });

      if (response.ok) {
        toast.success("Role added successfully");
        fetchUsersAndRoles();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to add role");
      }
    } catch (error) {
      console.error("Error adding role:", error);
      toast.error("Error adding role");
    } finally {
      setUpdating(null);
    }
  };

  const updateUserRoles = async (userId: string, newRoleIds: string[]) => {
    setUpdating(userId);
    try {
      const response = await fetch(`/api/admin/users/${userId}/roles`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ roleIds: newRoleIds }),
      });

      if (response.ok) {
        toast.success("User roles updated successfully");
        fetchUsersAndRoles();
        setDialogOpen(false);
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to update user roles");
      }
    } catch (error) {
      console.error("Error updating user roles:", error);
      toast.error("Error updating user roles");
    } finally {
      setUpdating(null);
    }
  };

  const handleRoleToggle = (roleName: string) => {
    setSelectedRoles((prev) =>
      prev.includes(roleName)
        ? prev.filter((r) => r !== roleName)
        : [...prev, roleName]
    );
  };

  const saveUserRoles = () => {
    if (!selectedUser) return;

    // Get role IDs for the selected roles
    const selectedRoleIds = selectedRoles
      .map(
        (roleName) => availableRoles.find((role) => role.name === roleName)?.id
      )
      .filter(Boolean) as string[];

    // If current user is not a super admin, preserve existing SUPER_ADMIN roles
    if (!currentUser?.roles?.includes("SUPER_ADMIN")) {
      const existingSuperAdminRoles = selectedUser.roles
        .filter((role) => role.name === "SUPER_ADMIN")
        .map((role) => roles.find((r) => r.name === role.name)?.id)
        .filter(Boolean) as string[];

      // Combine selected roles with preserved SUPER_ADMIN roles
      const finalRoleIds = [...selectedRoleIds, ...existingSuperAdminRoles];
      updateUserRoles(selectedUser.id, finalRoleIds);
    } else {
      // Super admins can modify all roles
      updateUserRoles(selectedUser.id, selectedRoleIds);
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeVariant = (roleName: string) => {
    switch (roleName) {
      case "SUPER_ADMIN":
        return "destructive";
      case "ADMIN":
        return "default";
      case "MARKETING":
      case "ACCOUNTING":
      case "SUPPLY_CHAIN":
      case "EXECUTIVE":
      case "M&A_REGULATORY":
      case "EU_TEAM":
      case "OPS_TEAM":
      case "CREATIVE":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Check if user has admin access
  if (!hasAdminAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Access Denied</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            You need Admin or Super Admin role to access user management.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Current roles: {currentUser?.roles?.join(", ") || "None"}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner loading={true} size={12} />
          <span className="ml-2">Loading users...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              User Management
              <Badge variant="secondary">Admin Access</Badge>
              {availableRoles.length > 0 && (
                <Badge variant="outline">{availableRoles.length} Roles</Badge>
              )}
              {availableRoles.length === 0 && roles.length === 0 && (
                <Badge variant="destructive">No Roles Loaded</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Manage user roles and permissions. Changes take effect
              immediately.
              {!currentUser?.roles?.includes("SUPER_ADMIN") && (
                <span className="block mt-1 text-sm text-amber-600">
                  Note: As an Admin, you can manage most users but cannot see or
                  modify Super Admin users.
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                await refreshUserData();
                await fetchUsersAndRoles();
              }}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Current Roles</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={
                          user.image ||
                          `https://api.dicebear.com/5.x/initials/svg?seed=${user.name}`
                        }
                        alt={user.name}
                      />
                      <AvatarFallback className="text-xs">
                        {getUserInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {user.email}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {(() => {
                    // Filter roles based on current user permissions
                    const visibleRoles = user.roles.filter((role) => {
                      // Super admins can see all roles
                      if (currentUser?.roles?.includes("SUPER_ADMIN")) {
                        return true;
                      }
                      // Regular admins cannot see SUPER_ADMIN role
                      return role.name !== "SUPER_ADMIN";
                    });

                    return visibleRoles.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {visibleRoles.map((role) => (
                          <Badge
                            key={role.name}
                            variant={getRoleBadgeVariant(role.name)}
                            className="text-xs"
                          >
                            {role.name.replace("_", " ")}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <Badge variant="outline">No Visible Roles</Badge>
                    );
                  })()}
                </TableCell>
                <TableCell>
                  {new Date(user.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {user.lastLoginAt
                    ? new Date(user.lastLoginAt).toLocaleDateString()
                    : "Never"}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Dialog
                      open={dialogOpen && selectedUser?.id === user.id}
                      onOpenChange={(open) => {
                        setDialogOpen(open);
                        if (open) openRoleDialog(user);
                      }}
                    >
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={updating === user.id}
                        >
                          {updating === user.id ? (
                            <Spinner loading={true} size={8} />
                          ) : (
                            <Settings className="h-4 w-4" />
                          )}
                          <span className="ml-1 hidden sm:inline">
                            Manage Roles
                          </span>
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>
                            Manage Roles for {user.name}
                          </DialogTitle>
                          <DialogDescription>
                            Select the roles you want to assign to this user.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-3 py-4">
                          {availableRoles.map((role) => (
                            <div
                              key={role.id}
                              className="flex items-center space-x-3"
                            >
                              <Checkbox
                                id={`role-${role.id}`}
                                checked={selectedRoles.includes(role.name)}
                                onCheckedChange={() =>
                                  handleRoleToggle(role.name)
                                }
                                disabled={updating === user.id}
                              />
                              <label
                                htmlFor={`role-${role.id}`}
                                className="flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                              >
                                <div className="flex items-center justify-between">
                                  <span>{role.name.replace("_", " ")}</span>
                                  <Badge
                                    variant={getRoleBadgeVariant(role.name)}
                                    className="text-xs"
                                  >
                                    {role.name.replace("_", " ")}
                                  </Badge>
                                </div>
                                {role.description && (
                                  <div className="text-xs text-muted-foreground mt-1">
                                    {role.description}
                                  </div>
                                )}
                              </label>
                            </div>
                          ))}

                          {selectedRoles.length === 0 && (
                            <div className="text-center py-4 text-muted-foreground text-sm">
                              No roles selected. User will not have access to
                              the dashboard.
                            </div>
                          )}
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            onClick={() => setDialogOpen(false)}
                            disabled={updating === user.id}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={saveUserRoles}
                            disabled={updating === user.id}
                          >
                            {updating === user.id ? (
                              <>
                                <Spinner loading={true} size={8} />
                                <span className="ml-2">Saving...</span>
                              </>
                            ) : (
                              "Save Changes"
                            )}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>

                    <Select
                      onValueChange={(roleId) => addRole(user.id, roleId)}
                      disabled={updating === user.id}
                    >
                      <SelectTrigger className="w-28">
                        <SelectValue placeholder="+ Role" />
                      </SelectTrigger>
                      <SelectContent>
                        {(() => {
                          const filteredRoles = availableRoles.filter(
                            (role) =>
                              !user.roles.some(
                                (userRole) => userRole.name === role.name
                              )
                          );

                          if (filteredRoles.length === 0) {
                            return (
                              <SelectItem value="no-roles" disabled>
                                <span className="text-muted-foreground">
                                  {availableRoles.length === 0
                                    ? "No roles available"
                                    : "All roles already assigned"}
                                </span>
                              </SelectItem>
                            );
                          }

                          return filteredRoles.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              <div className="flex items-center space-x-2">
                                <Plus className="h-3 w-3" />
                                <span>{role.name.replace("_", " ")}</span>
                              </div>
                            </SelectItem>
                          ));
                        })()}
                      </SelectContent>
                    </Select>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
