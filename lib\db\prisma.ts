import { PrismaClient } from "@/lib/generated/prisma";
import { Pool, neonConfig } from "@neondatabase/serverless";
import { PrismaNeon } from "@prisma/adapter-neon";
import {
  getServerlessDbUrl,
  SERVERLESS_DB_CONFIG,
} from "@/lib/config/serverless";
import ws from "ws";

// Sets up WebSocket connections for Neon serverless
neonConfig.webSocketConstructor = ws;

// Note: fetchConnectionCache is now always enabled by default in newer Neon versions

// Get serverless-optimized database URL
const databaseUrl = getServerlessDbUrl();

// Serverless-optimized Neon connection configuration
const config = {
  connectionString: databaseUrl,
  pool: {
    max: SERVERLESS_DB_CONFIG.MAX_CONNECTIONS, // 1 connection per function
    min: 0, // No persistent connections in serverless
    idleTimeoutMillis: 0, // Close immediately after use
    acquireTimeoutMillis: SERVERLESS_DB_CONFIG.CONNECTION_TIMEOUT,
    createTimeoutMillis: SERVERLESS_DB_CONFIG.CONNECTION_TIMEOUT,
  },
};

// Create a connection pool with the configuration
const pool = new Pool(config);

// Create Prisma Neon adapter with the pool's underlying configuration
const adapter = new PrismaNeon(pool.options);

// Create PrismaClient instance with the Neon adapter
export const prisma = new PrismaClient({ adapter });
