import { BeatLoader } from "react-spinners";
import { FC, useState, useEffect } from "react";
import { useTheme } from "next-themes";

interface SpinnerProps {
  loading: boolean;
  size?: number;
  color?: string;
}

const Spinner: FC<SpinnerProps> = ({ loading, size = 10, color }) => {
  const [mounted, setMounted] = useState(false);
  const { theme, resolvedTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  // If a custom color is provided, use it
  if (color) {
    return <BeatLoader loading={loading} color={color} size={size} />;
  }

  // If not mounted yet, use a default color to avoid hydration issues
  if (!mounted) {
    return <BeatLoader loading={loading} color="#ffffff" size={size} />;
  }

  // Use theme-aware colors
  const currentTheme = resolvedTheme || theme;
  const spinnerColor = currentTheme === "dark" ? "#ffffff" : "#374151";

  return <BeatLoader loading={loading} color={spinnerColor} size={size} />;
};

export default Spinner;
