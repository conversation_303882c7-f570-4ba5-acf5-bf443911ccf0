"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, usePathname } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PackageIcon, SearchIcon, XIcon, Plus } from "lucide-react";
import Image from "next/image";
import { useTheme } from "next-themes";
import Spinner from "@/components/spinner";
import { useNPDContext } from "@/context/npd-context";
import { useNavigationGuard } from "@/context/navigation-guard-context";
import {
  PRODUCT_STAGE_VALUES,
  formatProductStageLabel,
  type ProductStage,
} from "@/lib/constants/product-stages";
import type { ProductSummary } from "@/types";

// Brand logo mapping
const brandLogoMap: Record<string, string> = {
  "Perfect Remedy": "pr",
  "grace & stella": "gs",
  LeGushe: "lg",
  Baebody: "bb",
  "Protect Life": "pl",
  "Venus Visage": "vv",
  Ballotte: "bl",
  EZPIK: "ep",
  Kiddycare: "kc",
  "Dr. Arthritis": "da",
  "Dr. Moritz": "dm",
  Pilpoc: "pp",
};

// Helper function to get logo path
const getBrandLogo = (brandName: string, resolvedTheme: string | undefined) => {
  const logoCode = brandLogoMap[brandName];
  if (!logoCode) return null;

  // Default to light mode if theme is not resolved yet
  const isDark = resolvedTheme === "dark";
  return `/images/${logoCode}_logo${isDark ? "_dark" : ""}.png`;
};

export function NPDSidebar() {
  const {
    allProducts: products,
    isLoadingProducts,
    error,
    selectProductBySlug,
    selectedProduct,
    clearSelectedProduct,
  } = useNPDContext();
  const router = useRouter();
  const pathname = usePathname();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBrand, setSelectedBrand] = useState<string>("");
  const [selectedStage, setSelectedStage] = useState<string>("");
  const [filteredProducts, setFilteredProducts] = useState<ProductSummary[]>(
    []
  );
  const [isInitialized, setIsInitialized] = useState(false);
  const { resolvedTheme } = useTheme();
  const { navigateWithCustomAction } = useNavigationGuard();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Extract current product slug from pathname
  const getProductSlugFromPath = (pathname: string) => {
    const pathParts = pathname.split("/");
    // Handle routes like /dashboard/npd/[slug] and /dashboard/npd/[slug]/settings
    if (pathParts.includes("npd") && pathParts.length >= 4) {
      const productIndex = pathParts.indexOf("npd");
      return pathParts[productIndex + 1]; // Get the slug after "npd"
    }
    return null;
  };

  const currentSlug = getProductSlugFromPath(pathname);
  const selectedProductId = currentSlug
    ? products.find((p) => p.slug === currentSlug)?.id
    : undefined;

  // Get all unique brands
  const allBrands = Array.from(
    new Set(products.map((product) => product.brand))
  ).sort();

  // Get available stages based on selected brand
  const getAvailableStages = () => {
    const relevantProducts =
      selectedBrand && selectedBrand !== "all"
        ? products.filter((p) => p.brand === selectedBrand)
        : products;

    return Array.from(
      new Set(relevantProducts.map((product) => product.stage))
    ).sort();
  };

  // Initialize selected brand from localStorage on component mount
  useEffect(() => {
    const savedBrand = localStorage.getItem("npd_selected_brand");
    if (savedBrand && allBrands.includes(savedBrand)) {
      setSelectedBrand(savedBrand);
    }
    const savedStage = localStorage.getItem("npd_selected_stage");
    if (
      savedStage &&
      PRODUCT_STAGE_VALUES.includes(savedStage as ProductStage)
    ) {
      setSelectedStage(savedStage);
    }
    setIsInitialized(true);
  }, [allBrands]);

  // Listen for localStorage changes to sync brand filter updates from other components
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "npd_selected_brand") {
        const newBrand = e.newValue;
        if (newBrand && allBrands.includes(newBrand)) {
          setSelectedBrand(newBrand);
        } else if (newBrand === null) {
          setSelectedBrand("");
        }
      }
    };

    // Also listen for custom storage events (for same-tab updates)
    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === "npd_selected_brand") {
        const newBrand = e.detail.newValue;
        if (newBrand && allBrands.includes(newBrand)) {
          setSelectedBrand(newBrand);
        } else if (newBrand === null) {
          setSelectedBrand("");
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener(
      "localStorageChange",
      handleCustomStorageChange as EventListener
    );

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "localStorageChange",
        handleCustomStorageChange as EventListener
      );
    };
  }, [allBrands]);

  // Filter products based on search term, selected brand, and selected stage
  useEffect(() => {
    if (!isInitialized) return; // Wait for initialization

    let filtered = products;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.slug.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply brand filter
    if (selectedBrand && selectedBrand !== "all") {
      filtered = filtered.filter((product) => product.brand === selectedBrand);
    }

    // Apply stage filter
    if (selectedStage && selectedStage !== "all") {
      filtered = filtered.filter((product) => product.stage === selectedStage);
    }

    setFilteredProducts(filtered);
  }, [searchTerm, selectedBrand, selectedStage, products, isInitialized]);

  const handleBrandChange = (brand: string) => {
    setSelectedBrand(brand);

    // Reset stage filter when brand changes
    setSelectedStage("");
    localStorage.removeItem("npd_selected_stage");

    // Check if currently selected product belongs to the new brand filter
    if (selectedProduct && brand && brand !== "all") {
      if (selectedProduct.brand !== brand) {
        // Clear selected product if it doesn't belong to the filtered brand
        clearSelectedProduct();
        // Navigate to products list without specific product
        router.push("/dashboard/npd");
      }
    }

    // Save selected brand to localStorage
    if (brand && brand !== "all") {
      localStorage.setItem("npd_selected_brand", brand);
    } else {
      localStorage.removeItem("npd_selected_brand");
    }
  };

  const handleStageChange = (stage: string) => {
    setSelectedStage(stage);

    // Check if currently selected product belongs to the new stage filter
    if (selectedProduct && stage && stage !== "all") {
      if (selectedProduct.stage !== stage) {
        // Clear selected product if it doesn't belong to the filtered stage
        clearSelectedProduct();
        // Navigate to products list without specific product
        router.push("/dashboard/npd");
      }
    }

    // Save selected stage to localStorage
    if (stage && stage !== "all") {
      localStorage.setItem("npd_selected_stage", stage);
    } else {
      localStorage.removeItem("npd_selected_stage");
    }
  };

  const handleBrandLogoClick = (brand: string) => {
    handleBrandChange(brand);
    // Scroll to top of the products list to show the filtered brand
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  };

  const clearBrandFilter = () => {
    setSelectedBrand("");
    localStorage.removeItem("npd_selected_brand");
    // Note: We keep the selected product when clearing the brand filter
    // since the user may want to see all brands while keeping their current product selection
  };

  const clearStageFilter = () => {
    setSelectedStage("");
    localStorage.removeItem("npd_selected_stage");
    // Note: We keep the selected product when clearing the stage filter
    // since the user may want to see all stages while keeping their current product selection
  };

  // Group filtered products by brand
  const productsByBrand = filteredProducts.reduce((acc, product) => {
    const brand = product.brand;
    if (!acc[brand]) {
      acc[brand] = [];
    }
    acc[brand].push(product);
    return acc;
  }, {} as Record<string, ProductSummary[]>);

  const totalProducts = filteredProducts.length;

  // Show loading state
  if (isLoadingProducts) {
    return (
      <div className="h-full border-r bg-background flex flex-col">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2 mb-3">
            <PackageIcon className="h-5 w-5 text-primary" />
            <h2 className="font-semibold">Products</h2>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="mb-2">
              <Spinner loading={true} size={12} />
            </div>
            <p className="text-sm text-muted-foreground">Loading products...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="h-full border-r bg-background flex flex-col">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2 mb-3">
            <PackageIcon className="h-5 w-5 text-primary" />
            <h2 className="font-semibold">Products</h2>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center p-4">
            <PackageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load products
            </p>
            <p className="text-xs text-muted-foreground">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full border-r bg-background flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <PackageIcon className="h-5 w-5 text-primary" />
            <h2 className="font-semibold">Products</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/dashboard/npd/new")}
            className="h-8 w-8 p-0 cursor-pointer hover:bg-muted/50"
            title="Add New Product"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        <div className="mt-4 space-y-2">
          {/* Brand Filter */}
          <div className="flex items-center gap-2">
            <Select value={selectedBrand} onValueChange={handleBrandChange}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="All brands" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  All brands ({products.length})
                </SelectItem>
                {allBrands.map((brand) => {
                  const brandProductCount = products.filter(
                    (p) => p.brand === brand
                  ).length;
                  return (
                    <SelectItem key={brand} value={brand}>
                      {brand} ({brandProductCount})
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {selectedBrand && selectedBrand !== "all" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearBrandFilter}
                className="h-10 px-2 text-xs"
              >
                <XIcon className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Stage Filter */}
          <div className="flex items-center gap-2">
            <Select value={selectedStage} onValueChange={handleStageChange}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="All stages" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  All stages (
                  {selectedBrand && selectedBrand !== "all"
                    ? products.filter((p) => p.brand === selectedBrand).length
                    : products.length}
                  )
                </SelectItem>
                {getAvailableStages().map((stage) => {
                  const stageProductCount = (
                    selectedBrand && selectedBrand !== "all"
                      ? products.filter(
                          (p) => p.brand === selectedBrand && p.stage === stage
                        )
                      : products.filter((p) => p.stage === stage)
                  ).length;
                  return (
                    <SelectItem key={stage} value={stage}>
                      {formatProductStageLabel(stage)} ({stageProductCount})
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {selectedStage && selectedStage !== "all" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearStageFilter}
                className="h-10 px-2 text-xs"
              >
                <XIcon className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* NPD List */}
      <div className="flex-1 overflow-y-auto" ref={scrollContainerRef}>
        <div className="p-4 space-y-4">
          {totalProducts === 0 ? (
            <div className="text-center py-8">
              <PackageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                {searchTerm
                  ? "No NPD products found"
                  : "No NPD products available"}
              </p>
            </div>
          ) : (
            Object.entries(productsByBrand)
              .sort(([a], [b]) => a.localeCompare(b))
              .map(([brand, brandProducts]) => (
                <div key={brand} className="space-y-2 mb-8">
                  {/* Brand Header */}
                  <div className="flex items-center justify-center">
                    {getBrandLogo(brand, resolvedTheme) ? (
                      <Image
                        src={getBrandLogo(brand, resolvedTheme)!}
                        alt={`${brand} logo`}
                        height={160}
                        width={160}
                        priority={true}
                        className={`${
                          !selectedBrand || selectedBrand === "all"
                            ? "cursor-pointer hover:scale-105 transition-transform duration-200 hover:shadow-lg rounded-lg"
                            : ""
                        }`}
                        onClick={() => {
                          if (!selectedBrand || selectedBrand === "all") {
                            handleBrandLogoClick(brand);
                          }
                        }}
                        title={
                          !selectedBrand || selectedBrand === "all"
                            ? `Filter by ${brand}`
                            : undefined
                        }
                      />
                    ) : (
                      <h3
                        className={`text-sm font-medium text-muted-foreground ${
                          !selectedBrand || selectedBrand === "all"
                            ? "cursor-pointer hover:text-foreground transition-colors"
                            : ""
                        }`}
                        onClick={() => {
                          if (!selectedBrand || selectedBrand === "all") {
                            handleBrandLogoClick(brand);
                          }
                        }}
                        title={
                          !selectedBrand || selectedBrand === "all"
                            ? `Filter by ${brand}`
                            : undefined
                        }
                      >
                        {brand}
                      </h3>
                    )}
                  </div>

                  {/* Brand Products */}
                  <div className="space-y-2">
                    {brandProducts
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((product) => (
                        <Card
                          key={product.id}
                          className={`cursor-pointer transition-all hover:shadow-sm ${
                            selectedProductId === product.id
                              ? "ring-2 ring-primary bg-primary/5"
                              : "hover:bg-muted/50"
                          }`}
                          onClick={async () => {
                            // Define the navigation action with product selection
                            const navigationAction = async () => {
                              try {
                                await selectProductBySlug(product.slug);
                                router.push(`/dashboard/npd/${product.slug}`);
                              } catch (error) {
                                console.error(
                                  "Error selecting product:",
                                  error
                                );
                                router.push(`/dashboard/npd/${product.slug}`);
                              }
                            };

                            // Use the new navigation guard with custom action
                            navigateWithCustomAction(navigationAction);
                          }}
                        >
                          <CardContent className="px-4 py-2 h-4 relative flex items-center">
                            {/* Product name with fixed height and line clamping */}
                            <h4 className="font-medium text-sm leading-tight line-clamp-2 overflow-hidden">
                              {product.name}
                            </h4>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </div>
              ))
          )}
        </div>
      </div>
    </div>
  );
}
