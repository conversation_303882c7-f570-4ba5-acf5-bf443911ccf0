import { PrismaClient } from "../lib/generated/prisma";

const prisma = new PrismaClient();

// Default Roles
const DEFAULT_ROLES = [
  "SUPER_ADMIN",
  "ADMIN",
  "MARKETING",
  "ACCOUNTING",
  "SUPPLY_CHAIN",
  "EXECUTIVE",
  "M&A_REGULATORY",
  "EU_TEAM",
  "OPS_TEAM",
  "CREATIVE",
];

// Default Permissions organized by category
const DEFAULT_PERMISSIONS = {
  // Product Management
  PRODUCT: [
    "PRODUCT_VIEW",
    "PRODUCT_CREATE",
    "PRODUCT_EDIT",
    "PRODUCT_DELETE",
    "PRODUCT_EDIT_NAME",
    "PRODUCT_EDIT_BRAND",
    "PRODUCT_EDIT_STAGE",
  ],

  // Product Tabs
  PRODUCT_TABS: [
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_CREATE",
    "PRODUCT_TAB_EDIT",
    "PRODUCT_TAB_DELETE",
    "PRODUCT_TAB_EDIT_OVERVIEW",
    "PRODUCT_TAB_EDIT_SOURCING_BRIEF",
    "PRODUCT_TAB_EDIT_SECTION_1_1",
    "PRODUCT_TAB_EDIT_SECTION_1_2",
    "PRODUCT_TAB_EDIT_SECTION_1_3",
  ],

  // Communication & Collaboration
  COMMUNICATION: [
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "CHAT_DELETE_ANY",
    "CHAT_MENTION_USERS",
  ],

  // Activity & Notifications
  ACTIVITY: [
    "ACTIVITY_VIEW",
    "ACTIVITY_CREATE",
    "NOTIFICATION_VIEW",
    "NOTIFICATION_SEND",
    "NOTIFICATION_MANAGE",
  ],

  // User Management
  USER_MANAGEMENT: [
    "USER_VIEW",
    "USER_EDIT_PROFILE",
    "USER_MANAGE_ROLES",
    "USER_DELETE",
    "USER_VIEW_ALL",
  ],

  // Admin Panel
  ADMIN: [
    "ADMIN_PANEL_ACCESS",
    "ADMIN_USER_MANAGEMENT",
    "ADMIN_ROLE_MANAGEMENT",
    "ADMIN_PERMISSION_MANAGEMENT", // Super Admin only
    "ADMIN_SYSTEM_SETTINGS",
    "ADMIN_DATABASE_MANAGEMENT",
  ],

  // Data Operations
  DATA: ["DATA_EXPORT", "DATA_IMPORT", "DATA_BACKUP", "DATA_RESTORE"],

  // System
  SYSTEM: ["SYSTEM_SETTINGS", "SYSTEM_MAINTENANCE", "SYSTEM_LOGS"],
};

// Default Role-Permission Mappings
const DEFAULT_ROLE_PERMISSIONS = {
  SUPER_ADMIN: [
    // All permissions
    ...Object.values(DEFAULT_PERMISSIONS).flat(),
  ],

  ADMIN: [
    // Product permissions
    ...DEFAULT_PERMISSIONS.PRODUCT,
    ...DEFAULT_PERMISSIONS.PRODUCT_TABS,

    // Communication
    ...DEFAULT_PERMISSIONS.COMMUNICATION,
    ...DEFAULT_PERMISSIONS.ACTIVITY,

    // User management (except permission management)
    "USER_VIEW",
    "USER_EDIT_PROFILE",
    "USER_MANAGE_ROLES",
    "USER_VIEW_ALL",

    // Admin panel (except permission management)
    "ADMIN_PANEL_ACCESS",
    "ADMIN_USER_MANAGEMENT",
    "ADMIN_ROLE_MANAGEMENT",
    "ADMIN_SYSTEM_SETTINGS",

    // Data operations
    ...DEFAULT_PERMISSIONS.DATA,
  ],

  MARKETING: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_OVERVIEW",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "CHAT_MENTION_USERS",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
    "DATA_EXPORT",
  ],

  ACCOUNTING: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_SOURCING_BRIEF",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
    "DATA_EXPORT",
  ],

  SUPPLY_CHAIN: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_SOURCING_BRIEF",
    "PRODUCT_TAB_EDIT_SECTION_1_1",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
  ],

  EXECUTIVE: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
    "DATA_EXPORT",
  ],

  "M&A_REGULATORY": [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_SOURCING_BRIEF",
    "PRODUCT_TAB_EDIT_SECTION_1_2",
    "PRODUCT_TAB_EDIT_SECTION_1_3",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
  ],

  EU_TEAM: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_SECTION_1_2",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
  ],

  OPS_TEAM: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_SECTION_1_1",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "ACTIVITY_CREATE",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
  ],

  CREATIVE: [
    "PRODUCT_VIEW",
    "PRODUCT_TAB_VIEW",
    "PRODUCT_TAB_EDIT_OVERVIEW",
    "CHAT_VIEW",
    "CHAT_SEND",
    "CHAT_DELETE_OWN",
    "ACTIVITY_VIEW",
    "NOTIFICATION_VIEW",
    "USER_EDIT_PROFILE",
  ],
};

// Helper function to get all permissions as flat array
function getAllPermissions(): string[] {
  return Object.values(DEFAULT_PERMISSIONS).flat();
}

// Helper function to find permission ID by code
function findPermissionId(
  code: string,
  permissions: { id: string; code: string }[]
): string {
  const permission = permissions.find((p) => p.code === code);
  if (!permission) {
    throw new Error(`Permission with code "${code}" not found`);
  }
  return permission.id;
}

async function main() {
  console.log("🌱 Starting database seeding...");

  // Determine seeding strategy
  const resetAll = process.argv.includes("--reset-all");
  const syncOnly = process.argv.includes("--sync-only");
  const nuclear = process.argv.includes("--nuclear");

  if (nuclear) {
    await nuclearReset();
    return;
  }

  if (resetAll) {
    await resetAllStrategy();
    return;
  }

  if (syncOnly) {
    await syncOnlyStrategy();
    return;
  }

  // Default behavior - sync only
  await syncOnlyStrategy();
}

// Strategy 1: Reset everything + add products/roles/permissions
async function resetAllStrategy() {
  console.log(
    "🧹 STRATEGY 1: Reset all data + seed products/roles/permissions"
  );

  // Delete everything except users (preserve login ability)
  await prisma.nPDProductActivityReadStatus.deleteMany({});
  await prisma.nPDProductChatReadStatus.deleteMany({});
  await prisma.nPDProductChat.deleteMany({});
  await prisma.nPDProductSubscription.deleteMany({});
  await prisma.nPDProductActivityLog.deleteMany({});
  await prisma.nPDProductTabHistory.deleteMany({});
  await prisma.nPDProductTab.deleteMany({});
  await prisma.nPDProduct.deleteMany({});
  await prisma.notification.deleteMany({});

  // Clear user role assignments but keep users
  const users = await prisma.user.findMany({ select: { id: true } });
  for (const user of users) {
    await prisma.user.update({
      where: { id: user.id },
      data: { roles: { set: [] } },
    });
  }

  // Clear roles and permissions
  await prisma.role.deleteMany({});
  await prisma.permission.deleteMany({});

  console.log("✅ All data cleared, users preserved");

  // Seed fresh data
  await seedPermissions();
  await seedRoles();
  await seedNPDs();

  console.log("🎉 Strategy 1 completed - fresh start with core data!");
}

// Strategy 2: Sync products/roles/permissions, keep other data
async function syncOnlyStrategy() {
  console.log(
    "🔄 STRATEGY 2: Sync products/roles/permissions, preserve other data"
  );

  await seedPermissions();
  await seedRoles();
  await seedNPDs();

  console.log("🎉 Strategy 2 completed - data synced!");
}

// Strategy 3: Nuclear - delete everything
async function nuclearReset() {
  // Delete EVERYTHING including users
  await prisma.nPDProductActivityReadStatus.deleteMany({});
  await prisma.nPDProductChatReadStatus.deleteMany({});
  await prisma.nPDProductChat.deleteMany({});
  await prisma.nPDProductSubscription.deleteMany({});
  await prisma.nPDProductActivityLog.deleteMany({});
  await prisma.nPDProductTabHistory.deleteMany({});
  await prisma.nPDProductTab.deleteMany({});
  await prisma.nPDProduct.deleteMany({});
  await prisma.notification.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.role.deleteMany({});
  await prisma.permission.deleteMany({});
}

// Seed permissions
async function seedPermissions() {
  console.log("📋 Creating permissions...");

  const allPermissions = getAllPermissions();
  const permissions = await Promise.all(
    allPermissions.map((permissionCode) =>
      prisma.permission.upsert({
        where: { code: permissionCode },
        update: {},
        create: { code: permissionCode },
      })
    )
  );

  console.log(`✅ Created/updated ${permissions.length} permissions`);
  return permissions;
}

// Seed roles with permissions
async function seedRoles() {
  console.log("👥 Creating roles with permissions...");

  const permissions = await prisma.permission.findMany();

  for (const roleName of DEFAULT_ROLES) {
    const rolePermissions =
      DEFAULT_ROLE_PERMISSIONS[
        roleName as keyof typeof DEFAULT_ROLE_PERMISSIONS
      ] || [];
    const permissionConnections = rolePermissions.map(
      (permissionCode: string) => ({
        id: findPermissionId(permissionCode, permissions),
      })
    );

    await prisma.role.upsert({
      where: { name: roleName },
      update: {
        permissions: {
          set: permissionConnections, // Always update to default permissions
        },
      },
      create: {
        name: roleName,
        permissions: {
          connect: permissionConnections,
        },
      },
    });

    console.log(
      `✅ Created/updated role: ${roleName} with ${rolePermissions.length} permissions`
    );
  }
}

// Seed NPD projects (from existing NPD seed file)
async function seedNPDs() {
  console.log("📦 Creating sample NPD projects...");

  // Import and run NPD seeding
  const { seedNPDs: runNPDSeed } = await import("./seed-npds");
  await runNPDSeed();
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
