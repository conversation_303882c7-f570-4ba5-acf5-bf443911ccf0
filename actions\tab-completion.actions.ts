"use server";

/* eslint-disable @typescript-eslint/no-explicit-any */
// Note: This file uses 'any' types for NPDTabCompletion model operations
// because the NPDTabCompletion model uses generated Prisma types.
// This provides type safety while maintaining flexibility.

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";
import { createProductActivityLog } from "@/actions/activity-log.actions";

// Type definitions for tab completion
interface TabCompletionData {
  id: string;
  npdProductId: string;
  npdProductTabId: string;
  completion: number;
  createdAt: Date;
  updatedAt: Date;
  npdProductTab: {
    id: string;
    tabName: string;
    order: number;
  };
}

/**
 * Get tab completion values for a product
 */
export async function getTabCompletions(npdProductId: string) {
  await validateServerAction();

  try {
    // Use NPDProductTabCompletion model
    const completions: TabCompletionData[] = await (
      prisma as any
    ).nPDProductTabCompletion.findMany({
      where: { npdProductId: npdProductId },
      include: {
        npdProductTab: {
          select: {
            id: true,
            tabName: true,
            order: true,
          },
        },
      },
      orderBy: {
        npdProductTab: {
          order: "asc",
        },
      },
    });

    return completions;
  } catch (error) {
    console.error("Error fetching tab completions:", error);

    // If table doesn't exist yet, return empty array
    if (error instanceof Error && error.message.includes("NPDTabCompletion")) {
      console.warn(
        "NPDTabCompletion table not found. Please run database migration."
      );
      return [];
    }

    // For any other error (like no completions found), return empty array instead of throwing
    console.warn("No tab completions found, returning empty array");
    return [];
  }
}

/**
 * Set completion percentage for a specific tab
 */
export async function setTabCompletion(
  npdProductId: string,
  npdProductTabId: string,
  completion: number
) {
  const user = await validateServerAction();

  // Validate completion percentage
  const validCompletion = Math.max(0, Math.min(100, Math.round(completion)));

  try {
    // Check if tab exists and belongs to the product
    const tab = await prisma.nPDProductTab.findFirst({
      where: {
        id: npdProductTabId,
        npdProductId: npdProductId,
      },
    });

    if (!tab) {
      throw new Error("Tab not found or does not belong to this product");
    }

    // Get the previous completion value for activity log
    const previousCompletion = await (
      prisma as any
    ).nPDProductTabCompletion.findUnique({
      where: {
        npdProductId_npdProductTabId: {
          npdProductId: npdProductId,
          npdProductTabId: npdProductTabId,
        },
      },
      select: {
        completion: true,
      },
    });

    // Upsert the completion record
    const result = await (prisma as any).nPDProductTabCompletion.upsert({
      where: {
        npdProductId_npdProductTabId: {
          npdProductId: npdProductId,
          npdProductTabId: npdProductTabId,
        },
      },
      update: {
        completion: validCompletion,
        updatedAt: new Date(),
      },
      create: {
        npdProductId: npdProductId,
        npdProductTabId: npdProductTabId,
        completion: validCompletion,
      },
      include: {
        npdProductTab: {
          select: {
            tabName: true,
          },
        },
      },
    });

    // Create activity log entry
    const previousValue = previousCompletion?.completion || 0;
    const action =
      previousValue === 0 ? "set_completion" : "updated_completion";
    const description =
      previousValue === 0
        ? `Set ${result.npdProductTab.tabName} completion to ${validCompletion}%`
        : `Updated ${result.npdProductTab.tabName} completion from ${previousValue}% to ${validCompletion}%`;

    await createProductActivityLog({
      npdProductId,
      userId: user.userId,
      action,
      description,
      changes: {
        tabCompletion: {
          from: previousValue,
          to: validCompletion,
        },
      },
      metadata: {
        field: "tabCompletion",
        npdProductTabId,
        tabName: result.npdProductTab.tabName,
        previousCompletion: previousValue,
        newCompletion: validCompletion,
      },
    });

    return result;
  } catch (error) {
    console.error("Error setting tab completion:", error);
    throw new Error("Failed to set tab completion");
  }
}

/**
 * Remove completion override for a tab (set to 0)
 */
export async function removeTabCompletion(
  npdProductId: string,
  npdProductTabId: string
) {
  const user = await validateServerAction();

  try {
    // Check if tab exists and belongs to the product
    const tab = await prisma.nPDProductTab.findFirst({
      where: {
        id: npdProductTabId,
        npdProductId: npdProductId,
      },
    });

    if (!tab) {
      throw new Error("Tab not found or does not belong to this product");
    }

    // Get the previous completion value for activity log
    const previousCompletion = await (
      prisma as any
    ).nPDProductTabCompletion.findUnique({
      where: {
        npdProductId_npdProductTabId: {
          npdProductId: npdProductId,
          npdProductTabId: npdProductTabId,
        },
      },
      select: {
        completion: true,
      },
    });

    // Set completion to 0 instead of deleting the record
    const result = await (prisma as any).nPDProductTabCompletion.upsert({
      where: {
        npdProductId_npdProductTabId: {
          npdProductId: npdProductId,
          npdProductTabId: npdProductTabId,
        },
      },
      update: {
        completion: 0,
        updatedAt: new Date(),
      },
      create: {
        npdProductId: npdProductId,
        npdProductTabId: npdProductTabId,
        completion: 0,
      },
      include: {
        npdProductTab: {
          select: {
            tabName: true,
          },
        },
      },
    });

    // Create activity log entry only if there was a previous value > 0
    const previousValue = previousCompletion?.completion || 0;
    if (previousValue > 0) {
      await createProductActivityLog({
        npdProductId,
        userId: user.userId,
        action: "reset_completion",
        description: `Reset ${result.npdProductTab.tabName} completion from ${previousValue}% to 0%`,
        changes: {
          tabCompletion: {
            from: previousValue,
            to: 0,
          },
        },
        metadata: {
          field: "tabCompletion",
          npdProductTabId,
          tabName: result.npdProductTab.tabName,
          previousCompletion: previousValue,
          newCompletion: 0,
        },
      });
    }

    return result;
  } catch (error) {
    console.error("Error removing tab completion:", error);
    throw new Error("Failed to remove tab completion");
  }
}

/**
 * Clear all completion values for a product (set all to 0)
 */
export async function clearAllTabCompletions(npdProductId: string) {
  const user = await validateServerAction();

  try {
    // Get all tabs for the product with their current completion values
    const tabs = await prisma.nPDProductTab.findMany({
      where: { npdProductId: npdProductId },
      select: {
        id: true,
        tabName: true,
      },
    });

    // Get current completion values
    const currentCompletions = await (
      prisma as any
    ).nPDProductTabCompletion.findMany({
      where: { npdProductId: npdProductId },
      select: {
        npdProductTabId: true,
        completion: true,
      },
    });

    // Create a map of current completions
    const completionMap = new Map(
      currentCompletions.map((c: any) => [c.npdProductTabId, c.completion])
    );

    // Update all existing completions to 0 and create missing ones
    const updates = tabs.map((tab) =>
      (prisma as any).nPDProductTabCompletion.upsert({
        where: {
          npdProductId_npdProductTabId: {
            npdProductId: npdProductId,
            npdProductTabId: tab.id,
          },
        },
        update: {
          completion: 0,
          updatedAt: new Date(),
        },
        create: {
          npdProductId: npdProductId,
          npdProductTabId: tab.id,
          completion: 0,
        },
      })
    );

    await prisma.$transaction(updates);

    // Create activity log entry for tabs that had completion > 0
    const tabsWithCompletion = tabs.filter((tab) => {
      const currentCompletion = (completionMap.get(tab.id) as number) || 0;
      return currentCompletion > 0;
    });

    if (tabsWithCompletion.length > 0) {
      const tabNames = tabsWithCompletion.map((tab) => tab.tabName).join(", ");
      await createProductActivityLog({
        npdProductId,
        userId: user.userId,
        action: "cleared_all_completions",
        description: `Cleared completion for all tabs: ${tabNames}`,
        changes: {
          tabCompletions: "all_cleared",
        },
        metadata: {
          field: "tabCompletion",
          action: "bulk_clear",
          tabsCleared: tabsWithCompletion.length,
          totalTabs: tabs.length,
          clearedTabs: tabsWithCompletion.map((tab: any) => ({
            npdProductTabId: tab.id,
            tabName: tab.tabName,
            previousCompletion: (completionMap.get(tab.id) as number) || 0,
          })),
        },
      });
    }

    return { success: true, message: "All tab completions cleared" };
  } catch (error) {
    console.error("Error clearing tab completions:", error);
    throw new Error("Failed to clear tab completions");
  }
}

/**
 * Get overall progress for a product
 */
export async function getProductProgress(npdProductId: string) {
  await validateServerAction();

  try {
    const completions = await (prisma as any).nPDProductTabCompletion.findMany({
      where: { npdProductId: npdProductId },
      include: {
        npdProductTab: {
          select: {
            tabName: true,
            order: true,
          },
        },
      },
    });

    if (completions.length === 0) {
      return { overallProgress: 0, tabCount: 0 };
    }

    const totalCompletion = completions.reduce(
      (sum: number, completion: any) => sum + completion.completion,
      0
    );
    const overallProgress = Math.round(totalCompletion / completions.length);

    return {
      overallProgress,
      tabCount: completions.length,
      completions: completions.map((c: any) => ({
        npdProductTabId: c.npdProductTabId,
        tabName: c.npdProductTab.tabName,
        completion: c.completion,
      })),
    };
  } catch (error) {
    console.error("Error getting product progress:", error);
    throw new Error("Failed to get product progress");
  }
}
