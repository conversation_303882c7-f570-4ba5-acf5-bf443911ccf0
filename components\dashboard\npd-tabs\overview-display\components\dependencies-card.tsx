"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  GitBranch,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  ChevronDown,
  ChevronRight,
  History,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { DeletionRequestDialog } from "@/components/ui/deletion-request-dialog";
import { toast } from "sonner";
import { sendDeletionRequestNotification } from "@/actions/overview-notifications.actions";

interface DependencyVersion {
  id: string;
  title: string;
  type: "internal" | "external";
  status: "resolved" | "pending" | "blocked";
  description?: string;
  dueDate?: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface Dependency {
  id: string;
  title: string;
  type: "internal" | "external";
  status: "resolved" | "pending" | "blocked";
  description?: string;
  dueDate?: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: DependencyVersion[];
}

interface DependenciesCardProps {
  data: Dependency[];
  isEditing: boolean;
  onUpdate: (data: Dependency[]) => void;
  onUserMention?: (username: string) => void;
  productId?: string;
  productName?: string;
  productSlug?: string;
  tabId?: string;
}

const statusIcons = {
  resolved: <CheckCircle className="h-4 w-4" />,
  pending: <Clock className="h-4 w-4" />,
  blocked: <AlertTriangle className="h-4 w-4" />,
};

const statusColors = {
  resolved: "bg-green-100 text-green-800 border-green-200",
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  blocked: "bg-red-100 text-red-800 border-red-200",
};

const typeColors = {
  internal: "bg-blue-100 text-blue-800 border-blue-200",
  external: "bg-purple-100 text-purple-800 border-purple-200",
};

export function DependenciesCard({
  data,
  isEditing,
  onUpdate,
  onUserMention,
  productId,
  productName,
  productSlug,
  tabId,
}: DependenciesCardProps) {
  const [localData, setLocalData] = useState<Dependency[]>(data);
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(
    new Set()
  );
  const [showDeletionDialog, setShowDeletionDialog] = useState(false);
  const [dependencyToDelete, setDependencyToDelete] =
    useState<Dependency | null>(null);
  const { user } = useAuth();

  const handleUserMention = (username: string) => {
    if (onUserMention) {
      onUserMention(username);
    }
  };

  const updateLocalData = (newData: Dependency[]) => {
    setLocalData(newData);
    onUpdate(newData);
  };

  const addDependency = () => {
    const newDependency: Dependency = {
      id: Date.now().toString(),
      title: "",
      type: "internal",
      status: "pending",
      description: "",
      user: user?.name || "", // Automatically populate with current user's name
      isEdited: false, // Explicitly set as not edited for new entries
    };
    updateLocalData([...localData, newDependency]);
  };

  const updateDependency = (id: string, updates: Partial<Dependency>) => {
    const updatedDependencies = localData.map((dependency) =>
      dependency.id === id ? { ...dependency, ...updates } : dependency
    );
    updateLocalData(updatedDependencies);
  };

  const toggleVersions = (dependencyId: string) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(dependencyId)) {
      newExpanded.delete(dependencyId);
    } else {
      newExpanded.add(dependencyId);
    }
    setExpandedVersions(newExpanded);
  };

  const removeDependency = (id: string) => {
    const dependency = localData.find((d) => d.id === id);
    if (!dependency || !user) return;

    const isOwnEntry = dependency.user === user.name;

    if (isOwnEntry) {
      // User can delete their own entry
      const filteredDependencies = localData.filter((d) => d.id !== id);
      updateLocalData(filteredDependencies);
    } else {
      // Show deletion request dialog for others' entries
      setDependencyToDelete(dependency);
      setShowDeletionDialog(true);
    }
  };

  const handleSendDeletionRequest = async () => {
    if (
      !dependencyToDelete ||
      !productId ||
      !productName ||
      !productSlug ||
      !tabId ||
      !dependencyToDelete.user
    ) {
      return;
    }

    const result = await sendDeletionRequestNotification(
      dependencyToDelete.user,
      "dependency",
      dependencyToDelete.title,
      productId,
      productName,
      productSlug,
      tabId,
      dependencyToDelete.id
    );

    // Reset state
    setDependencyToDelete(null);

    // Show result message
    if (result.success) {
      toast.success("Deletion request sent", {
        description: result.message,
      });
    } else {
      toast.error("Failed to send deletion request", {
        description: result.message,
      });
    }
  };

  // Sort dependencies by status
  const sortedDependencies = [...localData].sort((a, b) => {
    const statusOrder = { blocked: 3, pending: 2, resolved: 1 };
    return statusOrder[b.status] - statusOrder[a.status];
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-orange-500" />
            Dependencies
          </CardTitle>
          {isEditing && (
            <Button size="sm" variant="outline" onClick={addDependency}>
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sortedDependencies.map((dependency) => (
            <div
              key={dependency.id}
              id={`entry-dependency-${dependency.id}`}
              className="border rounded-lg p-3 space-y-3"
            >
              {isEditing ? (
                <div className="space-y-3">
                  {/* Title */}
                  <Input
                    placeholder="Dependency title"
                    value={dependency.title}
                    onChange={(e) =>
                      updateDependency(dependency.id, { title: e.target.value })
                    }
                  />

                  {/* Type and Status */}
                  <div className="grid grid-cols-2 gap-2">
                    <Select
                      value={dependency.type}
                      onValueChange={(value: Dependency["type"]) =>
                        updateDependency(dependency.id, { type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="internal">Internal</SelectItem>
                        <SelectItem value="external">External</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select
                      value={dependency.status}
                      onValueChange={(value: Dependency["status"]) =>
                        updateDependency(dependency.id, { status: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="blocked">Blocked</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Due Date */}
                  <div className="grid grid-cols-1 gap-2">
                    <Input
                      type="date"
                      placeholder="Due date"
                      value={dependency.dueDate || ""}
                      onChange={(e) =>
                        updateDependency(dependency.id, {
                          dueDate: e.target.value,
                        })
                      }
                    />
                  </div>

                  {/* Description */}
                  <Textarea
                    placeholder="Description (optional)"
                    value={dependency.description || ""}
                    onChange={(e) =>
                      updateDependency(dependency.id, {
                        description: e.target.value,
                      })
                    }
                    rows={2}
                  />

                  {/* Remove Button */}
                  <div className="flex justify-end">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeDependency(dependency.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {/* Version toggle icon on the left */}
                      {dependency.versions &&
                        dependency.versions.length > 0 && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleVersions(dependency.id)}
                            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
                          >
                            {expandedVersions.has(dependency.id) ? (
                              <ChevronDown className="h-3 w-3" />
                            ) : (
                              <ChevronRight className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                      {statusIcons[dependency.status]}
                      <h4 className="font-medium">{dependency.title}</h4>
                      {dependency.isEdited && (
                        <Badge variant="secondary" className="text-xs">
                          edited
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge className={typeColors[dependency.type]}>
                        {dependency.type}
                      </Badge>
                    </div>
                  </div>

                  {/* Status Badge */}
                  <div className="flex items-center gap-2">
                    <Badge className={statusColors[dependency.status]}>
                      {dependency.status}
                    </Badge>
                  </div>

                  {/* Details */}
                  {(dependency.dueDate ||
                    dependency.user ||
                    (dependency.editedAt && dependency.editedBy)) && (
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {dependency.dueDate && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(dependency.dueDate).toLocaleDateString()}
                        </div>
                      )}
                      {dependency.user && (
                        <div className="flex items-center gap-1">
                          <button
                            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm bg-blue-50 px-1 py-0.5 rounded-sm"
                            onClick={() =>
                              dependency.user &&
                              handleUserMention(dependency.user)
                            }
                          >
                            @{dependency.user}
                          </button>
                        </div>
                      )}
                      {dependency.editedAt &&
                        dependency.editedBy &&
                        dependency.editedBy !== dependency.user && (
                          <div className="text-xs text-muted-foreground">
                            edited by @{dependency.editedBy} on{" "}
                            {new Date(dependency.editedAt).toLocaleDateString()}
                          </div>
                        )}
                    </div>
                  )}

                  {/* Description */}
                  {dependency.description && (
                    <p className="text-sm text-muted-foreground">
                      {dependency.description}
                    </p>
                  )}

                  {/* Previous versions */}
                  {dependency.versions &&
                    dependency.versions.length > 0 &&
                    expandedVersions.has(dependency.id) && (
                      <div className="mt-3 pt-3 border-t border-dashed">
                        <div className="flex items-center gap-2 mb-2">
                          <History className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground font-medium">
                            Previous Versions
                          </span>
                        </div>
                        <div className="space-y-2 ml-4">
                          {[...dependency.versions].reverse().map((version) => (
                            <div
                              key={version.id}
                              className="p-2 bg-muted/30 rounded text-sm"
                            >
                              <div className="flex items-center gap-2">
                                {statusIcons[version.status]}
                                <div className="font-medium text-muted-foreground">
                                  {version.title}
                                </div>
                                <Badge variant="outline">{version.type}</Badge>
                                <Badge variant="outline">
                                  {version.status}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                <span>by @{version.user}</span>

                                {version.dueDate && (
                                  <>
                                    <span>•</span>
                                    <span>
                                      due{" "}
                                      {new Date(
                                        version.dueDate
                                      ).toLocaleDateString()}
                                    </span>
                                  </>
                                )}
                              </div>
                              {version.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {version.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          ))}

          {localData.length === 0 && !isEditing && (
            <div className="text-center py-6 text-muted-foreground">
              <GitBranch className="h-6 w-6 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No dependencies tracked</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Deletion Request Dialog */}
      <DeletionRequestDialog
        open={showDeletionDialog}
        onOpenChange={setShowDeletionDialog}
        creatorUsername={dependencyToDelete?.user || ""}
        entryType="dependency"
        entryTitle={dependencyToDelete?.title || ""}
        onSendRequest={handleSendDeletionRequest}
        onCancel={() => setDependencyToDelete(null)}
      />
    </Card>
  );
}
