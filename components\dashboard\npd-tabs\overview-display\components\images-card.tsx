"use client";

import React from "react";
import { Image as ImageIcon } from "lucide-react";
import { EnhancedImageCard } from "../../shared/enhanced-image-card";

interface ImagesCardProps {
  data: string[];
  isEditing: boolean;
  onUpdate: (data: string[]) => void;
  productSlug?: string;
  onNewFiles?: (files: File[]) => void;
}

export function ImagesCard({
  data,
  isEditing,
  onUpdate,
  productSlug = "default-product",
  onNewFiles,
}: ImagesCardProps) {
  return (
    <EnhancedImageCard
      title="Images"
      icon={<ImageIcon className="h-5 w-5 text-red-500" />}
      images={data}
      isEditing={isEditing}
      onImagesChange={onUpdate}
      productSlug={productSlug}
      tabType="overview"
      maxFileSize={5}
      allowMultiple={true}
      showUploadButton={true}
      showAddUrlButton={true}
      onNewFiles={onNewFiles}
    />
  );
}
