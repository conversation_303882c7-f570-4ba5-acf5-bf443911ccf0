import { NPDDetailContent } from "@/components/dashboard/npd-detail-content";

interface NPDDetailPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: NPDDetailPageProps) {
  const { slug } = await params;

  // Simple metadata for internal app - no need for database lookup
  return {
    title: `NPD ${slug} - NPD`,
    description: "NPD details",
  };
}

// Force dynamic rendering
export const dynamic = "force-dynamic";

const NPDDetailPage = async () => {
  // The actual content is now handled by the client component
  // which will use the NPDContext for data management
  return <NPDDetailContent />;
};

export default NPDDetailPage;
