"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";
import {
  getPollingInterval,
  isPollingEnabled,
  POLLING_DEBUG,
} from "@/lib/constants/polling";

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: {
    roles?: string[];
    [key: string]: unknown;
  };
  read: boolean;
  createdAt: string;
}

// Global flag to prevent multiple polling instances
let globalPollingActive = false;

export function useRoleUpdates() {
  const { refreshUserData, user } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const lastNotificationCheckRef = useRef<number>(0);

  // Get polling configuration
  const pollingInterval = getPollingInterval("ROLE_UPDATES");
  const pollingEnabled = isPollingEnabled("ROLE_UPDATES");
  const debugLogging = POLLING_DEBUG.VERBOSE_LOGGING;

  const fetchNotifications = useCallback(
    async (silent = false) => {
      if (!user) return null;

      try {
        const params = new URLSearchParams({
          unreadOnly: "true",
          limit: "5",
          silent: silent.toString(),
        });

        const response = await fetch(`/api/notifications?${params}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.notifications || [];
      } catch (error) {
        if (!silent) {
          console.error(`❌ Error fetching notifications:`, error);
        }
        return null;
      }
    },
    [user]
  );

  const processNotifications = useCallback(
    async (notifications: Notification[]) => {
      if (!notifications || notifications.length === 0) return;

      let hasRoleUpdates = false;

      for (const notification of notifications) {
        if (notification.type === "role_update") {
          hasRoleUpdates = true;

          toast.success(notification.title, {
            description: notification.message,
            duration: 5000,
          });
        }
      }

      if (hasRoleUpdates) {
        await refreshUserData();
      }

      // Only mark role_update notifications as read
      try {
        const roleUpdateIds = notifications
          .filter((n) => n.type === "role_update")
          .map((n) => n.id);
        if (roleUpdateIds.length > 0) {
          await fetch("/api/notifications", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ notificationIds: roleUpdateIds }),
          });
        }
      } catch (error) {
        console.error(
          `❌ Error marking role update notifications as read:`,
          error
        );
      }
    },
    [refreshUserData]
  );

  const checkForUpdates = useCallback(
    async (silent = false) => {
      const now = Date.now();

      // Prevent too frequent checks (minimum 5 seconds between requests)
      if (now - lastNotificationCheckRef.current < 5000) {
        return;
      }

      lastNotificationCheckRef.current = now;

      const notifications = await fetchNotifications(silent);
      if (notifications && notifications.length > 0) {
        await processNotifications(notifications);
      }
    },
    [fetchNotifications, processNotifications]
  );

  const startPolling = useCallback(() => {
    // Check if polling is enabled
    if (!pollingEnabled) {
      return;
    }

    if (globalPollingActive) {
      return;
    }

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    globalPollingActive = true;
    setIsPolling(true);

    intervalRef.current = setInterval(async () => {
      await checkForUpdates(true); // Silent polling
    }, pollingInterval);
  }, [checkForUpdates, pollingEnabled, pollingInterval]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    globalPollingActive = false;
    setIsPolling(false);
  }, []);

  // Start polling when user is authenticated
  useEffect(() => {
    if (user) {
      startPolling();
    } else {
      stopPolling();
    }

    // Cleanup on unmount
    return () => {
      stopPolling();
    };
  }, [user, startPolling, stopPolling, debugLogging]);

  return {
    isPolling,
    checkNow: checkForUpdates,
  };
}
