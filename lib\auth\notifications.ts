import { prisma } from "@/lib/db/prisma";
import { Prisma } from "@/lib/generated/prisma";
import { createRoleUpdateNotification } from "@/lib/utils/notifications";

// Create a notification for a user
export async function createNotification(
  userId: string,
  type: string,
  title: string,
  message: string,
  data?: Prisma.InputJsonValue
) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        message,
        data: data || undefined,
        read: false,
      },
    });

    return notification;
  } catch (error) {
    console.error(`Error creating notification for user ${userId}:`, error);
    throw error;
  }
}

// Get unread notifications for a user
export async function getUnreadNotifications(userId: string) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        userId,
        read: false,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return notifications;
  } catch (error) {
    console.error(`Error fetching notifications for user ${userId}:`, error);
    throw error;
  }
}

// Mark notifications as read
export async function markNotificationsAsRead(
  userId: string,
  notificationIds: string[]
) {
  try {
    await prisma.notification.updateMany({
      where: {
        id: { in: notificationIds },
        userId, // Ensure user can only update their own notifications
      },
      data: {
        read: true,
      },
    });
  } catch (error) {
    console.error(
      `Error marking notifications as read for user ${userId}:`,
      error
    );
    throw error;
  }
}

// Notify user about role changes
export async function notifyUserRoleChange(
  userId: string,
  newRoles: string[],
  oldRoles?: string[]
) {
  try {
    // Use the improved notification system
    await createRoleUpdateNotification(userId, newRoles, oldRoles || []);
  } catch (error) {
    console.error(
      `Error creating role update notification for user ${userId}:`,
      error
    );
    throw error;
  }
}
