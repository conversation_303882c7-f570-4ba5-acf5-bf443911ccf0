import React from "react";
import { cn } from "@/lib/utils";

interface ChangeIndicatorProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Small amber indicator showing that a field has been changed
 */
export function FieldChangedIndicator({
  children,
  className,
}: ChangeIndicatorProps) {
  return (
    <span
      className={cn(
        "text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1",
        className
      )}
    >
      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
      {children}
    </span>
  );
}

/**
 * Larger amber badge showing that there are unsaved changes
 */
export function UnsavedChangesIndicator({
  children,
  className,
}: ChangeIndicatorProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-amber-700 bg-amber-100 rounded-md dark:bg-amber-900/20 dark:text-amber-400",
        className
      )}
    >
      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
      {children}
    </span>
  );
}

interface LabelWithChangeIndicatorProps {
  children: React.ReactNode;
  isChanged: boolean;
  isEditing?: boolean;
  className?: string;
}

/**
 * Label component that shows a visual indicator when the field has been modified
 */
export function LabelWithChangeIndicator({
  children,
  isChanged,
  isEditing = true,
  className,
}: LabelWithChangeIndicatorProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      {children}
      {isEditing && isChanged && (
        <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
      )}
    </div>
  );
}

interface HeaderWithUnsavedChangesProps {
  title: React.ReactNode;
  hasUnsavedChanges: boolean;
  className?: string;
}

/**
 * Header component that shows an "Unsaved changes" indicator when there are modifications
 */
export function HeaderWithUnsavedChanges({
  title,
  hasUnsavedChanges,
  className,
}: HeaderWithUnsavedChangesProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      {title}
      {hasUnsavedChanges && (
        <UnsavedChangesIndicator>Unsaved changes</UnsavedChangesIndicator>
      )}
    </div>
  );
}
