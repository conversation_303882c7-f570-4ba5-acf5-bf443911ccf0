"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TabHeader, LoadingButton } from "../shared";
import { BarChart3 } from "lucide-react";
import { getTabOrder } from "@/lib/constants/tab-order";
import { toast } from "sonner";

interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

interface AddSection12TabProps {
  productName: string;
  onBack: () => void;
  onSave: (tabData: TabData) => Promise<void>;
}

export function AddSection12Tab({
  productName,
  onBack,
  onSave,
}: AddSection12TabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const formData = {
    pricingScenarios: [
      {
        date: new Date().toLocaleDateString(),
        priceScenario: "Breakeven",
        priceUsd: "",
        estRefundRate: "",
        referalFeePercent: "",
        fbaFeeUsd: "",
        landedCostPercent: "",
        tacosPercent: "",
        commentary: "",
        isVisible: true,
      },
      {
        date: new Date().toLocaleDateString(),
        priceScenario: "Lowest",
        priceUsd: "",
        estRefundRate: "",
        referalFeePercent: "",
        fbaFeeUsd: "",
        landedCostPercent: "",
        tacosPercent: "",
        commentary: "",
        isVisible: true,
      },
      {
        date: new Date().toLocaleDateString(),
        priceScenario: "Highest",
        priceUsd: "",
        estRefundRate: "",
        referalFeePercent: "",
        fbaFeeUsd: "",
        landedCostPercent: "",
        tacosPercent: "",
        commentary: "",
        isVisible: true,
      },
      {
        date: new Date().toLocaleDateString(),
        priceScenario: "Suggested",
        priceUsd: "",
        estRefundRate: "",
        referalFeePercent: "",
        fbaFeeUsd: "",
        landedCostPercent: "",
        tacosPercent: "",
        commentary: "",
        isVisible: true,
      },
    ],
    user: null,
    editedAt: new Date().toISOString(),
    editedBy: null,
    isEdited: false,
    versions: [],
  };

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsLoading(true);

    try {
      await onSave({
        tabName: "1.2",
        order: getTabOrder("1.2"),
        fields: formData,
      });
      // Success toast will be shown after page reload via URL parameters
    } catch (error) {
      console.error("Error creating section 1.2 tab:", error);
      toast.error("Failed to create section 1.2 tab. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <TabHeader
        title="Add 1.2 Tab"
        description="Create a methodology and implementation tab for {productName}"
        productName={productName}
        onBack={onBack}
        actionButton={
          <LoadingButton
            isLoading={isLoading}
            onClick={handleSubmit}
            loadingText="Creating..."
            defaultText="Create Section 1.2"
          />
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                Section 1.2 - Pricing Scenarios
              </CardTitle>
              <CardDescription>
                This tab will provide a comprehensive pricing analysis table
                with all the financial metrics you need to evaluate different
                pricing strategies. It starts with four default scenarios that
                you can customize.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium">Pricing Analysis Table</h3>
                    <p className="text-sm text-muted-foreground">
                      Comprehensive table with 9 columns and 4 default
                      scenarios: Breakeven, Lowest, Highest, Suggested
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium">Financial Metrics</h3>
                    <p className="text-sm text-muted-foreground">
                      Track prices, fees, costs, TACOS, and net profit
                      calculations with today&apos;s date
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What you'll get */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your section 1.2 tab will include a comprehensive pricing
                scenarios table with all these columns and 4 pre-filled
                scenarios ready for your data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Date</Badge>
                    <span className="text-sm text-muted-foreground">
                      Today&apos;s date (automatic)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Price scenario</Badge>
                    <span className="text-sm text-muted-foreground">
                      4 default options + custom input
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Price $</Badge>
                    <span className="text-sm text-muted-foreground">
                      Product price in USD
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Est. refund rate</Badge>
                    <span className="text-sm text-muted-foreground">
                      Expected refund percentage
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Referal fee %</Badge>
                    <span className="text-sm text-muted-foreground">
                      Amazon referral fee
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">FBA fee $</Badge>
                    <span className="text-sm text-muted-foreground">
                      Fulfillment fee amount
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">FBA fee %</Badge>
                    <span className="text-sm text-muted-foreground">
                      Fee as percentage (calculated)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Landed cost %</Badge>
                    <span className="text-sm text-muted-foreground">
                      Cost percentage
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Landed cost $</Badge>
                    <span className="text-sm text-muted-foreground">
                      Total cost in USD (calculated)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">TACOS %</Badge>
                    <span className="text-sm text-muted-foreground">
                      Total advertising cost
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Net profit %</Badge>
                    <span className="text-sm text-muted-foreground">
                      Profit margin percentage (calculated)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Net profit $</Badge>
                    <span className="text-sm text-muted-foreground">
                      Profit amount in USD (calculated)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Commentary</Badge>
                    <span className="text-sm text-muted-foreground">
                      Notes and analysis
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
