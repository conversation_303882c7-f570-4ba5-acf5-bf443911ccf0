"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertTriangle, Send } from "lucide-react";
import Spinner from "@/components/spinner";

interface DeletionRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  creatorUsername: string;
  entryType: string;
  entryTitle: string;
  onSendRequest: () => Promise<void>;
  onCancel?: () => void;
}

export function DeletionRequestDialog({
  open,
  onOpenChange,
  creatorUsername,
  entryType,
  entryTitle,
  onSendRequest,
  onCancel,
}: DeletionRequestDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleSendRequest = async () => {
    setIsLoading(true);
    try {
      await onSendRequest();
      onOpenChange(false);
    } catch (error) {
      console.error("Error sending deletion request:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader className="space-y-3">
          <AlertDialogTitle className="flex items-center gap-3 text-xl">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <span>Cannot Delete Entry</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base leading-relaxed text-muted-foreground">
            You cannot delete this {entryType} because it was created by{" "}
            <span className="font-medium text-blue-600">
              @{creatorUsername}
            </span>
            .
            <br />
            <br />
            Would you like to send them a notification requesting deletion of{" "}
            <span className="font-medium">&ldquo;{entryTitle}&rdquo;</span>?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-3 sm:gap-3">
          <AlertDialogCancel
            onClick={handleCancel}
            className="min-w-[80px] hover:bg-muted"
            disabled={isLoading}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleSendRequest}
            disabled={isLoading}
            className="min-w-[120px] bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Spinner loading={true} size={8} color="#ffffff" />
                Sending...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Send Request
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
