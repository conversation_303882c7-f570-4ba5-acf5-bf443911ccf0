import type { Table } from "@tanstack/react-table";
import { TableFooter } from "./TableFooter";
import { forwardRef } from "react";
import {
  TABLE_STYLES,
  TabsTableContainer,
  TabsTableHeader,
  TabsTableBody,
} from "../../shared";

interface Competitor {
  productDetails: string;
  asin: string;
  url: string;
  imageUrl: string;
  brand: string;
  price: string;
  asinSales: string;
  asinRevenue: string;
  bsr: string;
  sellerCountry: string;
  fees: string;
  ratings: string;
  reviewCount: string;
  fulfillment: string;
  dimensions: string;
  weight: string;
  creationDate: string;
  isVisible?: boolean; // New field for visibility toggle
}

type TableRow = Competitor & { index: number; [key: string]: unknown };

interface CompetitorTableProps {
  table: Table<TableRow>;
  competitors: Competitor[];
  onCopy?: (text: string) => void;
}

export const CompetitorTable = forwardRef<HTMLDivElement, CompetitorTableProps>(
  ({ table, competitors, onCopy }, ref) => {
    return (
      <TabsTableContainer
        maxHeight="600px"
        height={competitors.length === 0 ? "auto" : "fit-content"}
      >
        <div ref={ref}>
          <table
            {...{
              style: {
                width: table.getCenterTotalSize(),
                minWidth: "100%",
              },
            }}
            className={TABLE_STYLES.table}
          >
            <TabsTableHeader table={table} />

            <TabsTableBody
              table={table}
              getRowClassName={(rowIndex, rowData) => {
                const isVisible = rowData.isVisible !== false;
                return !isVisible
                  ? TABLE_STYLES.bodyRow.hidden
                  : rowIndex % 2 === 0
                  ? TABLE_STYLES.bodyRow.even
                  : TABLE_STYLES.bodyRow.odd;
              }}
            />

            {/* Table footer for calculated totals */}
            <TableFooter
              table={table}
              competitors={competitors}
              onCopy={onCopy}
            />
          </table>
        </div>
      </TabsTableContainer>
    );
  }
);

CompetitorTable.displayName = "CompetitorTable";
