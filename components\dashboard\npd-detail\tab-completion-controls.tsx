"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Settings2 } from "lucide-react";
import { LoadingButton } from "../npd-tabs/shared/loading-button";
import {
  getTabCompletions,
  setTabCompletion,
  removeTabCompletion,
} from "@/actions/tab-completion.actions";
import { toast } from "sonner";

interface Tab {
  id: string;
  tabName: string;
  order: number;
}

interface TabCompletionControlsProps {
  productId: string;
  tabs: Tab[];
  onUpdate?: () => void; // Callback to trigger refresh
}

/**
 * Component for manually adjusting tab completion percentages
 * Stores values in database for persistence across sessions
 */
export function TabCompletionControls({
  productId,
  tabs,
  onUpdate,
}: TabCompletionControlsProps) {
  const [originalCompletions, setOriginalCompletions] = useState<
    Record<string, number>
  >({});
  const [currentCompletions, setCurrentCompletions] = useState<
    Record<string, number>
  >({});
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load completions when dialog opens
  const loadCompletions = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getTabCompletions(productId);
      const completionMap: Record<string, number> = {};
      data.forEach(
        (completion: { npdProductTabId: string; completion: number }) => {
          completionMap[completion.npdProductTabId] = completion.completion;
        }
      );
      setOriginalCompletions(completionMap);
      setCurrentCompletions({ ...completionMap });
    } catch (error) {
      console.error("Failed to load completions:", error);
      toast.error("Failed to load completion data");
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    if (isOpen) {
      loadCompletions();
    }
  }, [isOpen, loadCompletions]);

  // Handle input changes
  const handleCompletionChange = (tabId: string, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 100) {
      setCurrentCompletions((prev) => ({ ...prev, [tabId]: numValue }));
    } else if (value === "") {
      setCurrentCompletions((prev) => ({ ...prev, [tabId]: 0 }));
    }
  };

  // Check if there are unsaved changes
  const hasChanges = () => {
    return Object.keys(currentCompletions).some(
      (tabId) => currentCompletions[tabId] !== (originalCompletions[tabId] || 0)
    );
  };

  // Save all changes
  const handleSave = async () => {
    try {
      setSaving(true);

      // Get tabs that have changed
      const changedTabs = Object.keys(currentCompletions).filter(
        (tabId) =>
          currentCompletions[tabId] !== (originalCompletions[tabId] || 0)
      );

      // Update each changed tab
      for (const tabId of changedTabs) {
        const newCompletion = currentCompletions[tabId];
        if (newCompletion === 0) {
          await removeTabCompletion(productId, tabId);
        } else {
          await setTabCompletion(productId, tabId, newCompletion);
        }
      }

      // Update original completions to match current
      setOriginalCompletions({ ...currentCompletions });

      toast.success("Tab completions updated successfully");
      onUpdate?.(); // Trigger refresh in parent
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to save completions:", error);
      toast.error("Failed to save completions");
    } finally {
      setSaving(false);
    }
  };

  // Cancel changes
  const handleCancel = () => {
    setCurrentCompletions({ ...originalCompletions });
    setIsOpen(false);
  };

  // Clear all completions
  const handleClearAll = () => {
    const clearedCompletions: Record<string, number> = {};
    tabs.forEach((tab) => {
      clearedCompletions[tab.id] = 0;
    });
    setCurrentCompletions(clearedCompletions);
  };

  const sortedTabs = tabs.sort((a, b) => a.order - b.order);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
          <Settings2 className="h-3 w-3" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Tab Completion Settings</DialogTitle>
          <DialogDescription>
            Set completion percentages for each tab. Values are stored in the
            database.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-4">Loading...</div>
        ) : (
          <div className="space-y-6">
            {/* Tab completion inputs */}
            <div className="space-y-4">
              {sortedTabs.map((tab) => (
                <div key={tab.id} className="space-y-2">
                  <Label className="text-sm font-medium">{tab.tabName}</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={currentCompletions[tab.id] || 0}
                      onChange={(e) =>
                        handleCompletionChange(tab.id, e.target.value)
                      }
                      className="flex-1"
                      disabled={saving}
                    />
                    <span className="text-xs text-muted-foreground min-w-[2rem]">
                      %
                    </span>
                  </div>
                  {originalCompletions[tab.id] !==
                    currentCompletions[tab.id] && (
                    <p className="text-xs text-muted-foreground">
                      Changed from {originalCompletions[tab.id] || 0}% to{" "}
                      {currentCompletions[tab.id] || 0}%
                    </p>
                  )}
                </div>
              ))}
            </div>

            {/* Clear all button */}
            <div className="pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                className="w-full"
                disabled={saving}
              >
                Clear All Completions
              </Button>
            </div>

            {/* Save/Cancel buttons */}
            <div className="flex justify-end gap-2 pt-2 border-t">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <LoadingButton
                isLoading={saving}
                onClick={handleSave}
                disabled={!hasChanges()}
                loadingText="Saving..."
                defaultText="Save Changes"
              />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
