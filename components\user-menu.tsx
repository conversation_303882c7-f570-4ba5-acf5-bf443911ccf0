"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { LogOut, ChevronDown, Shield } from "lucide-react";
import { AuthPayload } from "@/lib/auth";
import { useState } from "react";

interface UserMenuProps {
  user: AuthPayload;
  onSignOut: () => Promise<void>;
  variant?: "mobile" | "desktop";
}

export function UserMenu({
  user,
  onSignOut,
  variant = "desktop",
}: UserMenuProps) {
  const [isSignOutDialogOpen, setIsSignOutDialogOpen] = useState(false);

  const handleSignOut = async () => {
    await onSignOut();
    setIsSignOutDialogOpen(false);
  };

  // Get user initials for fallback
  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Format role name for display
  const formatRoleName = (roleName: string) => {
    return roleName
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Get badge variant for role
  const getRoleBadgeVariant = (roleName: string) => {
    switch (roleName) {
      case "SUPER_ADMIN":
        return "destructive";
      case "ADMIN":
        return "default";
      default:
        return "secondary";
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`${
              variant === "mobile" ? "w-full justify-start" : "h-10 px-3"
            } gap-2 hover:bg-accent hover:text-accent-foreground`}
          >
            <Avatar className="size-7">
              <AvatarImage
                src={
                  user.image
                    ? user.image
                    : `https://api.dicebear.com/5.x/initials/svg?seed=${user.name}`
                }
                alt={`${user.name} Avatar`}
              />
              <AvatarFallback className="text-xs">
                {getUserInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <span
              className={
                variant === "mobile"
                  ? "flex-1 text-left"
                  : "hidden sm:inline max-w-24 truncate"
              }
            ></span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel className="font-normal">
            <div className="flex items-center space-x-2">
              <Avatar className="size-8">
                <AvatarImage
                  src={
                    user.image
                      ? user.image
                      : `https://api.dicebear.com/5.x/initials/svg?seed=${user.name}`
                  }
                  alt={`${user.name} Avatar`}
                />
                <AvatarFallback className="text-xs">
                  {getUserInitials(user.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{user.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </div>
          </DropdownMenuLabel>

          {/* Show user roles */}
          {user.roles && user.roles.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="px-2 py-1.5">
                <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                  <Shield className="h-3 w-3" />
                  <span>Roles</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {user.roles.map((role, index) => (
                    <Badge
                      key={index}
                      variant={getRoleBadgeVariant(role)}
                      className="text-xs"
                    >
                      {formatRoleName(role)}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}

          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-red-600 focus:text-red-600"
            onClick={() => setIsSignOutDialogOpen(true)}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog
        open={isSignOutDialogOpen}
        onOpenChange={setIsSignOutDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to sign out?
            </AlertDialogTitle>
            <AlertDialogDescription>
              You will be redirected to the home page and will need to sign in
              again to access your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSignOut}>
              Sign Out
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
