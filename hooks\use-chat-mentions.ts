"use client";

import { useState, useCallback, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { fetchAllUsers } from "@/actions/user.actions";

interface User {
  id: string;
  name: string;
  email?: string;
}

interface ChatMessage {
  id: string;
  content: string;
  mentions: string[];
  userId: string;
  userName: string;
  timestamp: Date;
  productId?: string;
}

interface Notification {
  id: string;
  type: "mention";
  message: string;
  fromUser: string;
  timestamp: Date;
  read: boolean;
  productId?: string;
}

export function useChatMentions() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [users, setUsers] = useState<User[]>([]);

  // Fetch real users from the system
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const fetchedUsers = await fetchAllUsers();
        setUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching users:", error);
        // Fallback to empty array if fetch fails
        setUsers([]);
      }
    };

    loadUsers();
  }, []);

  // Send chat message with mentions
  const sendMessage = useCallback(
    async (content: string, mentions: string[], productId?: string) => {
      if (!user) return;

      const message: ChatMessage = {
        id: Date.now().toString(),
        content,
        mentions,
        userId: user.userId,
        userName: user.name,
        timestamp: new Date(),
        productId,
      };

      // Create notifications for mentioned users
      const newNotifications: Notification[] = mentions.map(
        (mentionedUser) => ({
          id: `${Date.now()}-${mentionedUser}`,
          type: "mention" as const,
          message: `${user.name} mentioned you: "${content.slice(0, 50)}${
            content.length > 50 ? "..." : ""
          }"`,
          fromUser: user.name,
          timestamp: new Date(),
          read: false,
          productId,
        })
      );

      // Add notifications (in real app, this would be sent to server)
      setNotifications((prev) => [...newNotifications, ...prev]);
      setUnreadCount((prev) => prev + newNotifications.length);

      // In real app, send to server/database
      return message;
    },
    [user]
  );

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
    setUnreadCount((prev) => Math.max(0, prev - 1));
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));
    setUnreadCount(0);
  }, []);

  // Get unread notifications
  const unreadNotifications = notifications.filter((notif) => !notif.read);

  // Open chat with pre-filled mention
  const openChatWithMention = useCallback((username: string) => {
    // This would typically open a chat panel/modal
    // For now, we'll just trigger an event that the chat component can listen to
    const event = new CustomEvent("open-chat-with-mention", {
      detail: { username },
    });
    window.dispatchEvent(event);
  }, []);

  return {
    users,
    notifications,
    unreadNotifications,
    unreadCount,
    sendMessage,
    markAsRead,
    markAllAsRead,
    openChatWithMention,
  };
}
