import { ReactNode } from "react";
import { PageHeader } from "@/components/dashboard/shared";

interface TabHeaderProps {
  title: string;
  description: string;
  productName: string;
  onBack: () => void;
  actionButton: ReactNode;
}

export function TabHeader({
  title,
  description,
  productName,
  onBack,
  actionButton,
}: TabHeaderProps) {
  return (
    <PageHeader
      title={title}
      description={description.replace("{productName}", productName)}
      onBack={onBack}
      actionButton={actionButton}
    />
  );
}
