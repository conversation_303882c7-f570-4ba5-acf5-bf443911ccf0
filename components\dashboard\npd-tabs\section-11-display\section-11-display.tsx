"use client";

import type { SortingState } from "@tanstack/react-table";
import {
  useMemo,
  useState,
  useCallback,
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react";
import { updateTabData } from "@/actions/npd.actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Upload, BarChart3 } from "lucide-react";
import Papa from "papaparse";
import { toast } from "sonner";
import {
  useReactTable,
  getCoreRowModel,
  ColumnResizeMode,
  getSortedRowModel,
} from "@tanstack/react-table";
import { ProductInfoSection } from "../shared";
import { TabDangerZone } from "../shared/tab-danger-zone";
import {
  CompetitorTable,
  ColumnToggle,
  createCompetitorColumns,
  loadColumnSettings,
  resetColumnVisibility,
  resetColumnWidths,
  updateColumnVisibility,
  updateColumnWidths,
  updateSortingState,
  type TableRow,
  type ColumnSettings,
} from "./index";
import { useCardLevelChanges } from "../shared";
import { GoogleTrendsWidget } from "../shared/google-trends-widget";

interface Competitor {
  productDetails: string;
  asin: string;
  url: string;
  imageUrl: string;
  brand: string;
  price: string;
  asinSales: string;
  asinRevenue: string;
  bsr: string;
  sellerCountry: string;
  fees: string;
  ratings: string;
  reviewCount: string;
  fulfillment: string;
  dimensions: string;
  weight: string;
  creationDate: string;
  isVisible?: boolean; // New field for visibility toggle
}

interface ProductInfoVersion {
  id: string;
  features: string;
  color: string;
  size: string;
  pricerange: string;
  user: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface Section11Data {
  features: string;
  color: string;
  size: string;
  pricerange: string;
  launchIntoExistingListing: string;
  dataDiveDashboardName: string;
  dataDiveDashboardLink: string;
  totalSellersInNiche: string;
  googleTrendsKeyword: string;
  googleTrendsData?: {
    keyword: string;
    data: Array<{ date: string; value: number; formattedDate: string }>;
    fiveYearData?: Array<{
      date: string;
      value: number;
      formattedDate: string;
    }>;
    relatedQueries: string[];
    averageInterest: number;
    trend: "rising" | "falling" | "stable";
    peakInterest: number;
    region: string;
    timeframe: string;
    isRealData?: boolean;
    dataSource?: string;
  }; // Store the fetched Google Trends data
  competitors: Competitor[];
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: ProductInfoVersion[];
}

interface Section11DisplayProps {
  tabId: string;
  initialData: Section11Data;
  isEditing?: boolean;
  onDirtyChange?: (isDirty: boolean) => void;
  // Props for TabDangerZone
  tabName?: string;
  productId?: string;
  productName?: string;
  productSlug?: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
}

export interface Section11DisplayRef {
  save: () => Promise<void>;
}

export const Section11Display = forwardRef<
  Section11DisplayRef,
  Section11DisplayProps
>(
  (
    {
      tabId,
      initialData,
      isEditing = false,
      onDirtyChange,
      tabName,
      productId,
      productName,
      productSlug,
      tabCreatorId,
      productOwnerId,
    },
    ref
  ) => {
    // State for the current data
    const [data, setData] = useState<Section11Data>(initialData);
    const [originalData, setOriginalData] =
      useState<Section11Data>(initialData);
    // State for showing copy notifications
    const [copiedCell, setCopiedCell] = useState<string | null>(null);

    // Ref for scrolling to new rows
    const tableContainerRef = useRef<HTMLDivElement>(null);

    // Local state for competitor data when editing
    const [competitorData, setCompetitorData] = useState<Competitor[]>(
      data.competitors
    );

    // Track if any data has been modified
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // Track which specific fields have been modified for visual indicators
    const [modifiedProductFields, setModifiedProductFields] = useState<
      Set<string>
    >(new Set());
    const [modifiedCompetitorFields, setModifiedCompetitorFields] = useState<
      Map<number, Set<string>>
    >(new Map());

    // Ref to access current data without causing re-renders
    const dataRef = useRef(data);
    dataRef.current = data;

    // Update data when initialData changes
    useEffect(() => {
      const dataWithDefaults = {
        ...initialData,
        launchIntoExistingListing: initialData.launchIntoExistingListing || "",
        dataDiveDashboardName: initialData.dataDiveDashboardName || "",
        dataDiveDashboardLink: initialData.dataDiveDashboardLink || "",
        totalSellersInNiche: initialData.totalSellersInNiche || "",
        googleTrendsKeyword: initialData.googleTrendsKeyword || "",
        googleTrendsData: initialData.googleTrendsData || undefined,
      };
      setData(dataWithDefaults);
      setOriginalData(dataWithDefaults);
      setCompetitorData(initialData.competitors);
      setHasUnsavedChanges(false);
    }, [initialData]);

    // Check for changes
    const checkForChanges = useCallback(() => {
      const currentFullData = {
        ...data,
        competitors: competitorData,
      };
      const hasChanges =
        JSON.stringify(currentFullData) !== JSON.stringify(originalData);
      setHasUnsavedChanges(hasChanges);
      return hasChanges;
    }, [data, competitorData, originalData]);

    // Update data and check for changes
    const updateData = useCallback(
      (updates: Partial<Section11Data>) => {
        const newData = { ...data, ...updates };
        setData(newData);
        dataRef.current = newData;
        // Check for changes immediately after updating data
        setTimeout(() => {
          const currentFullData = {
            ...newData,
            competitors: competitorData,
          };
          const hasChanges =
            JSON.stringify(currentFullData) !== JSON.stringify(originalData);
          setHasUnsavedChanges(hasChanges);
        }, 0);
      },
      [data, competitorData, originalData]
    );

    // Check for changes whenever competitor data changes
    useEffect(() => {
      checkForChanges();
    }, [competitorData, checkForChanges]);

    // Notify parent of changes
    useEffect(() => {
      onDirtyChange?.(hasUnsavedChanges);
    }, [hasUnsavedChanges, onDirtyChange]);

    // Column settings state
    const [columnSettings, setColumnSettings] = useState<ColumnSettings>(() =>
      loadColumnSettings()
    );

    // Helper function to handle copying with notification
    const handleCopy = useCallback(async (text: string, cellId: string) => {
      if (text) {
        try {
          await navigator.clipboard.writeText(text);
          setCopiedCell(cellId);
          setTimeout(() => setCopiedCell(null), 2000); // Hide after 2 seconds
        } catch (err) {
          console.error("Failed to copy:", err);
        }
      }
    }, []);

    // Handle competitor cell value changes
    const handleCompetitorChange = useCallback(
      (rowIndex: number, field: keyof Competitor, value: string) => {
        setCompetitorData((prev) => {
          const newData = [...prev];
          // Use current data from ref to avoid stale closure
          const originalValue =
            dataRef.current.competitors[rowIndex]?.[field] || "";
          newData[rowIndex] = { ...newData[rowIndex], [field]: value };

          // Track which fields have been modified
          setModifiedCompetitorFields((prevModified) => {
            const newModified = new Map(prevModified);
            if (!newModified.has(rowIndex)) {
              newModified.set(rowIndex, new Set());
            }
            const rowModified = newModified.get(rowIndex)!;

            if (value !== originalValue) {
              rowModified.add(field);
            } else {
              rowModified.delete(field);
              if (rowModified.size === 0) {
                newModified.delete(rowIndex);
              }
            }

            return newModified;
          });

          return newData;
        });
      },
      [] // No dependencies - use ref for current data
    );

    // Handle deleting a competitor row
    const handleDeleteCompetitor = useCallback((rowIndex: number) => {
      setCompetitorData((prev) => {
        const newData = [...prev];
        newData.splice(rowIndex, 1);
        return newData;
      });
    }, []);

    // Handle toggling competitor visibility
    const handleToggleVisibility = useCallback((rowIndex: number) => {
      setCompetitorData((prev) => {
        const newData = [...prev];
        const currentVisibility = newData[rowIndex].isVisible !== false; // Default to visible
        newData[rowIndex] = {
          ...newData[rowIndex],
          isVisible: !currentVisibility,
        };
        return newData;
      });
      // Note: Visibility toggle does not mark data as dirty since it's just a UI state
      // and not a change that needs to be saved to the server
    }, []);

    // Handle adding a new competitor row
    const handleAddCompetitor = useCallback(() => {
      const newCompetitor: Competitor = {
        productDetails: "",
        asin: "",
        url: "",
        imageUrl: "",
        brand: "",
        price: "",
        asinSales: "",
        asinRevenue: "",
        bsr: "",
        sellerCountry: "",
        fees: "",
        ratings: "",
        reviewCount: "",
        fulfillment: "",
        dimensions: "",
        weight: "",
        creationDate: new Date().toISOString().split("T")[0], // Default to today's date
        isVisible: true, // Default to visible
      };

      setCompetitorData((prev) => [...prev, newCompetitor]);

      // Scroll to the new row after it's been added and optionally focus the first field
      setTimeout(() => {
        if (tableContainerRef.current) {
          // Scroll to the bottom of the table to show the new row
          tableContainerRef.current.scrollTo({
            top: tableContainerRef.current.scrollHeight,
            behavior: "smooth",
          });

          // After scrolling, try to focus the Product Details field of the new row
          setTimeout(() => {
            const allTextareas =
              tableContainerRef.current?.querySelectorAll("textarea");
            if (allTextareas && allTextareas.length > 0) {
              // Focus the last textarea (which should be the Product Details of the new row)
              const lastTextarea = allTextareas[
                allTextareas.length - 1
              ] as HTMLTextAreaElement;
              lastTextarea.focus();
            }
          }, 300); // Wait for smooth scroll to complete
        }
      }, 100); // Small delay to ensure the row is rendered
    }, []);

    // Handle CSV file import
    const handleFileImport = useCallback((file: File) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            const importedData = results.data as Record<string, string>[];

            // Filter out empty rows and header rows
            const validData = importedData.filter((row) => {
              // Check if row has meaningful data (not just headers or empty values)
              const productDetails =
                row["Product Details"] || row["productDetails"] || "";
              const asin = row["ASIN"] || row["asin"] || "";

              // Skip if both key fields are empty or if it looks like a header row
              if (!productDetails.trim() && !asin.trim()) return false;
              if (productDetails.toLowerCase().includes("product details"))
                return false;
              if (asin.toLowerCase().includes("asin")) return false;

              return true;
            });

            // Limit to 10 records
            const limitedData = validData.slice(0, 10);

            if (validData.length > 10) {
              toast.warning(
                `CSV contains ${validData.length} valid records. Only the first 10 will be imported.`
              );
            }

            // Helper function to round revenue values
            const formatRevenueValue = (value: string): string => {
              if (!value) return "";
              // Remove any currency symbols and commas
              const cleanValue = value.replace(/[$,]/g, "");
              const numericValue = parseFloat(cleanValue);
              // If it's a valid number, round it and return as string
              if (!isNaN(numericValue)) {
                return Math.round(numericValue).toString();
              }
              return value; // Return original if not a valid number
            };

            // Map CSV data to competitor format
            const newCompetitors: Competitor[] = limitedData.map((row) => ({
              productDetails:
                row["Product Details"] || row["productDetails"] || "",
              asin: row["ASIN"] || row["asin"] || "",
              url: row["URL"] || row["url"] || "",
              imageUrl: row["Image URL"] || row["imageUrl"] || "",
              brand: row["Brand"] || row["brand"] || "",
              price:
                row["Price  US$"] || // Your CSV format (with extra spaces)
                row["Price US$"] || // Alternative format
                row["Price"] ||
                row["price"] ||
                "",
              asinSales: row["ASIN Sales"] || row["asinSales"] || "",
              asinRevenue: formatRevenueValue(
                row["ASIN Revenue"] || row["asinRevenue"] || ""
              ),
              bsr: row["BSR"] || row["bsr"] || "",
              sellerCountry:
                row["Seller Country/Region"] || // Your CSV format
                row["Seller Country"] ||
                row["sellerCountry"] ||
                "",
              fees:
                row["Fees  US$"] || // Your CSV format (with extra spaces)
                row["Fees US$"] || // Alternative format
                row["Fees"] ||
                row["fees"] ||
                "",
              ratings: row["Ratings"] || row["ratings"] || "",
              reviewCount: row["Review Count"] || row["reviewCount"] || "",
              fulfillment: row["Fulfillment"] || row["fulfillment"] || "",
              dimensions: row["Dimensions"] || row["dimensions"] || "",
              weight: row["Weight"] || row["weight"] || "",
              creationDate:
                row["Creation Date"] ||
                row["creationDate"] ||
                new Date().toISOString().split("T")[0],
              isVisible: true,
            }));

            // Add imported competitors to existing data
            setCompetitorData((prev) => [...prev, ...newCompetitors]);

            // Show detailed success message
            const skippedRows = importedData.length - validData.length;
            const successMessage =
              skippedRows > 0
                ? `Successfully imported ${newCompetitors.length} competitors from CSV (${skippedRows} empty/header rows skipped)`
                : `Successfully imported ${newCompetitors.length} competitors from CSV`;

            toast.success(successMessage);
          } catch (error) {
            console.error("Error parsing CSV:", error);
            toast.error(
              "Failed to import CSV file. Please check the format and try again."
            );
          }
        },
        error: (error) => {
          console.error("CSV parsing error:", error);
          toast.error(
            "Failed to parse CSV file. Please check the format and try again."
          );
        },
      });
    }, []);

    // Handle column visibility changes
    const handleVisibilityChange = useCallback(
      (visibility: Record<string, boolean>) => {
        updateColumnVisibility(visibility);
        setColumnSettings((prev) => ({ ...prev, visibility }));
      },
      []
    );

    // Handle column width changes
    const handleColumnResize = useCallback(
      (updaterOrValue: unknown) => {
        const newSizing =
          typeof updaterOrValue === "function"
            ? updaterOrValue(columnSettings.widths)
            : updaterOrValue;
        updateColumnWidths(newSizing as Record<string, number>);
        setColumnSettings((prev) => ({
          ...prev,
          widths: newSizing as Record<string, number>,
        }));
      },
      [columnSettings.widths]
    );

    // Handle reset visibility to defaults
    const handleResetVisibility = useCallback(() => {
      const defaultVisibility = resetColumnVisibility();
      setColumnSettings((prev) => ({
        ...prev,
        visibility: defaultVisibility,
      }));
    }, []);

    // Handle reset widths to defaults
    const handleResetWidths = useCallback(() => {
      const defaultWidths = resetColumnWidths();
      setColumnSettings((prev) => ({
        ...prev,
        widths: defaultWidths,
      }));
    }, []);

    // Reset competitor data when props change but preserve visibility states
    useEffect(() => {
      setCompetitorData((prev) => {
        // Preserve visibility states from current data when props change
        const visibilityStates = prev.reduce((acc, competitor, index) => {
          acc[index] = competitor.isVisible;
          return acc;
        }, {} as Record<number, boolean | undefined>);

        // Reset to new prop data but apply preserved visibility states
        return data.competitors.map((competitor, index) => ({
          ...competitor,
          isVisible:
            visibilityStates[index] !== undefined
              ? visibilityStates[index]
              : true,
        }));
      });
      setModifiedCompetitorFields(new Map());
      setModifiedProductFields(new Set());
    }, [data.competitors]);

    // Track previous editing state to detect when editing is cancelled
    const [prevIsEditing, setPrevIsEditing] = useState(isEditing);

    // Store the view mode column visibility to restore when exiting edit mode
    const [viewModeVisibility, setViewModeVisibility] = useState<Record<
      string,
      boolean
    > | null>(null);

    // Reset function to restore original data but preserve visibility states
    const resetToOriginalData = useCallback(() => {
      // Reset main data to original
      setData(originalData);

      setCompetitorData((prev) => {
        // Preserve visibility states from current data when resetting
        const visibilityStates = prev.reduce((acc, competitor, index) => {
          acc[index] = competitor.isVisible;
          return acc;
        }, {} as Record<number, boolean | undefined>);

        // Reset to original data but apply preserved visibility states
        return originalData.competitors.map((competitor, index) => ({
          ...competitor,
          isVisible:
            visibilityStates[index] !== undefined
              ? visibilityStates[index]
              : true,
        }));
      });
      setModifiedCompetitorFields(new Map());
      setModifiedProductFields(new Set());
    }, [originalData]);

    // Handle edit mode transitions - both for sorting and data reset
    useEffect(() => {
      if (prevIsEditing && !isEditing) {
        // Exiting edit mode - restore last sorting and reset to original data
        setSorting(lastSortingRef.current);
        resetToOriginalData();

        // Restore view mode column visibility if we have it stored
        if (viewModeVisibility) {
          setColumnSettings((prev) => ({
            ...prev,
            visibility: viewModeVisibility,
          }));
          setViewModeVisibility(null);
        }
      } else if (!prevIsEditing && isEditing) {
        // Entering edit mode - store current sorting and column visibility
        // The current sorting is already stored in lastSortingRef by the other effect

        // Clear sorting to prevent auto-sorting during edits
        setSorting([]);

        // Store current column visibility and show all columns in edit mode
        setViewModeVisibility(columnSettings.visibility);
        const allColumnsVisible = Object.keys(columnSettings.visibility).reduce(
          (acc, key) => {
            acc[key] = true;
            return acc;
          },
          {} as Record<string, boolean>
        );
        setColumnSettings((prev) => ({
          ...prev,
          visibility: allColumnsVisible,
        }));
      }
      setPrevIsEditing(isEditing);
    }, [
      isEditing,
      prevIsEditing,
      resetToOriginalData,
      columnSettings.visibility,
      viewModeVisibility,
    ]);

    // Handle product info changes from ProductInfoCard
    const handleProductInfoChange = useCallback(
      (updatedProductInfo: Partial<Section11Data>) => {
        updateData(updatedProductInfo);
        checkForChanges();
      },
      [updateData, checkForChanges]
    );

    // Handle product info field changes to track which fields are modified
    const handleProductInfoFieldChange = useCallback(
      (field: string, value: string, originalData: Section11Data) => {
        const originalValue = originalData[field as keyof Section11Data] || "";
        setModifiedProductFields((prev) => {
          const newModified = new Set(prev);
          if (value !== originalValue) {
            newModified.add(field);
          } else {
            newModified.delete(field);
          }
          return newModified;
        });
        // Update the actual data
        updateData({ [field]: value } as Partial<Section11Data>);
        checkForChanges();
      },
      [updateData, checkForChanges]
    );

    // Define card field groups for Section 1.1
    const cardFieldGroups = {
      productInfo: [
        "features",
        "color",
        "size",
        "pricerange",
        "launchIntoExistingListing",
        "dataDiveDashboardName",
        "dataDiveDashboardLink",
        "totalSellersInNiche",
        "googleTrendsKeyword",
      ],
      competitors: [], // Competitors are handled separately as they're dynamic
    };

    // Use card-level changes hook
    useCardLevelChanges(modifiedProductFields, cardFieldGroups);

    // Check if competitor data has changed (separate from product info)
    const hasCompetitorChanges = () => {
      return (
        JSON.stringify(competitorData) !== JSON.stringify(data.competitors)
      );
    };

    // Save function
    const save = useCallback(async () => {
      if (!hasUnsavedChanges) return;

      try {
        const dataToSave = {
          ...data,
          competitors: competitorData,
        };

        await updateTabData(tabId, dataToSave as Record<string, unknown>);

        // Dispatch event to trigger progress indicator update
        window.dispatchEvent(new CustomEvent("refreshNotifications"));

        setOriginalData(dataToSave);
        setData(dataToSave);
        setHasUnsavedChanges(false);
        toast.success("Section 1.1 updated successfully");
      } catch (error) {
        console.error("Error saving section 1.1:", error);
        toast.error("Failed to save section 1.1 changes");
        throw error;
      }
    }, [hasUnsavedChanges, tabId, data, competitorData]);

    // Expose save function to parent
    useImperativeHandle(
      ref,
      () => ({
        save,
      }),
      [save]
    );

    // Prepare data for the tanstack table
    const tableData = useMemo(() => {
      return competitorData.map((competitor, index) => ({
        ...competitor,
        index: index,
      })) as TableRow[];
    }, [competitorData]);

    // Table sorting state - initialize from localStorage
    const [sorting, setSorting] = useState<SortingState>(() => {
      // Load sorting state from localStorage on initial render
      const savedSettings = loadColumnSettings();
      return savedSettings.sorting;
    });

    // Ref to store the last sorting state when editing mode is enabled
    const lastSortingRef = useRef<SortingState>(sorting);

    // Update lastSortingRef whenever sorting changes outside of edit mode
    useEffect(() => {
      if (!isEditing) {
        lastSortingRef.current = sorting;
      }
    }, [sorting, isEditing]);

    // Handle sorting changes - only allow when not editing
    const handleSortingChange = useCallback(
      (updaterOrValue: unknown) => {
        // Prevent sorting changes when in edit mode
        if (isEditing) {
          return;
        }

        setSorting((currentSorting) => {
          const newSorting =
            typeof updaterOrValue === "function"
              ? updaterOrValue(currentSorting)
              : updaterOrValue;

          // Persist to localStorage
          updateSortingState(newSorting as SortingState);

          return newSorting as SortingState;
        });
      },
      [isEditing]
    );

    // Ref to access modified fields without causing re-renders
    const modifiedFieldsRef = useRef<Map<number, Set<string>>>(new Map());
    modifiedFieldsRef.current = modifiedCompetitorFields;

    // Stable function to check if a field is modified
    const isFieldModified = useCallback(
      (rowIndex: number, fieldName: string): boolean => {
        const rowModifications = modifiedFieldsRef.current?.get(rowIndex);
        return rowModifications?.has(fieldName) ?? false;
      },
      []
    );

    // Ref to access copied cell without causing re-renders
    const copiedCellRef = useRef(copiedCell);
    copiedCellRef.current = copiedCell;

    // Create columns using extracted function with stable dependencies
    const columns = useMemo(() => {
      return createCompetitorColumns(
        dataRef.current, // Use ref for data
        handleCopy,
        copiedCellRef.current, // Use ref for copiedCell to prevent re-creation
        columnSettings.widths,
        isEditing,
        handleCompetitorChange,
        handleDeleteCompetitor,
        handleToggleVisibility,
        isFieldModified // Pass the stable function instead of the changing Map
      );
    }, [
      // Only include dependencies that should trigger column recreation
      columnSettings.widths,
      isEditing,
      handleCopy,
      handleCompetitorChange,
      handleDeleteCompetitor,
      handleToggleVisibility,
      isFieldModified, // This is stable and won't change
      // Note: We use refs for data and copiedCell to prevent columns from being
      // recreated during editing which causes focus loss
    ]);

    // Create the TanStack table with sorting and column visibility
    const table = useReactTable<TableRow>({
      data: tableData,
      columns,
      getCoreRowModel: getCoreRowModel(),
      ...(isEditing ? {} : { getSortedRowModel: getSortedRowModel() }), // Only add sorted model when not editing
      onSortingChange: handleSortingChange,
      onColumnSizingChange: handleColumnResize,
      enableSorting: !isEditing, // Disable sorting completely when editing
      state: {
        sorting: sorting,
        columnVisibility: columnSettings.visibility,
        columnSizing: columnSettings.widths,
      },
      columnResizeMode: "onChange" as ColumnResizeMode,
      enableColumnResizing: true,
    });

    return (
      <div className="space-y-6 h-full flex flex-col">
        {/* Basic Information Card */}
        <ProductInfoSection
          data={{
            features: data.features,
            color: data.color,
            size: data.size,
            pricerange: data.pricerange,
            launchIntoExistingListing: data.launchIntoExistingListing,
            dataDiveDashboardName: data.dataDiveDashboardName,
            dataDiveDashboardLink: data.dataDiveDashboardLink,
            totalSellersInNiche: data.totalSellersInNiche,
            googleTrendsKeyword: data.googleTrendsKeyword,
            user: data.user,
            editedAt: data.editedAt,
            editedBy: data.editedBy,
            isEdited: data.isEdited,
            versions: data.versions,
          }}
          isEditing={isEditing}
          onValuesChange={(values) =>
            handleProductInfoChange(values as Partial<Section11Data>)
          }
          modifiedFields={modifiedProductFields}
          onFieldChange={(
            field: string,
            value: string,
            originalData: unknown
          ) =>
            handleProductInfoFieldChange(field, value, {
              ...(originalData as Section11Data),
              competitors: data.competitors,
            })
          }
        />

        {/* Competitors Card */}
        <Card className="flex-shrink-0">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-red-600" />
                <span>Competitor Analysis</span>
                {data.competitors.length > 0 && (
                  <Badge variant="secondary">
                    {data.competitors.length} competitor
                    {data.competitors.length !== 1 ? "s" : ""}
                  </Badge>
                )}
                {hasCompetitorChanges() && (
                  <div className="flex items-center gap-1 ml-2">
                    <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                    <span className="text-xs text-amber-600 font-normal">
                      Unsaved changes
                    </span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {data.competitors.length > 0 && !isEditing && (
                  <ColumnToggle
                    columnVisibility={columnSettings.visibility}
                    onVisibilityChange={handleVisibilityChange}
                    onResetVisibility={handleResetVisibility}
                    onResetWidths={handleResetWidths}
                  />
                )}
                {isEditing && (
                  <>
                    <div className="flex items-center gap-2">
                      <Input
                        type="file"
                        id="fileImport"
                        accept=".csv"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleFileImport(file);
                          }
                        }}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          document.getElementById("fileImport")?.click()
                        }
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Import CSV
                      </Button>
                    </div>
                    <Button
                      onClick={handleAddCompetitor}
                      className="flex items-center gap-2"
                      size="sm"
                      type="button"
                    >
                      <Plus className="h-4 w-4" />
                      Add New Competitor
                    </Button>
                  </>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {data.competitors.length > 0 || competitorData.length > 0 ? (
              <CompetitorTable
                ref={tableContainerRef}
                table={table}
                competitors={competitorData}
                onCopy={(text) => handleCopy(text, "footer")}
              />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No competitor data available.</p>
                {isEditing ? (
                  <div className="mt-4">
                    <p className="text-sm mb-4">
                      Click the button above to add your first competitor.
                    </p>
                  </div>
                ) : (
                  <p className="text-sm">
                    Edit this tab to add competitor analysis data.
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Google Trends Widget */}
        <GoogleTrendsWidget
          keyword={data.googleTrendsKeyword || ""}
          className="flex-shrink-0"
          isEditing={isEditing}
          storedData={data.googleTrendsData}
          onKeywordChange={(keyword) => {
            // Update the keyword
            setData((prev) => ({
              ...prev,
              googleTrendsKeyword: keyword,
            }));
            // Mark as changed
            setHasUnsavedChanges(true);
          }}
          onDataFetched={(trendsData) => {
            // Update the data with fetched Google Trends data
            setData((prev) => ({
              ...prev,
              googleTrendsData: trendsData,
            }));
            // Mark as changed
            setHasUnsavedChanges(true);
          }}
        />

        {/* Tab Danger Zone - Only show in edit mode */}
        {isEditing && tabName && productId && productName && productSlug && (
          <TabDangerZone
            tabId={tabId}
            tabName={tabName}
            productId={productId}
            productName={productName}
            productSlug={productSlug}
            tabCreatorId={tabCreatorId}
            productOwnerId={productOwnerId}
            isEditMode={isEditing}
          />
        )}
      </div>
    );
  }
);

Section11Display.displayName = "Section11Display";
