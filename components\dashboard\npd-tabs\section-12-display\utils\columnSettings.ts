// Column settings utilities for localStorage persistence
import type { SortingState } from "@tanstack/react-table";

export interface ColumnSettings {
  visibility: Record<string, boolean>;
  widths: Record<string, number>;
  sorting: SortingState;
}

const STORAGE_KEY = "section12-column-settings";

// Default sorting state (empty by default)
export const defaultSortingState: SortingState = [];

// Default column visibility for pricing scenarios table
export const defaultColumnVisibility: Record<string, boolean> = {
  date: true,
  priceScenario: true,
  priceUsd: true,
  estRefundRate: true,
  referalFeePercent: true,
  fbaFeeUsd: true,
  fbaFeePercent: true,
  landedCostPercent: true,
  landedCostUsd: true,
  tacosPercent: true,
  netProfitPercent: true,
  netProfitUsd: true,
  commentary: true,
  actions: true,
};

// Default column widths for pricing scenarios table
export const defaultColumnWidths: Record<string, number> = {
  date: 100,
  priceScenario: 100,
  priceUsd: 80,
  estRefundRate: 100,
  referalFeePercent: 100,
  fbaFeeUsd: 80,
  fbaFeePercent: 80,
  landedCostPercent: 100,
  landedCostUsd: 100,
  tacosPercent: 80,
  netProfitPercent: 100,
  netProfitUsd: 100,
  commentary: 200,
  actions: 80,
};

// Column display names for the UI
export const columnDisplayNames: Record<string, string> = {
  date: "Date",
  priceScenario: "Price Scenario",
  priceUsd: "Price",
  estRefundRate: "Est Refund Rate",
  referalFeePercent: "Referral Fee",
  fbaFeeUsd: "FBA Fee",
  fbaFeePercent: "FBA Fee Percent",
  landedCostPercent: "Landed Cost Percent",
  landedCostUsd: "Landed Cost",
  tacosPercent: "TACOS",
  netProfitPercent: "Net Profit Percent",
  netProfitUsd: "Net Profit",
  commentary: "Commentary",
  actions: "Actions",
};

// Save column settings to localStorage
export function saveColumnSettings(settings: ColumnSettings): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch {
    // Silently handle localStorage errors
  }
}

// Load column settings from localStorage
export function loadColumnSettings(): ColumnSettings {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored) as ColumnSettings;
      return {
        visibility: { ...defaultColumnVisibility, ...parsed.visibility },
        widths: { ...defaultColumnWidths, ...parsed.widths },
        sorting: parsed.sorting || defaultSortingState,
      };
    }
  } catch (error) {
    console.warn("Failed to load column settings from localStorage:", error);
  }

  return {
    visibility: defaultColumnVisibility,
    widths: defaultColumnWidths,
    sorting: defaultSortingState,
  };
}

// Reset column settings to defaults
export function resetColumnSettings(): ColumnSettings {
  const defaultSettings = {
    visibility: defaultColumnVisibility,
    widths: defaultColumnWidths,
    sorting: defaultSortingState,
  };
  saveColumnSettings(defaultSettings);
  return defaultSettings;
}

// Update only column widths
export function updateColumnWidths(widths: Record<string, number>): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    widths,
  };
  saveColumnSettings(newSettings);
}

// Update sorting state
export function updateSortingState(sorting: SortingState): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    sorting,
  };
  saveColumnSettings(newSettings);
}

// Update only column visibility
export function updateColumnVisibility(
  visibility: Record<string, boolean>
): void {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    visibility,
  };
  saveColumnSettings(newSettings);
}

// Reset only column visibility to defaults
export function resetColumnVisibility(): Record<string, boolean> {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    visibility: defaultColumnVisibility,
  };
  saveColumnSettings(newSettings);
  return defaultColumnVisibility;
}

// Reset only column widths to defaults
export function resetColumnWidths(): Record<string, number> {
  const currentSettings = loadColumnSettings();
  const newSettings = {
    ...currentSettings,
    widths: defaultColumnWidths,
  };
  saveColumnSettings(newSettings);
  return defaultColumnWidths;
}
