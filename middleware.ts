import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = ["/"];
  const apiAuthRoutes = ["/api/auth"];

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.includes(pathname);

  // Check if it's an API auth route
  const isApiAuthRoute = apiAuthRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Check if it's any other API route
  const isOtherApiRoute = pathname.startsWith("/api/") && !isApiAuthRoute;

  // Allow access to public routes and API auth routes
  if (isPublicRoute || isApiAuthRoute) {
    return NextResponse.next();
  }

  // For other API routes, let them through but they should handle their own auth
  if (isOtherApiRoute) {
    return NextResponse.next();
  }

  // Check if user is trying to access dashboard routes
  const isDashboardRoute = pathname.startsWith("/dashboard");

  if (isDashboardRoute) {
    // Check if auth token exists
    const token = request.cookies.get("quickt-auth-token")?.value;

    if (!token) {
      // No token, redirect to home
      return NextResponse.redirect(new URL("/", request.url));
    }

    // Token exists, let the page handle verification
    return NextResponse.next();
  }

  // For all other routes, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    "/((?!_next/static|_next/image|favicon.ico|images|public).*)",
  ],
};
