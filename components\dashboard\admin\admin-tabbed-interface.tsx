"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Content } from "@/components/ui/tabs";
import { UserRoleManager } from "@/components/admin/user-role-manager";
import { RolePermissionManager as OldRolePermissionManager } from "@/components/admin/role-permission-manager";
import { RolePermissionManagement } from "@/components/dashboard/admin/role-permission-management";
import { ArchivedProductsManager } from "@/components/dashboard/admin/archived-products-manager";
import { ArchivedTabsManager } from "@/components/dashboard/admin/archived-tabs-manager";
import DatabaseManager from "@/components/admin/database-manager";
import {
  Users,
  Shield,
  Key,
  Database,
  Settings,
  Crown,
  Archive,
  FileText,
} from "lucide-react";

interface AdminTabbedInterfaceProps {
  userRole: string;
  isSuperAdmin: boolean;
}

export function AdminTabbedInterface({
  userRole,
  isSuperAdmin,
}: AdminTabbedInterfaceProps) {
  const [activeTab, setActiveTab] = useState("users");

  // Define tabs based on user role
  const tabs = [
    {
      id: "users",
      label: "User Management",
      icon: Users,
      description: "Manage users and their role assignments",
      component: <UserRoleManager />,
      available: true, // Available to both Admin and Super Admin
    },
    {
      id: "roles",
      label: isSuperAdmin ? "Roles & Permissions" : "Role Assignments",
      icon: isSuperAdmin ? Crown : Shield,
      description: isSuperAdmin
        ? "Create and manage roles and permissions"
        : "Manage role-permission relationships",
      component: isSuperAdmin ? (
        <RolePermissionManagement userRole={userRole} />
      ) : (
        <OldRolePermissionManager />
      ),
      available: true,
    },
    {
      id: "permissions",
      label: "Permission Details",
      icon: Key,
      description: "View detailed permission information",
      component: <PermissionDetailsTab />,
      available: isSuperAdmin, // Only for Super Admin
    },
    {
      id: "archived-products",
      label: "Archived NPD",
      icon: Archive,
      description: "Manage and restore archived NPD projects",
      component: <ArchivedProductsManager />,
      available: true, // Available to both Admin and Super Admin
    },
    {
      id: "archived-tabs",
      label: "Archived Tabs",
      icon: FileText,
      description: "Manage and restore archived tabs",
      component: <ArchivedTabsManager />,
      available: true, // Available to both Admin and Super Admin
    },
    {
      id: "database",
      label: "Database",
      icon: Database,
      description: "Database management and seeding operations",
      component: <DatabaseManager />,
      available: true,
    },
    {
      id: "system",
      label: "System Settings",
      icon: Settings,
      description: "System configuration and maintenance",
      component: <SystemSettingsTab />,
      available: isSuperAdmin, // Only for Super Admin
    },
  ];

  // Filter tabs based on availability
  const availableTabs = tabs.filter((tab) => tab.available);

  return (
    <div className="h-full flex flex-col">
      {/* Header - Similar to product detail header */}
      <div className="flex-shrink-0 px-6 py-4 border-b bg-muted/20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Admin Panel</h1>
            <p className="text-sm text-muted-foreground">
              Manage users, roles, permissions, and system settings
            </p>
            <div className="mt-2 flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                Current Role:
              </span>
              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium">
                {isSuperAdmin ? (
                  <Crown className="h-3 w-3" />
                ) : (
                  <Shield className="h-3 w-3" />
                )}
                {userRole}
              </span>
              {isSuperAdmin && (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-yellow-100 text-yellow-800 text-xs font-medium">
                  <Crown className="h-3 w-3" />
                  Full Access
                </span>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="hidden md:flex items-center gap-4 text-sm">
            <div className="text-center">
              <p className="font-medium">{availableTabs.length}</p>
              <p className="text-muted-foreground text-xs">Available Tabs</p>
            </div>
            <div className="text-center">
              <p className="font-medium">{isSuperAdmin ? "44" : "View Only"}</p>
              <p className="text-muted-foreground text-xs">Permissions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Interface */}
      <div className="flex-1 min-h-0">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="h-full flex flex-col"
        >
          {/* Tab Navigation - Similar to product tabs */}
          <div className="flex-shrink-0 px-6 border-b py-2 bg-muted/10">
            <div className="flex items-center justify-between">
              <TabsList className="inline-flex h-10 items-center justify-start rounded-lg bg-muted/60 p-1 text-muted-foreground shadow-sm">
                {availableTabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <TabsTrigger
                      key={tab.id}
                      value={tab.id}
                      className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium transition-all hover:bg-background/50"
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="hidden sm:inline">{tab.label}</span>
                      <span className="sm:hidden">
                        {tab.id === "users"
                          ? "Users"
                          : tab.id === "roles"
                          ? "Roles"
                          : tab.id === "permissions"
                          ? "Perms"
                          : tab.id === "archived-products"
                          ? "Products"
                          : tab.id === "archived-tabs"
                          ? "Tabs"
                          : tab.id === "database"
                          ? "DB"
                          : "System"}
                      </span>
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {/* Active Tab Info */}
              <div className="hidden lg:flex items-center gap-2 text-sm text-muted-foreground">
                <span>
                  {
                    availableTabs.find((tab) => tab.id === activeTab)
                      ?.description
                  }
                </span>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="flex-1 min-h-0 overflow-hidden">
            {availableTabs.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="mt-0 h-full overflow-auto"
              >
                <div className="h-full">
                  {/* Tab Header - Only show on mobile/tablet where description isn't visible */}
                  <div className="lg:hidden px-6 py-4 border-b bg-muted/5">
                    <div className="flex items-center gap-2 mb-1">
                      <tab.icon className="h-5 w-5 text-primary" />
                      <h2 className="text-lg font-semibold">{tab.label}</h2>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {tab.description}
                    </p>
                  </div>

                  {/* Tab Component Container */}
                  <div className="p-6 h-full">{tab.component}</div>
                </div>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  );
}

// Enhanced components for new tabs
function PermissionDetailsTab() {
  return (
    <div className="space-y-6 max-w-7xl">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="p-4 border rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <Key className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium">Total Permissions</h3>
          </div>
          <p className="text-2xl font-bold text-blue-700">44</p>
          <p className="text-sm text-blue-600">Across 8 categories</p>
        </div>
        <div className="p-4 border rounded-lg bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-2 mb-2">
            <Shield className="h-5 w-5 text-green-600" />
            <h3 className="font-medium">Active Roles</h3>
          </div>
          <p className="text-2xl font-bold text-green-700">10</p>
          <p className="text-sm text-green-600">Default system roles</p>
        </div>
        <div className="p-4 border rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium">Categories</h3>
          </div>
          <p className="text-2xl font-bold text-purple-700">8</p>
          <p className="text-sm text-purple-600">Organized by function</p>
        </div>
      </div>

      {/* Permission Categories */}
      <div className="border rounded-lg p-6 bg-white">
        <div className="flex items-center gap-2 mb-4">
          <Key className="h-5 w-5 text-primary" />
          <h3 className="font-medium">Permission Categories</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            {
              name: "PRODUCT",
              count: 7,
              color: "blue",
              description: "Product management",
            },
            {
              name: "PRODUCT_TABS",
              count: 9,
              color: "green",
              description: "Tab operations",
            },
            {
              name: "COMMUNICATION",
              count: 5,
              color: "purple",
              description: "Chat & mentions",
            },
            {
              name: "ACTIVITY",
              count: 5,
              color: "orange",
              description: "Logs & notifications",
            },
            {
              name: "USER_MANAGEMENT",
              count: 5,
              color: "red",
              description: "User operations",
            },
            {
              name: "ADMIN",
              count: 6,
              color: "indigo",
              description: "Admin panel",
            },
            {
              name: "DATA",
              count: 4,
              color: "teal",
              description: "Import/export",
            },
            {
              name: "SYSTEM",
              count: 3,
              color: "gray",
              description: "System settings",
            },
          ].map((category) => (
            <div
              key={category.name}
              className="p-4 rounded-lg border-l-4 bg-gray-50 border-gray-400"
            >
              <p className="text-sm font-medium text-gray-900">
                {category.name}
              </p>
              <p className="text-xs text-gray-600 mb-1">
                {category.description}
              </p>
              <p className="text-xs font-medium text-gray-700">
                {category.count} permissions
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function SystemSettingsTab() {
  return (
    <div className="space-y-6">
      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-4">System Information</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Environment</span>
            <span className="text-sm font-medium">Development</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Database</span>
            <span className="text-sm font-medium">PostgreSQL (Neon)</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Deployment</span>
            <span className="text-sm font-medium">Vercel</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Auth System</span>
            <span className="text-sm font-medium">JWT + Database Roles</span>
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4">
        <h3 className="font-medium mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <button className="p-3 text-left border rounded-md hover:bg-muted/50 transition-colors">
            <p className="font-medium text-sm">Clear Cache</p>
            <p className="text-xs text-muted-foreground">
              Clear application cache
            </p>
          </button>
          <button className="p-3 text-left border rounded-md hover:bg-muted/50 transition-colors">
            <p className="font-medium text-sm">System Health</p>
            <p className="text-xs text-muted-foreground">Check system status</p>
          </button>
          <button className="p-3 text-left border rounded-md hover:bg-muted/50 transition-colors">
            <p className="font-medium text-sm">Backup Settings</p>
            <p className="text-xs text-muted-foreground">
              Export configuration
            </p>
          </button>
          <button className="p-3 text-left border rounded-md hover:bg-muted/50 transition-colors">
            <p className="font-medium text-sm">View Logs</p>
            <p className="text-xs text-muted-foreground">Access system logs</p>
          </button>
        </div>
      </div>
    </div>
  );
}
