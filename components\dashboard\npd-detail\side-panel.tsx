"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ResizableHandle, ResizablePanel } from "@/components/ui/resizable";
import {
  ProductActivityPanel,
  ProductChatPanel,
} from "@/components/dashboard/panels";
import { useActivityData } from "@/hooks/use-activity-data";
import { useChatData } from "@/hooks/use-chat-data";
import { Activity, MessageCircle, X } from "lucide-react";

interface Product {
  id: string;
  name: string;
  isSubscribed?: boolean;
}

interface SidePanelProps {
  product: Product;
  openPanel: "activity" | "chat" | null;
  onPanelChange: (panel: "activity" | "chat" | null) => void;
  panelWidth: number;
  minPanelPercentage: number;
  maxPanelPercentage: number;
  // Removed unread counts - component will fetch its own data when panels are active
}

export function SidePanel({
  product,
  openPanel,
  onPanelChange,
  panelWidth,
  minPanelPercentage,
  maxPanelPercentage,
}: SidePanelProps) {
  const [pendingMention, setPendingMention] = useState<string | null>(null);

  // Listen for mention events to open chat tab
  useEffect(() => {
    const handleMentionEvent = (event: CustomEvent) => {
      const { username } = event.detail;

      // Store the username to pass to chat panel
      setPendingMention(username);

      // Switch to chat tab when @mention is clicked
      onPanelChange("chat");
    };

    window.addEventListener(
      "open-chat-with-mention",
      handleMentionEvent as EventListener
    );

    return () => {
      window.removeEventListener(
        "open-chat-with-mention",
        handleMentionEvent as EventListener
      );
    };
  }, [onPanelChange]);

  // Only fetch data when the specific panel is actually open
  // This eliminates unnecessary Server Action calls during product switching
  const { unreadCount: activityUnreadCount } = useActivityData(
    product.id,
    openPanel === "activity" // Only fetch when activity panel is open
  );

  const { unreadCount: chatUnreadCount } = useChatData(
    product.id,
    openPanel === "chat" // Only fetch when chat panel is open
  );
  if (!openPanel) {
    return null;
  }

  return (
    <>
      <ResizableHandle withHandle />
      <ResizablePanel
        defaultSize={panelWidth}
        minSize={minPanelPercentage}
        maxSize={maxPanelPercentage}
        className="h-full bg-background shadow-xl"
      >
        {/* Panel Tabs Navigation */}
        <Tabs
          value={openPanel}
          onValueChange={(value) => {
            const newPanel = value as "activity" | "chat" | null;
            onPanelChange(newPanel);
          }}
          className="h-full flex flex-col"
          key={`panel-${product.id}`} // Key to maintain panel state per product
        >
          <div className="border-t-2 py-3 px-2 bg-muted/10">
            <TabsList className="h-10 w-full justify-start rounded-lg bg-muted/40 p-1 gap-1 shadow-sm">
              <TabsTrigger
                value="activity"
                className="cursor-pointer relative h-10 rounded-lg border border-transparent bg-muted/30 ml-2 px-4 py-2 font-semibold text-muted-foreground shadow-none transition-all hover:bg-muted/60 hover:text-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary/20 data-[state=active]:shadow-md"
              >
                {/* Active indicator bar */}
                <div
                  className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-primary rounded-r-full opacity-0 data-[state=active]:opacity-100 transition-opacity"
                  data-state={openPanel === "activity" ? "active" : "inactive"}
                />
                <div className="flex items-center gap-2 relative">
                  {/* Unread badge - only show if user is subscribed */}
                  {product &&
                    product.isSubscribed &&
                    activityUnreadCount > 0 && (
                      <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[16px] h-4 flex items-center justify-center px-1 font-medium shadow-sm">
                        {activityUnreadCount > 99 ? "99+" : activityUnreadCount}
                      </div>
                    )}
                  <Activity className="h-4 w-4" />
                  Activity
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="chat"
                className="cursor-pointer relative h-10 rounded-lg border border-transparent bg-muted/30 px-4 py-2 font-semibold text-muted-foreground shadow-none transition-all hover:bg-muted/60 hover:text-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary/20 data-[state=active]:shadow-md"
              >
                {/* Active indicator bar */}
                <div
                  className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-primary rounded-r-full opacity-0 data-[state=active]:opacity-100 transition-opacity"
                  data-state={openPanel === "chat" ? "active" : "inactive"}
                />
                <div className="flex items-center gap-2 relative">
                  {/* Unread badge - only show if user is subscribed */}
                  {product && product.isSubscribed && chatUnreadCount > 0 && (
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[16px] h-4 flex items-center justify-center px-1 font-medium shadow-sm">
                      {chatUnreadCount > 99 ? "99+" : chatUnreadCount}
                    </div>
                  )}
                  <MessageCircle className="h-4 w-4" />
                  Chat
                </div>
              </TabsTrigger>
              {/* Close button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPanelChange(null)}
                className="ml-auto mr-2 h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-sm cursor-pointer"
                title="Close Panel"
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            </TabsList>
          </div>

          <TabsContent
            value="activity"
            className="border-b-2 mt-0 flex-1 flex flex-col min-h-0"
          >
            <ProductActivityPanel productId={product.id} />
          </TabsContent>

          <TabsContent
            value="chat"
            className="border-b-2 mt-0 flex-1 flex flex-col min-h-0"
          >
            <ProductChatPanel
              productId={product.id}
              productName={product.name}
              pendingMention={pendingMention}
              onMentionHandled={() => setPendingMention(null)}
            />
          </TabsContent>
        </Tabs>
      </ResizablePanel>
    </>
  );
}
