"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useEffect,
} from "react";
import {
  fetchAllNPDs,
  fetchNPDById as fetchNPDByIdAction,
  addTabToNPD,
  updateNPDStage,
  update<PERSON><PERSON><PERSON><PERSON>,
  createNPD,
  subscribeToNPD,
  unsubscribeFromNPD,
  addUsersToNPDSubscription,
  removeUsersFromNPDSubscription,
  updateNPDSettings,
} from "@/actions/npd.actions";
import type { ProductSummary, ProductWithTabs, NPDProductTab } from "@/types";
import type { ProductStage } from "@/lib/constants/product-stages";

// Enhanced NPD Context Types
interface NPDContextState {
  // Layer 1: Always available (lightweight)
  allProducts: ProductSummary[];

  // Layer 2: On-demand (full details)
  selectedProduct: ProductWithTabs | null;
  selectedProductId: string | null;

  // Layer 3: Cached recent products (smart prefetch)
  recentProducts: Map<string, ProductWithTabs>;

  // Loading states
  isLoadingProducts: boolean;
  isLoadingSelectedProduct: boolean;

  // Error states
  error: string | null;
  selectedProductError: string | null;

  // Pending slug to select after products load
  pendingSlug: string | null;
}

interface NPDContextActions {
  // Core actions
  selectProduct: (id: string) => Promise<void>;
  selectProductBySlug: (slug: string) => Promise<void>;
  refreshProducts: () => Promise<void>;
  clearSelectedProduct: () => void;

  // Tab management
  addTab: (tabData: {
    tabName: string;
    order: number;
    fields: Record<string, unknown>;
  }) => Promise<NPDProductTab>; // Return the new tab object

  // Product management
  updateStage: (productId: string, stage: string) => Promise<void>;
  updateBrand: (productId: string, brand: string) => Promise<void>;
  updateProductSettings: (
    productId: string,
    updates: {
      brand?: string;
      stage?: ProductStage;
      name?: string;
      slug?: string;
      description?: string;
      imageUrl?: string;
      usersToAdd?: string[];
      usersToRemove?: string[];
    }
  ) => Promise<void>;
  createNPD: (data: {
    name: string;
    brand: string;
    stage: string;
    description?: string;
    imageUrl?: string;
    subscribedUserIds?: string[];
  }) => Promise<ProductWithTabs>;

  // Subscription management
  subscribeToNPD: (productId: string) => Promise<void>;
  unsubscribeFromNPD: (productId: string) => Promise<void>;
  addUsersToSubscription: (
    productId: string,
    userIds: string[]
  ) => Promise<void>;
  removeUsersFromSubscription: (
    productId: string,
    userIds: string[]
  ) => Promise<void>;

  // Cache management
  preloadProduct: (id: string) => Promise<void>;
  clearProductCache: () => void;
}

type NPDContextType = NPDContextState & NPDContextActions;

// Action types for reducer
type NPDAction =
  | { type: "SET_LOADING_PRODUCTS"; payload: boolean }
  | { type: "SET_LOADING_SELECTED_PRODUCT"; payload: boolean }
  | { type: "SET_ALL_PRODUCTS"; payload: ProductSummary[] }
  | {
      type: "SET_SELECTED_PRODUCT";
      payload: { product: ProductWithTabs; id: string };
    }
  | { type: "CLEAR_SELECTED_PRODUCT" }
  | { type: "ADD_TO_RECENT"; payload: { id: string; product: ProductWithTabs } }
  | { type: "CLEAR_RECENT" }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_SELECTED_PRODUCT_ERROR"; payload: string | null }
  | { type: "SET_PENDING_SLUG"; payload: string | null };

// Initial state
const initialState: NPDContextState = {
  allProducts: [],
  selectedProduct: null,
  selectedProductId: null,
  recentProducts: new Map(),
  isLoadingProducts: false,
  isLoadingSelectedProduct: false,
  error: null,
  selectedProductError: null,
  pendingSlug: null,
};

// Reducer
function npdReducer(
  state: NPDContextState,
  action: NPDAction
): NPDContextState {
  switch (action.type) {
    case "SET_LOADING_PRODUCTS":
      return { ...state, isLoadingProducts: action.payload };

    case "SET_LOADING_SELECTED_PRODUCT":
      return { ...state, isLoadingSelectedProduct: action.payload };

    case "SET_ALL_PRODUCTS":
      return {
        ...state,
        allProducts: action.payload,
        error: null,
        isLoadingProducts: false,
      };

    case "SET_SELECTED_PRODUCT":
      const newRecentProducts = new Map(state.recentProducts);
      newRecentProducts.set(action.payload.id, action.payload.product);

      // Keep only last 5 products in cache
      if (newRecentProducts.size > 5) {
        const firstKey = newRecentProducts.keys().next().value;
        if (firstKey) {
          newRecentProducts.delete(firstKey);
        }
      }

      return {
        ...state,
        selectedProduct: action.payload.product,
        selectedProductId: action.payload.id,
        recentProducts: newRecentProducts,
        selectedProductError: null,
        isLoadingSelectedProduct: false,
      };

    case "CLEAR_SELECTED_PRODUCT":
      return {
        ...state,
        selectedProduct: null,
        selectedProductId: null,
        selectedProductError: null,
      };

    case "ADD_TO_RECENT":
      const updatedRecentProducts = new Map(state.recentProducts);
      updatedRecentProducts.set(action.payload.id, action.payload.product);

      if (updatedRecentProducts.size > 5) {
        const firstKey = updatedRecentProducts.keys().next().value;
        if (firstKey) {
          updatedRecentProducts.delete(firstKey);
        }
      }

      return {
        ...state,
        recentProducts: updatedRecentProducts,
      };

    case "CLEAR_RECENT":
      return {
        ...state,
        recentProducts: new Map(),
      };

    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
        isLoadingProducts: false,
      };

    case "SET_SELECTED_PRODUCT_ERROR":
      return {
        ...state,
        selectedProductError: action.payload,
        isLoadingSelectedProduct: false,
      };

    case "SET_PENDING_SLUG":
      return {
        ...state,
        pendingSlug: action.payload,
      };

    default:
      return state;
  }
}

// Context
const NPDContext = createContext<NPDContextType | null>(null);

// Hook to use the context
export const useNPDContext = () => {
  const context = useContext(NPDContext);
  if (!context) {
    throw new Error("useNPDContext must be used within a NPDProvider");
  }
  return context;
};

// Provider component
interface NPDProviderProps {
  children: React.ReactNode;
}

export const NPDProvider: React.FC<NPDProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(npdReducer, initialState);

  // Fetch all products (summary data)
  const refreshProducts = useCallback(async () => {
    try {
      dispatch({ type: "SET_LOADING_PRODUCTS", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      const products = await fetchAllNPDs();
      dispatch({
        type: "SET_ALL_PRODUCTS",
        payload: products as ProductSummary[],
      });
    } catch (error) {
      console.error("Error fetching NPDs:", error);
      dispatch({
        type: "SET_ERROR",
        payload:
          error instanceof Error ? error.message : "Failed to fetch NPDs",
      });
    }
  }, []);

  // Fetch NPD by ID with full details
  const fetchNPDById = useCallback(
    async (id: string): Promise<ProductWithTabs> => {
      const product = await fetchNPDByIdAction(id);
      if (!product) {
        throw new Error("NPD not found");
      }
      return product;
    },
    []
  );

  // Select product by ID
  const selectProduct = useCallback(
    async (id: string) => {
      try {
        // Check if already selected
        if (state.selectedProductId === id) {
          return;
        }

        dispatch({ type: "SET_LOADING_SELECTED_PRODUCT", payload: true });
        dispatch({ type: "SET_SELECTED_PRODUCT_ERROR", payload: null });

        // Add a minimum loading duration to prevent flashing
        const minLoadingTime = 150; // 150ms minimum
        const startTime = Date.now();

        // Check cache first
        const cachedProduct = state.recentProducts.get(id);
        if (cachedProduct) {
          // Even with cache, maintain minimum loading time
          const elapsed = Date.now() - startTime;
          const remainingTime = Math.max(0, minLoadingTime - elapsed);

          if (remainingTime > 0) {
            await new Promise((resolve) => setTimeout(resolve, remainingTime));
          }

          dispatch({
            type: "SET_SELECTED_PRODUCT",
            payload: { product: cachedProduct, id },
          });
          return;
        }

        const product = await fetchNPDById(id);

        // Ensure minimum loading time
        const elapsed = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsed);

        if (remainingTime > 0) {
          await new Promise((resolve) => setTimeout(resolve, remainingTime));
        }

        dispatch({
          type: "SET_SELECTED_PRODUCT",
          payload: { product, id },
        });
      } catch (error) {
        console.error("Error selecting product:", error);
        dispatch({
          type: "SET_SELECTED_PRODUCT_ERROR",
          payload:
            error instanceof Error ? error.message : "Failed to load product",
        });
      } finally {
        dispatch({ type: "SET_LOADING_SELECTED_PRODUCT", payload: false });
      }
    },
    [state.selectedProductId, state.recentProducts, fetchNPDById]
  );

  // Select product by slug
  const selectProductBySlug = useCallback(
    async (slug: string) => {
      // Check if we're already loading or have selected this product
      if (state.selectedProduct?.slug === slug && !state.selectedProductError) {
        return; // Already have this product loaded
      }

      // Always set loading state immediately when called
      dispatch({ type: "SET_LOADING_SELECTED_PRODUCT", payload: true });
      dispatch({ type: "SET_SELECTED_PRODUCT_ERROR", payload: null });

      // If products are still loading OR if we have no products yet, store the slug
      if (state.isLoadingProducts || state.allProducts.length === 0) {
        dispatch({ type: "SET_PENDING_SLUG", payload: slug });
        // Loading state is already set above
        return;
      }

      // Clear any pending slug since we're processing this one
      dispatch({ type: "SET_PENDING_SLUG", payload: null });

      const product = state.allProducts.find((p) => p.slug === slug);
      if (!product) {
        dispatch({
          type: "SET_SELECTED_PRODUCT_ERROR",
          payload: "Product not found",
        });
        dispatch({ type: "SET_LOADING_SELECTED_PRODUCT", payload: false });
        return;
      }

      await selectProduct(product.id);
    },
    [
      state.allProducts,
      state.isLoadingProducts,
      state.selectedProduct,
      state.selectedProductError,
      selectProduct,
    ]
  );

  // Preload product (for smart caching)
  const preloadProduct = useCallback(
    async (id: string) => {
      // Don't preload if already in cache or currently selected
      if (state.recentProducts.has(id) || state.selectedProductId === id) {
        return;
      }

      try {
        const product = await fetchNPDById(id);
        dispatch({
          type: "ADD_TO_RECENT",
          payload: { id, product },
        });
      } catch {
        // Silently fail for preload operations
      }
    },
    [state.recentProducts, state.selectedProductId, fetchNPDById]
  );

  // Clear selected product
  const clearSelectedProduct = useCallback(() => {
    dispatch({ type: "CLEAR_SELECTED_PRODUCT" });
  }, []);

  // Add tab to current product
  const addTab = useCallback(
    async (tabData: {
      tabName: string;
      order: number;
      fields: Record<string, unknown>;
    }) => {
      if (!state.selectedProduct) {
        throw new Error("No product selected");
      }

      const productId = state.selectedProduct.id;

      try {
        // Add the tab via server action first
        const newTab = await addTabToNPD(productId, tabData);

        // Trigger notification refresh for potential auto-subscription
        window.dispatchEvent(new CustomEvent("refreshNotifications"));

        // Immediately update the selected product with the new tab for instant UI feedback
        const updatedProduct = {
          ...state.selectedProduct,
          tabs: [...state.selectedProduct.tabs, newTab].sort(
            (a, b) => a.order - b.order
          ),
        };

        dispatch({
          type: "SET_SELECTED_PRODUCT",
          payload: { product: updatedProduct, id: productId },
        });

        // Refresh products list in the background to keep it in sync
        refreshProducts();

        // Update the cache as well
        dispatch({
          type: "ADD_TO_RECENT",
          payload: { id: productId, product: updatedProduct },
        });

        // Return the new tab so the component can switch to it
        return newTab;
      } catch (error) {
        console.error("Error adding tab:", error);
        dispatch({
          type: "SET_SELECTED_PRODUCT_ERROR",
          payload: error instanceof Error ? error.message : "Failed to add tab",
        });
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  // Update product stage
  const updateStage = useCallback(
    async (productId: string, stage: string) => {
      try {
        await updateNPDStage(productId, stage as ProductStage);

        // Refresh the selected NPD to get updated stage
        if (state.selectedProduct?.id === productId) {
          const updatedProduct = await fetchNPDByIdAction(productId);
          if (updatedProduct) {
            dispatch({
              type: "SET_SELECTED_PRODUCT",
              payload: { product: updatedProduct, id: productId },
            });
          }
        }

        // Refresh products list to keep it in sync
        refreshProducts();
      } catch (error) {
        console.error("Error updating product stage:", error);
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  // Update product brand
  const updateBrand = useCallback(
    async (productId: string, brand: string) => {
      try {
        await updateNPDBrand(productId, brand);

        // Refresh the selected NPD to get updated brand
        if (state.selectedProduct?.id === productId) {
          const updatedProduct = await fetchNPDByIdAction(productId);
          if (updatedProduct) {
            dispatch({
              type: "SET_SELECTED_PRODUCT",
              payload: { product: updatedProduct, id: productId },
            });
          }
        }

        // Refresh products list to keep it in sync
        refreshProducts();
      } catch (error) {
        console.error("Error updating product brand:", error);
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  // Create new NPD
  const createNPDAction = useCallback(
    async (data: {
      name: string;
      brand: string;
      stage: string;
      description?: string;
      imageUrl?: string;
      subscribedUserIds?: string[];
    }) => {
      try {
        const result = await createNPD(data);

        if (result.success && result.product) {
          // Add the new NPD to the existing NPDs list immediately
          const newNPDSummary = {
            id: result.product.id,
            name: result.product.name,
            brand: result.product.brand,
            slug: result.product.slug,
            stage: result.product.stage,
            description: result.product.description,
            imageUrl: result.product.imageUrl,
            createdAt: result.product.createdAt,
            updatedAt: result.product.updatedAt,
          };

          // Update the products list with the new product
          dispatch({
            type: "SET_ALL_PRODUCTS",
            payload: [...state.allProducts, newNPDSummary],
          });

          // Also immediately cache the full product data in recent products
          // so that selectProduct/selectProductBySlug can find it immediately
          dispatch({
            type: "ADD_TO_RECENT",
            payload: { id: result.product.id, product: result.product },
          });

          return result.product;
        } else {
          throw new Error("Failed to create product");
        }
      } catch (error) {
        console.error("Error creating product:", error);
        throw error;
      }
    },
    [state.allProducts]
  );

  // Subscription management functions
  const subscribeToNPDAction = useCallback(
    async (productId: string) => {
      try {
        await subscribeToNPD(productId);

        // Update the selected NPD if it's the one being subscribed to
        if (state.selectedProduct && state.selectedProduct.id === productId) {
          const updatedNPD = {
            ...state.selectedProduct,
            isSubscribed: true,
          };
          dispatch({
            type: "SET_SELECTED_PRODUCT",
            payload: { product: updatedNPD, id: productId },
          });
        }

        // Refresh products to update the subscription status
        await refreshProducts();
      } catch (error) {
        console.error("Error subscribing to product:", error);
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  const unsubscribeFromNPDAction = useCallback(
    async (productId: string) => {
      try {
        await unsubscribeFromNPD(productId);

        // Update the selected NPD if it's the one being unsubscribed from
        if (state.selectedProduct && state.selectedProduct.id === productId) {
          const updatedNPD = {
            ...state.selectedProduct,
            isSubscribed: false,
          };
          dispatch({
            type: "SET_SELECTED_PRODUCT",
            payload: { product: updatedNPD, id: productId },
          });
        }

        // Refresh products to update the subscription status
        await refreshProducts();
      } catch (error) {
        console.error("Error unsubscribing from product:", error);
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  const addUsersToSubscriptionAction = useCallback(
    async (productId: string, userIds: string[]) => {
      try {
        await addUsersToNPDSubscription(productId, userIds);

        // Refresh the selected NPD if it's the one being updated
        if (state.selectedProduct && state.selectedProduct.id === productId) {
          await selectProduct(productId);
        }
      } catch (error) {
        console.error("Error adding users to subscription:", error);
        throw error;
      }
    },
    [state.selectedProduct, selectProduct]
  );

  const removeUsersFromSubscriptionAction = useCallback(
    async (productId: string, userIds: string[]) => {
      try {
        await removeUsersFromNPDSubscription(productId, userIds);

        // Refresh the selected NPD if it's the one being updated
        if (state.selectedProduct && state.selectedProduct.id === productId) {
          await selectProduct(productId);
        }
      } catch (error) {
        console.error("Error removing users from subscription:", error);
        throw error;
      }
    },
    [state.selectedProduct, selectProduct]
  );

  // Update product settings atomically
  const updateProductSettingsAction = useCallback(
    async (
      productId: string,
      updates: {
        brand?: string;
        stage?: ProductStage;
        name?: string;
        slug?: string;
        description?: string;
        imageUrl?: string;
        usersToAdd?: string[];
        usersToRemove?: string[];
      }
    ) => {
      try {
        await updateNPDSettings(productId, updates);

        // Refresh the selected NPD to get all updates
        if (state.selectedProduct?.id === productId) {
          const updatedProduct = await fetchNPDByIdAction(productId);
          if (updatedProduct) {
            dispatch({
              type: "SET_SELECTED_PRODUCT",
              payload: { product: updatedProduct, id: productId },
            });
          }
        }

        // Refresh products list to keep it in sync
        refreshProducts();
      } catch (error) {
        console.error("Error updating product settings:", error);
        throw error;
      }
    },
    [state.selectedProduct, refreshProducts]
  );

  // Clear product cache
  const clearProductCache = useCallback(() => {
    dispatch({ type: "CLEAR_RECENT" });
  }, []);

  // Process pending slug when products finish loading
  useEffect(() => {
    if (
      !state.isLoadingProducts &&
      state.pendingSlug &&
      state.allProducts.length > 0
    ) {
      const slug = state.pendingSlug;

      // Clear the pending slug first
      dispatch({ type: "SET_PENDING_SLUG", payload: null });

      // Find the product directly here to avoid recursion
      const product = state.allProducts.find((p) => p.slug === slug);
      if (product) {
        // The loading state should already be set from selectProductBySlug
        // Just call selectProduct directly
        selectProduct(product.id);
      } else {
        dispatch({
          type: "SET_SELECTED_PRODUCT_ERROR",
          payload: "Product not found",
        });
        dispatch({ type: "SET_LOADING_SELECTED_PRODUCT", payload: false });
      }
    }
  }, [
    state.isLoadingProducts,
    state.pendingSlug,
    state.allProducts,
    selectProduct,
  ]);

  // Load products on mount
  useEffect(() => {
    refreshProducts();
  }, [refreshProducts]);

  const contextValue: NPDContextType = {
    // State
    ...state,

    // Actions
    selectProduct,
    selectProductBySlug,
    refreshProducts,
    clearSelectedProduct,
    addTab,
    updateStage,
    updateBrand,
    updateProductSettings: updateProductSettingsAction,
    createNPD: createNPDAction,
    preloadProduct,
    clearProductCache,
    subscribeToNPD: subscribeToNPDAction,
    unsubscribeFromNPD: unsubscribeFromNPDAction,
    addUsersToSubscription: addUsersToSubscriptionAction,
    removeUsersFromSubscription: removeUsersFromSubscriptionAction,
  };

  return (
    <NPDContext.Provider value={contextValue}>{children}</NPDContext.Provider>
  );
};
