// Function to validate image files
export function validateImageFiles(files: File[], maxSizeMB: number = 5): {
  validFiles: File[];
  errors: string[];
} {
  const validFiles: File[] = [];
  const errors: string[] = [];

  for (const file of files) {
    if (!file.type.startsWith("image/")) {
      errors.push(`${file.name} is not a valid image file`);
      continue;
    }

    if (file.size > maxSizeMB * 1024 * 1024) {
      errors.push(`${file.name} is too large (max ${maxSizeMB}MB)`);
      continue;
    }

    validFiles.push(file);
  }

  return { validFiles, errors };
}

// Function to extract public ID from Cloudinary URL
export function extractPublicIdFromUrl(url: string): string | null {
  try {
    const urlParts = url.split("/");
    const publicIdWithExtension = urlParts.slice(-1)[0];
    return publicIdWithExtension.split(".")[0];
  } catch {
    return null;
  }
}

// Function to check if URL is a Cloudinary URL
export function isCloudinaryUrl(url: string): boolean {
  return url.includes("cloudinary.com") || url.includes("res.cloudinary.com");
}

// Function to normalize images to array
export function normalizeImages(images: string[] | string | null): string[] {
  if (Array.isArray(images)) return images;
  if (typeof images === "string") return images.split(",").filter(Boolean);
  return [];
}
