import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Navigate to a product and optionally update the sidebar brand filter
 * @param router - Next.js router instance
 * @param productSlug - The product slug to navigate to
 * @param productBrand - The product brand to set as the brand filter (optional)
 */
export function navigateToProductWithBrandFilter(
  router: { push: (url: string) => void },
  productSlug: string,
  productBrand?: string
) {
  // Update brand filter if provided
  if (productBrand) {
    localStorage.setItem("npd_selected_brand", productBrand);

    // Dispatch custom event for same-tab localStorage changes
    window.dispatchEvent(
      new CustomEvent("localStorageChange", {
        detail: { key: "npd_selected_brand", newValue: productBrand },
      })
    );
  }

  // Navigate to the NPD
  router.push(`/dashboard/npd/${productSlug}`);
}
