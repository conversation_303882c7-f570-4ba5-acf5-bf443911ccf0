"use server";

/**
 * Request-scoped cache for serverless environments
 * Cache persists only for the duration of a single request/function invocation
 * Perfect for Vercel serverless functions with Neon database
 */

interface RequestCacheEntry<T> {
  data: T;
  timestamp: number;
}

class RequestCache {
  private cache = new Map<string, RequestCacheEntry<unknown>>();
  private requestId: string;

  constructor() {
    // Generate unique request ID for this function invocation
    this.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get data from request cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    return entry.data as T;
  }

  /**
   * Set data in request cache
   */
  set<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }

  /**
   * Delete specific key from cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics for this request
   */
  getStats() {
    return {
      requestId: this.requestId,
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Request-scoped cache instance (new for each serverless invocation)
let requestCache: RequestCache | null = null;

/**
 * Get or create request cache for current invocation
 */
function getRequestCache(): RequestCache {
  if (!requestCache) {
    requestCache = new RequestCache();
  }
  return requestCache;
}

/**
 * Cache data for the duration of the current request
 */
export async function withRequestCache<T>(
  key: string,
  fetcher: () => Promise<T>
): Promise<T> {
  const cache = getRequestCache();
  
  // Try to get from cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch fresh data
  const data = await fetcher();
  
  // Cache the result for this request
  cache.set(key, data);
  
  return data;
}

/**
 * Memoize function results within a single request
 */
export function memoizeForRequest<TArgs extends unknown[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  keyGenerator?: (...args: TArgs) => string
): (...args: TArgs) => Promise<TReturn> {
  return async (...args: TArgs): Promise<TReturn> => {
    const key = keyGenerator 
      ? keyGenerator(...args)
      : `memoized:${fn.name}:${JSON.stringify(args)}`;
    
    return withRequestCache(key, () => fn(...args));
  };
}

/**
 * Cache database queries within a single request
 */
export function cacheQuery<T>(
  queryName: string,
  query: () => Promise<T>
): Promise<T> {
  return withRequestCache(`query:${queryName}`, query);
}

/**
 * Batch multiple database operations within a request
 */
export class RequestBatcher<TInput, TOutput> {
  private batch: TInput[] = [];
  private promises: Array<{
    resolve: (value: TOutput) => void;
    reject: (error: Error) => void;
  }> = [];
  private timeout: NodeJS.Timeout | null = null;
  private readonly batchProcessor: (items: TInput[]) => Promise<TOutput[]>;
  private readonly batchDelay: number;

  constructor(
    batchProcessor: (items: TInput[]) => Promise<TOutput[]>,
    batchDelay: number = 10 // Very short delay for serverless
  ) {
    this.batchProcessor = batchProcessor;
    this.batchDelay = batchDelay;
  }

  add(item: TInput): Promise<TOutput> {
    return new Promise((resolve, reject) => {
      this.batch.push(item);
      this.promises.push({ resolve, reject });

      // Clear existing timeout
      if (this.timeout) {
        clearTimeout(this.timeout);
      }

      // Schedule batch processing
      this.timeout = setTimeout(() => {
        this.processBatch();
      }, this.batchDelay);
    });
  }

  private async processBatch(): Promise<void> {
    if (this.batch.length === 0) return;

    const currentBatch = [...this.batch];
    const currentPromises = [...this.promises];
    
    // Clear current batch
    this.batch = [];
    this.promises = [];
    this.timeout = null;

    try {
      const results = await this.batchProcessor(currentBatch);
      
      // Resolve all promises with corresponding results
      currentPromises.forEach((promise, index) => {
        promise.resolve(results[index]);
      });
    } catch (error) {
      // Reject all promises with the error
      currentPromises.forEach((promise) => {
        promise.reject(error instanceof Error ? error : new Error('Batch processing failed'));
      });
    }
  }
}

/**
 * Reset request cache (useful for testing)
 */
export function resetRequestCache(): void {
  requestCache = null;
}

/**
 * Get current request cache stats (for debugging)
 */
export function getRequestCacheStats() {
  return requestCache?.getStats() || null;
}
