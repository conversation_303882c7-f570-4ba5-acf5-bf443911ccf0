"use client";

import { useEffect, useState, useCallback } from "react";
import { getProductActivityLogs } from "@/actions/activity-log.actions";

interface ProductActivityLog {
  id: string;
  action: string;
  description: string;
  user?: {
    name: string;
    email: string;
    image?: string;
  };
  productTab?: {
    tabName: string;
    order: number;
  };
  changes?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  createdAt: string;
}

export function useProductActivityLogs(productId: string) {
  const [logs, setLogs] = useState<ProductActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLogs = useCallback(async () => {
    if (!productId) {
      setLogs([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const result = await getProductActivityLogs(productId);

      if (result.success && result.data) {
        setLogs(
          result.data.map((log) => ({
            id: log.id,
            action: log.action,
            description: log.description,
            user: log.user
              ? {
                  name: log.user.name,
                  email: log.user.email,
                  image: log.user.image || undefined,
                }
              : undefined,
            productTab: log.npdProductTab
              ? {
                  tabName: log.npdProductTab.tabName,
                  order: log.npdProductTab.order,
                }
              : undefined,
            changes: log.changes as Record<string, unknown>,
            metadata: log.metadata as Record<string, unknown>,
            createdAt: log.createdAt.toISOString(),
          }))
        );
      } else {
        setError(result.error || "Failed to fetch activity logs");
        setLogs([]);
      }
    } catch {
      setError("Failed to fetch activity logs");
      setLogs([]);
    } finally {
      setIsLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  return { logs, isLoading, error, refetch: fetchLogs };
}
