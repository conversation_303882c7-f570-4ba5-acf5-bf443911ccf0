"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/context/auth-context";

// Note: This hook now works with database-only permissions
// Constants have been removed in favor of database-only system

interface UsePermissionsReturn {
  permissions: string[];
  roles: string[];
  loading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  hasAnyPermissions: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRoles: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  refetch: () => Promise<void>;
}

export function usePermissions(): UsePermissionsReturn {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = useCallback(async () => {
    if (!user?.userId) {
      setPermissions([]);
      setRoles([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/users/${user.userId}/permissions`);

      if (!response.ok) {
        // If user not found or has no roles, treat as pending approval (no permissions)
        if (response.status === 404) {
          setPermissions([]);
          setRoles([]);
          return;
        }
        throw new Error(`Failed to fetch permissions: ${response.statusText}`);
      }

      const data = await response.json();
      setPermissions(data.permissions || []);
      setRoles(data.roles || []);
    } catch (err) {
      console.error("Error fetching permissions:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch permissions"
      );
      setPermissions([]);
      setRoles([]);
    } finally {
      setLoading(false);
    }
  }, [user?.userId]);

  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  // Permission checking functions
  const hasPermission = useCallback(
    (permission: string): boolean => {
      return permissions.includes(permission);
    },
    [permissions]
  );

  const hasAnyPermissionsCheck = useCallback(
    (requiredPermissions: string[]): boolean => {
      return requiredPermissions.some((permission) =>
        permissions.includes(permission)
      );
    },
    [permissions]
  );

  const hasAllPermissionsCheck = useCallback(
    (requiredPermissions: string[]): boolean => {
      return requiredPermissions.every((permission) =>
        permissions.includes(permission)
      );
    },
    [permissions]
  );

  // Role checking functions
  const hasRole = useCallback(
    (role: string): boolean => {
      return roles.includes(role);
    },
    [roles]
  );

  const hasAnyRolesCheck = useCallback(
    (requiredRoles: string[]): boolean => {
      return requiredRoles.some((role) => roles.includes(role));
    },
    [roles]
  );

  const hasAllRolesCheck = useCallback(
    (requiredRoles: string[]): boolean => {
      return requiredRoles.every((role) => roles.includes(role));
    },
    [roles]
  );

  // Admin role checks
  const isAdmin = roles.includes("ADMIN") || roles.includes("SUPER_ADMIN");
  const isSuperAdmin = roles.includes("SUPER_ADMIN");

  return {
    permissions,
    roles,
    loading,
    error,
    hasPermission,
    hasAnyPermissions: hasAnyPermissionsCheck,
    hasAllPermissions: hasAllPermissionsCheck,
    hasRole,
    hasAnyRoles: hasAnyRolesCheck,
    hasAllRoles: hasAllRolesCheck,
    isAdmin,
    isSuperAdmin,
    refetch: fetchPermissions,
  };
}

// Convenience hooks for common permission checks
export function useHasPermission(permission: string): boolean {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
}

export function useHasRole(role: string): boolean {
  const { hasRole } = usePermissions();
  return hasRole(role);
}

export function useIsAdmin(): boolean {
  const { isAdmin } = usePermissions();
  return isAdmin;
}

export function useIsSuperAdmin(): boolean {
  const { isSuperAdmin } = usePermissions();
  return isSuperAdmin;
}
