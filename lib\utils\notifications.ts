/**
 * Utility functions for creating and managing notifications
 */

import { prisma } from "@/lib/db/prisma";
import { Prisma } from "@/lib/generated/prisma";

export interface CreateNotificationParams {
  userId: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, unknown>;
}

/**
 * Create a new notification for a user
 */
export async function createNotification(params: CreateNotificationParams) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId: params.userId,
        type: params.type,
        title: params.title,
        message: params.message,
        data: params.data ? (params.data as Prisma.JsonObject) : Prisma.DbNull,
        read: false,
        hidden: false, // Explicitly set hidden field
      },
    });

    return notification;
  } catch (error) {
    throw error;
  }
}

/**
 * Create role update notification
 */
export async function createRoleUpdateNotification(
  userId: string,
  newRoles: string[],
  oldRoles: string[] = []
) {
  const addedRoles = newRoles.filter((role) => !oldRoles.includes(role));
  const removedRoles = oldRoles.filter((role) => !newRoles.includes(role));

  let message = "";
  if (addedRoles.length > 0 && removedRoles.length > 0) {
    message = `Roles updated: Added ${addedRoles.join(
      ", "
    )}, Removed ${removedRoles.join(", ")}`;
  } else if (addedRoles.length > 0) {
    message = `New role${
      addedRoles.length > 1 ? "s" : ""
    } assigned: ${addedRoles.join(", ")}`;
  } else if (removedRoles.length > 0) {
    message = `Role${
      removedRoles.length > 1 ? "s" : ""
    } removed: ${removedRoles.join(", ")}`;
  } else {
    message = "Your roles have been updated";
  }

  return createNotification({
    userId,
    type: "role_update",
    title: "Role Updated",
    message,
    data: {
      newRoles,
      oldRoles,
      addedRoles,
      removedRoles,
    },
  });
}

/**
 * Notify admins and super admins about new user registration
 */
export async function notifyAdminsAboutNewUser(
  newUserId: string,
  newUserName: string,
  newUserEmail: string
) {
  try {
    // Get all users with ADMIN or SUPER_ADMIN roles
    const adminUsers = await prisma.user.findMany({
      where: {
        roles: {
          some: {
            name: {
              in: ["ADMIN", "SUPER_ADMIN"],
            },
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (adminUsers.length === 0) {
      console.warn(
        "No admin users found to notify about new user registration"
      );
      return;
    }

    // Create notifications for all admin users
    const notifications = adminUsers.map((admin) => ({
      userId: admin.id,
      type: "new_user_registration",
      title: "New User Awaiting Approval",
      message: `${newUserName} (${newUserEmail}) has signed up and is waiting for role assignment.`,
      data: {
        newUserId,
        newUserName,
        newUserEmail,
        registrationDate: new Date().toISOString(),
      },
    }));

    await prisma.notification.createMany({
      data: notifications,
    });

    console.log(
      `Notified ${adminUsers.length} admin(s) about new user: ${newUserEmail}`
    );
  } catch (error) {
    console.error("Error notifying admins about new user:", error);
    // Don't throw error to avoid breaking user registration flow
  }
}

/**
 * Create product assignment notification
 */
export async function createProductAssignmentNotification(
  userId: string,
  productName: string,
  productSlug: string
) {
  try {
    const result = await createNotification({
      userId,
      type: "product_assignment",
      title: "Product Subscription",
      message: `You've been subscribed to ${productName}`,
      data: {
        productName,
        productSlug,
      },
    });

    return result;
  } catch (error) {
    console.error(
      `🔔 [NOTIFICATION DEBUG] createProductAssignmentNotification failed:`,
      error
    );
    throw error;
  }
}

/**
 * Create product activity notification for all subscribers
 */
export async function createProductActivityNotification(
  productId: string,
  productName: string,
  productSlug: string,
  activityTitle: string,
  activityMessage: string,
  excludeUserId?: string
) {
  try {
    // Get all users subscribed to this product
    const subscriptions = await prisma.nPDProductSubscription.findMany({
      where: {
        npdProductId: productId,
        ...(excludeUserId && {
          userId: { not: excludeUserId },
        }),
      },
      include: {
        user: true,
      },
    });

    const notifications = await Promise.all(
      subscriptions.map((subscription: { userId: string }) =>
        createNotification({
          userId: subscription.userId,
          type: "product_activity",
          title: `${productName} - ${activityTitle}`,
          message: activityMessage,
          data: {
            productId,
            productName,
            productSlug,
            activityTitle,
          },
        })
      )
    );
    return notifications;
  } catch (error) {
    console.error("Error creating product activity notifications:", error);
    throw error;
  }
}

/**
 * Create user mention notification
 */
export async function createUserMentionNotification(
  userId: string,
  mentionedByUserName: string,
  context: string,
  actionUrl?: string,
  messageId?: string,
  messageContent?: string
) {
  return createNotification({
    userId,
    type: "user_mention",
    title: "You were mentioned",
    message: `${mentionedByUserName} mentioned you in ${context}`,
    data: {
      mentionedByUserName,
      context,
      actionUrl,
      messageId,
      messageContent: messageContent?.slice(0, 100), // Truncate for storage
    },
  });
}

/**
 * Bulk create notifications
 */
export async function createBulkNotifications(
  notifications: CreateNotificationParams[]
) {
  try {
    const createdNotifications = await prisma.notification.createMany({
      data: notifications.map((notification) => ({
        userId: notification.userId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data
          ? (notification.data as Prisma.JsonObject)
          : Prisma.DbNull,
        read: false,
      })),
    });

    return createdNotifications;
  } catch (error) {
    console.error("Error creating bulk notifications:", error);
    throw error;
  }
}
