"use server";

/**
 * Simple in-memory cache for Server Actions
 * Useful for caching frequently accessed data that doesn't change often
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private maxSize = 1000; // Maximum number of entries

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttlMs: number = 300000): void {
    // Default 5 minutes
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs,
    });
  }

  /**
   * Delete specific key from cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys()),
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
export const memoryCache = new MemoryCache();

/**
 * Cache TTL constants (in milliseconds)
 */
export const CACHE_TTL = {
  // User data - rarely changes
  USER_PROFILE: 10 * 60 * 1000, // 10 minutes
  USER_ROLES: 15 * 60 * 1000, // 15 minutes

  // Product data - changes occasionally
  PRODUCT_LIST: 5 * 60 * 1000, // 5 minutes
  PRODUCT_DETAILS: 3 * 60 * 1000, // 3 minutes

  // Activity/Chat counts - changes frequently
  UNREAD_COUNTS: 30 * 1000, // 30 seconds

  // Static data - rarely changes
  PERMISSIONS: 30 * 60 * 1000, // 30 minutes
  ROLES: 30 * 60 * 1000, // 30 minutes
} as const;

/**
 * Helper function to generate cache keys
 */
export function generateCacheKey(prefix: string, ...parts: string[]): string {
  return `${prefix}:${parts.join(":")}`;
}

/**
 * Wrapper function for caching Server Action results
 */
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlMs: number = CACHE_TTL.PRODUCT_DETAILS
): Promise<T> {
  // Try to get from cache first
  const cached = memoryCache.get<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch fresh data
  const data = await fetcher();

  // Cache the result
  memoryCache.set(key, data, ttlMs);

  return data;
}

/**
 * Invalidate cache entries by pattern
 */
export function invalidateCachePattern(pattern: string): void {
  const keys = Array.from(memoryCache.getStats().keys);
  for (const key of keys) {
    if (key.includes(pattern)) {
      memoryCache.delete(key);
    }
  }
}

// Cleanup expired entries every 5 minutes
if (typeof setInterval !== "undefined") {
  setInterval(() => {
    memoryCache.cleanup();
  }, 5 * 60 * 1000);
}
