"use client";

import React, { useCallback, useRef, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useNPDContext } from "@/context/npd-context";
import type { ProductWithTabs } from "@/types";
import {
  TAB_COMPONENTS,
  type TabComponentName,
} from "@/components/dashboard/npd-tabs";
import { type OverviewDisplayRef } from "@/components/dashboard/npd-tabs/overview-display";
import { type Section11DisplayRef } from "@/components/dashboard/npd-tabs/section-11-display";
import { type Section12DisplayRef } from "@/components/dashboard/npd-tabs/section-12-display";
import { type Section13DisplayRef } from "@/components/dashboard/npd-tabs/section-13-display";
import { type SourcingBriefDisplayRef } from "@/components/dashboard/npd-tabs/sourcing-brief-display";
import { useChatMentions } from "@/hooks/use-chat-mentions";
import { Tabs } from "@/components/ui/tabs";
import { ResizablePanelGroup, ResizablePanel } from "@/components/ui/resizable";
import { NavigationWarningDialog } from "./npd-tabs/shared";
import { TabRestoreChoiceDialog } from "./npd-tabs/shared/tab-restore-choice-dialog";
import {
  checkArchivedTab,
  manualRestoreTab,
} from "@/actions/tab-restoration.actions";
import { toast } from "sonner";

import {
  ProductHeader,
  LoadingState,
  ErrorState,
  EmptyState,
  TabNavigation,
  TabContentArea,
  SidePanel,
  useProductDetail,
} from "./npd-detail";

export function NPDDetailContent() {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);

  // Handle toast messages from URL parameters after page reload
  useEffect(() => {
    // Get URL parameters directly from window.location to avoid React dependency issues
    const urlParams = new URLSearchParams(window.location.search);
    const toastType = urlParams.get("toast");
    const message = urlParams.get("message");

    if (toastType && message) {
      const decodedMessage = decodeURIComponent(message);

      // Immediately clean up URL parameters to prevent re-processing
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("toast");
      newUrl.searchParams.delete("message");
      window.history.replaceState({}, "", newUrl.toString());

      // Show toast after URL cleanup
      if (toastType === "success") {
        toast.success(decodedMessage);
      } else if (toastType === "error") {
        toast.error(decodedMessage);
      }
    }
  }, []); // Empty dependency array - run only once on mount

  // Custom hook for state management
  const {
    slug,
    activeTab,
    isEditing,
    hasUnsavedChanges,
    showConfirmDialog,
    pendingAction,
    openPanel,
    panelWidth,
    selectedProduct,
    selectedProductError,
    isLoading,
    setActiveTab,
    setIsEditing,
    setHasUnsavedChanges,
    setShowConfirmDialog,
    setPendingAction,
    setOpenPanel,
    setPanelWidth,
    updateUrlWithTab,
    tabNameToUrlSlug,
  } = useProductDetail();

  const { addTab, selectProduct, clearProductCache } = useNPDContext();

  // Add tab state
  const [showAddTab, setShowAddTab] = React.useState<TabComponentName | null>(
    null
  );

  // Refs for tabs to access save functions
  const overviewRef = useRef<OverviewDisplayRef>(null);
  const section11Ref = useRef<Section11DisplayRef>(null);
  const section12Ref = useRef<Section12DisplayRef>(null);
  const section13Ref = useRef<Section13DisplayRef>(null);
  const sourcingBriefRef = useRef<SourcingBriefDisplayRef>(null);

  // Chat mentions functionality
  const { openChatWithMention } = useChatMentions();

  // Restore choice dialog state
  const [restoreChoiceDialog, setRestoreChoiceDialog] = useState<{
    isOpen: boolean;
    tabName: string;
    archivedTab: {
      id: string;
      tabName: string;
      archivedAt?: Date | null;
      archivedByUser?: { name: string } | null;
    } | null;
  }>({
    isOpen: false,
    tabName: "",
    archivedTab: null,
  });

  // Panel constraints
  const minPanelPercentage = 20;
  const maxPanelPercentage = 50;

  // We'll pass the productId to the side panel components
  // and let them handle their own data fetching when needed
  // This is more efficient than global polling

  // Handler functions
  const handleAddTab = async (tabName: TabComponentName) => {
    if (!selectedProduct) return;

    try {
      // Check if there's an archived tab with this name
      const archivedTabResult = await checkArchivedTab(
        selectedProduct.id,
        tabName
      );

      if (archivedTabResult.hasArchivedTab && archivedTabResult.archivedTab) {
        // Show restore choice dialog
        setRestoreChoiceDialog({
          isOpen: true,
          tabName,
          archivedTab: archivedTabResult.archivedTab,
        });
      } else {
        // No archived tab, proceed with normal creation
        setShowAddTab(tabName);
      }
    } catch (error) {
      console.error("Error checking for archived tab:", error);
      // If check fails, proceed with normal creation
      setShowAddTab(tabName);
    }
  };

  const handleSaveTab = async (tabData: {
    tabName: string;
    order: number;
    fields: Record<string, unknown>;
  }) => {
    try {
      const newTab = await addTab(tabData);
      setShowAddTab(null);

      if (newTab?.id) {
        // Simple solution: reload the page and navigate to the new/restored tab
        const tabNameSlug = tabNameToUrlSlug(tabData.tabName);
        const successMessage = encodeURIComponent(
          `${tabData.tabName} tab created successfully!`
        );
        window.location.href = `/dashboard/npd/${selectedProduct?.slug}?tab=${tabNameSlug}&toast=success&message=${successMessage}`;
      }
    } catch (error) {
      console.error("Failed to add tab:", error);
    }
  };

  const handleCancelAddTab = () => {
    setShowAddTab(null);
  };

  // Handle restore choice dialog actions
  const handleRestoreOriginal = async () => {
    if (!restoreChoiceDialog.archivedTab) return;

    try {
      const result = await manualRestoreTab(restoreChoiceDialog.archivedTab.id);

      if (result.success) {
        // Simple solution: reload the page and navigate to the restored tab
        const tabNameSlug = tabNameToUrlSlug(result.tabName);
        const successMessage = encodeURIComponent(
          "Tab restored successfully with original data!"
        );
        window.location.href = `/dashboard/npd/${result.productSlug}?tab=${tabNameSlug}&toast=success&message=${successMessage}`;
      }
    } catch (error) {
      console.error("Error restoring original tab:", error);

      // If manual restore fails, fall back to fresh start
      const errorMessage = encodeURIComponent(
        "Failed to restore tab. Trying fresh start instead..."
      );
      // Reload with error message and then trigger fresh start
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.set("toast", "error");
      currentUrl.searchParams.set("message", errorMessage);
      window.location.href = currentUrl.toString();
    }
  };

  const handleStartFresh = () => {
    // Proceed with normal tab creation (which will auto-restore with fresh data)
    setShowAddTab(restoreChoiceDialog.tabName as TabComponentName);
  };

  const handleCloseRestoreDialog = () => {
    setRestoreChoiceDialog({
      isOpen: false,
      tabName: "",
      archivedTab: null,
    });
  };

  // Helper functions
  const getCurrentTabValue = () => {
    if (activeTab) {
      return activeTab;
    }
    if (selectedProduct?.tabs && selectedProduct.tabs.length > 0) {
      const sortedTabs = selectedProduct.tabs.sort((a, b) => a.order - b.order);
      const firstTabId = sortedTabs[0]?.id || "no-tabs";
      return firstTabId;
    }
    return "no-tabs";
  };

  const getCurrentTabName = () => {
    if (!selectedProduct?.tabs || !activeTab) return null;
    const currentTab = selectedProduct.tabs.find((tab) => tab.id === activeTab);
    return currentTab?.tabName || null;
  };

  // Handle tab change with URL update
  const handleTabChange = (tabId: string) => {
    // If user is in edit mode, we need to handle unsaved changes
    if (isEditing) {
      if (hasUnsavedChanges) {
        // Show confirmation dialog for unsaved changes
        setPendingAction(() => () => {
          setIsEditing(false);
          setHasUnsavedChanges(false);
          setActiveTab(tabId);
          updateUrlWithTab(tabId);
        });
        setShowConfirmDialog(true);
        return;
      } else {
        // No unsaved changes, just reset edit mode
        setIsEditing(false);
        setHasUnsavedChanges(false);
      }
    }

    setActiveTab(tabId);
    updateUrlWithTab(tabId);
  };

  // Handler functions
  const handleEditToggle = async () => {
    if (isEditing) {
      // If we're in edit mode and there are unsaved changes, save them
      if (hasUnsavedChanges) {
        setIsSaving(true);
        try {
          await handleSaveCurrentTab();
        } finally {
          setIsSaving(false);
        }
      }
      setHasUnsavedChanges(false);
    }
    setIsEditing(!isEditing);
  };

  const handleCancelEdit = () => {
    if (hasUnsavedChanges) {
      setPendingAction(() => () => {
        setIsEditing(false);
        setHasUnsavedChanges(false);
      });
      setShowConfirmDialog(true);
    } else {
      setIsEditing(false);
      setHasUnsavedChanges(false);
    }
  };

  const handleConfirmAction = () => {
    setShowConfirmDialog(false);
    setHasUnsavedChanges(false);
    setIsEditing(false);
    pendingAction();
  };

  const handleCancelAction = () => {
    setShowConfirmDialog(false);
  };

  const handleNavigateToSettings = () => {
    if (hasUnsavedChanges) {
      setPendingAction(
        () => () => router.push(`/dashboard/npd/${slug}/settings`)
      );
      setShowConfirmDialog(true);
    } else {
      router.push(`/dashboard/npd/${slug}/settings`);
    }
  };

  const handleDirtyChange = useCallback(
    (isDirty: boolean) => {
      setHasUnsavedChanges(isDirty);
    },
    [setHasUnsavedChanges]
  );

  // Handle user mention clicks
  const handleUserMention = useCallback(
    (username: string) => {
      openChatWithMention(username);
    },
    [openChatWithMention]
  );

  // Save current tab data
  const handleSaveCurrentTab = async () => {
    if (!activeTab || !selectedProduct) return;

    const currentTab = selectedProduct.tabs.find((tab) => tab.id === activeTab);
    if (!currentTab) return;

    try {
      if (currentTab.tabName === "Overview" && overviewRef.current) {
        await overviewRef.current.save();
        // Force refresh the product data by clearing cache and refetching
        if (selectedProduct) {
          // Clear the product from cache to force fresh fetch
          clearProductCache();
          await selectProduct(selectedProduct.id);
        }
      } else if (currentTab.tabName === "1.1" && section11Ref.current) {
        await section11Ref.current.save();
        // Force refresh the product data by clearing cache and refetching
        if (selectedProduct) {
          // Clear the product from cache to force fresh fetch
          clearProductCache();
          await selectProduct(selectedProduct.id);
        }
      } else if (currentTab.tabName === "1.2" && section12Ref.current) {
        await section12Ref.current.save();
        // Force refresh the product data by clearing cache and refetching
        if (selectedProduct) {
          // Clear the product from cache to force fresh fetch
          clearProductCache();
          await selectProduct(selectedProduct.id);
        }
      } else if (currentTab.tabName === "1.3" && section13Ref.current) {
        await section13Ref.current.save();
        // Force refresh the product data by clearing cache and refetching
        if (selectedProduct) {
          // Clear the product from cache to force fresh fetch
          clearProductCache();
          await selectProduct(selectedProduct.id);
        }
      } else if (
        currentTab.tabName === "Sourcing Brief" &&
        sourcingBriefRef.current
      ) {
        await sourcingBriefRef.current.save();
        // Force refresh the product data by clearing cache and refetching
        if (selectedProduct) {
          // Clear the product from cache to force fresh fetch
          clearProductCache();
          await selectProduct(selectedProduct.id);
        }
      } else {
        // No save handler implemented for this tab type
      }
    } catch (error) {
      console.error(`Error saving ${currentTab.tabName} tab:`, error);
      throw error;
    }
  };

  const getEditButtonText = () => {
    const tabName = getCurrentTabName();
    if (tabName) {
      return isEditing ? `Save ${tabName}` : `Edit ${tabName}`;
    }
    return isEditing ? "Save" : "Edit";
  };

  // Error state
  if (selectedProductError) {
    return <ErrorState error={selectedProductError} />;
  }

  // Loading states
  if (!selectedProduct || isLoading || panelWidth === null) {
    return (
      <LoadingState
        message="Loading product..."
        description="Please wait while we fetch the product details"
      />
    );
  }

  // Empty state
  if (!selectedProduct) {
    return <EmptyState />;
  }

  // Show add tab page if one is selected
  if (showAddTab) {
    const AddTabComponent = TAB_COMPONENTS[showAddTab];
    return (
      <AddTabComponent
        productId={selectedProduct.id}
        productName={selectedProduct.name}
        onBack={handleCancelAddTab}
        onSave={handleSaveTab}
      />
    );
  }

  return (
    <div className="h-full w-full flex flex-col">
      {/* Product Header */}
      <ProductHeader
        product={selectedProduct}
        isEditing={isEditing}
        hasUnsavedChanges={hasUnsavedChanges}
        isSaving={isSaving}
        onEditToggle={handleEditToggle}
        onCancelEdit={handleCancelEdit}
        onNavigateToSettings={handleNavigateToSettings}
        getEditButtonText={getEditButtonText}
        hasTabsToEdit={selectedProduct.tabs && selectedProduct.tabs.length > 0}
      />

      {/* Main Content Area with Panels */}
      <div className="flex-1 min-h-0 flex flex-col bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <ResizablePanelGroup
          direction="horizontal"
          className="flex-1 min-h-0"
          onLayout={(sizes) => {
            if (openPanel && sizes.length === 2) {
              setPanelWidth(sizes[1]);
              localStorage.setItem("product_panel_width", sizes[1].toString());
            }
          }}
        >
          {/* Main Content Panel */}
          <ResizablePanel
            defaultSize={(() => {
              if (openPanel) {
                return 100 - (panelWidth || 30);
              }
              return 100;
            })()}
            className="h-full relative"
          >
            {/* Tab System */}
            <Tabs
              key={selectedProduct.id}
              value={getCurrentTabValue()}
              onValueChange={handleTabChange}
              className="flex-1 min-h-0 flex flex-col h-full"
            >
              {/* Tab Navigation */}
              <TabNavigation
                product={selectedProduct}
                activeTab={activeTab || ""}
                onTabChange={handleTabChange}
                onAddTab={handleAddTab}
                openPanel={openPanel}
                onOpenPanel={setOpenPanel}
              />

              {/* Tab Content */}
              <TabContentArea
                tabs={selectedProduct.tabs}
                isEditing={isEditing}
                onDirtyChange={handleDirtyChange}
                productName={selectedProduct.name}
                productId={selectedProduct.id}
                productSlug={selectedProduct.slug}
                productOwnerId={
                  (
                    selectedProduct as ProductWithTabs & {
                      user?: { id: string };
                    }
                  ).user?.id
                }
                overviewRef={overviewRef}
                section11Ref={section11Ref}
                section12Ref={section12Ref}
                section13Ref={section13Ref}
                sourcingBriefRef={sourcingBriefRef}
                onUserMention={handleUserMention}
              />
            </Tabs>
          </ResizablePanel>

          {/* Side Panel */}
          <SidePanel
            product={selectedProduct}
            openPanel={openPanel}
            onPanelChange={setOpenPanel}
            panelWidth={panelWidth || 30}
            minPanelPercentage={minPanelPercentage}
            maxPanelPercentage={maxPanelPercentage}
          />
        </ResizablePanelGroup>
      </div>

      {/* Navigation warning dialog for unsaved changes */}
      <NavigationWarningDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        onConfirm={handleConfirmAction}
        onCancel={handleCancelAction}
        variant="discard"
      />

      {/* Tab restore choice dialog */}
      <TabRestoreChoiceDialog
        isOpen={restoreChoiceDialog.isOpen}
        onClose={handleCloseRestoreDialog}
        tabName={restoreChoiceDialog.tabName}
        productName={selectedProduct?.name || ""}
        archivedAt={restoreChoiceDialog.archivedTab?.archivedAt}
        archivedBy={restoreChoiceDialog.archivedTab?.archivedByUser?.name}
        onRestoreOriginal={handleRestoreOriginal}
        onStartFresh={handleStartFresh}
      />
    </div>
  );
}
