"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

// Fetch all users for product subscription
export async function fetchAllUsers() {
  // Validate that user is authenticated
  await validateServerAction();

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        roles: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Filter out users who have SUPER_ADMIN role and ensure alphabetical sorting
    const filteredUsers = users
      .filter((user) => !user.roles.some((role) => role.name === "SUPER_ADMIN"))
      .sort((a, b) => a.name.localeCompare(b.name));

    return filteredUsers;
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
}

export interface UserProfileData {
  id: string;
  name: string;
  email: string;
  image: string | null;
  provider: string | null;
  lastLoginAt: Date | null;
  createdAt: Date;
  roles: Array<{
    id: string;
    name: string;
  }>;
  subscriptions: Array<{
    id: string;
    npdProduct: {
      id: string;
      name: string;
      slug: string;
      brand: string;
    };
    createdAt: Date;
  }>;
  recentActivity: Array<{
    id: string;
    action: string;
    description: string;
    createdAt: Date;
    npdProduct?: {
      name: string;
      slug: string;
    } | null;
    npdProductTab?: {
      tabName: string;
    } | null;
  }>;
  notifications: {
    total: number;
    unread: number;
    hidden: number;
  };
}

/**
 * Get comprehensive profile data for the current user
 */
export async function getUserProfile(): Promise<UserProfileData> {
  const user = await validateServerAction();

  try {
    // Fetch user with all related data
    const userProfile = await prisma.user.findUnique({
      where: { id: user.userId },
      include: {
        roles: {
          select: {
            id: true,
            name: true,
          },
        },
        npdProductSubscriptions: {
          include: {
            npdProduct: {
              select: {
                id: true,
                name: true,
                slug: true,
                brand: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        npdProductActivityLogs: {
          include: {
            npdProduct: {
              select: {
                name: true,
                slug: true,
              },
            },
            npdProductTab: {
              select: {
                tabName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10, // Get recent 10 activities
        },
      },
    });

    if (!userProfile) {
      throw new Error("User not found");
    }

    // Get notification counts
    const notificationCounts = await prisma.notification.aggregate({
      where: { userId: user.userId },
      _count: {
        id: true,
      },
    });

    const unreadCount = await prisma.notification.count({
      where: {
        userId: user.userId,
        read: false,
      },
    });

    const hiddenCount = await prisma.notification.count({
      where: {
        userId: user.userId,
        hidden: true,
      },
    });

    return {
      id: userProfile.id,
      name: userProfile.name,
      email: userProfile.email,
      image: userProfile.image,
      provider: userProfile.provider,
      lastLoginAt: userProfile.lastLoginAt,
      createdAt: userProfile.createdAt,
      roles: userProfile.roles,
      subscriptions: userProfile.npdProductSubscriptions,
      recentActivity: userProfile.npdProductActivityLogs,
      notifications: {
        total: notificationCounts._count.id,
        unread: unreadCount,
        hidden: hiddenCount,
      },
    };
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw error;
  }
}

/**
 * Update user profile name (if allowed by auth provider)
 */
export async function updateUserName(name: string) {
  const user = await validateServerAction();

  if (!name || name.trim().length < 2) {
    throw new Error("Name must be at least 2 characters long");
  }

  try {
    const updatedUser = await prisma.user.update({
      where: { id: user.userId },
      data: { name: name.trim() },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    return { success: true, user: updatedUser };
  } catch (error) {
    console.error("Error updating user name:", error);
    throw error;
  }
}

/**
 * Update last login timestamp
 */
export async function updateLastLogin() {
  const user = await validateServerAction();

  try {
    await prisma.user.update({
      where: { id: user.userId },
      data: { lastLoginAt: new Date() },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating last login:", error);
    throw error;
  }
}
