import type {
  User,
  Role,
  Permission,
  NPDProduct,
  NPDProductTab,
  NPDProductTabHistory,
  NPDProductSubscription,
} from "@/lib/generated/prisma";

import type { TabName } from "@/lib/constants/tab-order";
import type { ProductStage } from "@/lib/constants/product-stages";

// Context types
export * from "./context";

export type {
  User,
  Role,
  Permission,
  NPDProduct,
  NPDProductTab,
  NPDProductTabHistory,
  NPDProductSubscription,
  ProductStage,
  TabName,
};

// NPD-related types
export interface ProductWithTabs {
  id: string;
  name: string;
  brand: string;
  slug: string;
  stage: ProductStage;
  description?: string | null;
  imageUrl?: string | null;
  userId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  tabs: NPDProductTab[];
  subscriptions?: (NPDProductSubscription & { user: User })[];
  isSubscribed?: boolean; // Computed field for current user
}

export interface ProductSummary {
  id: string;
  name: string;
  brand: string;
  slug: string;
  stage: ProductStage;
  description?: string | null;
  imageUrl?: string | null;
  createdAt: Date;
  updatedAt: Date;
  isSubscribed?: boolean; // Computed field for current user
}

// User with permissions (common for auth)
export interface UserWithRoles extends User {
  roles: (Role & {
    permissions: Permission[];
  })[];
}

// API Response (for consistent server responses)
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
