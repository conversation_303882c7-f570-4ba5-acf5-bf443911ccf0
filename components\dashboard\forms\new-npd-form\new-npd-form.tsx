"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useNPDContext } from "@/context/npd-context";
import { useAuth } from "@/hooks/use-auth";
import { BRANDS } from "@/lib/constants/brands";
import { PRODUCT_STAGES } from "@/lib/constants/product-stages";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  PackageIcon,
  Package,
  Users,
  TrendingUp,
  FileText,
  Image as ImageIcon,
} from "lucide-react";
import { PageHeader } from "@/components/dashboard/shared";
import Spinner from "@/components/spinner";

// Import our shared form components
import {
  TextField,
  SelectField,
  StageSelectField,
  UserSelectionField,
} from "../shared/form-fields";
import {
  createProductSchema,
  validateForm,
  type CreateProductFormData,
  type FormErrors,
} from "../shared/form-validation";
import { useUsers } from "../shared/hooks/use-users";

export function NewNPDForm() {
  const router = useRouter();
  const { createNPD } = useNPDContext();
  const { users, isLoading: isLoadingUsers } = useUsers();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Form data state
  const [formData, setFormData] = useState<CreateProductFormData>({
    name: "",
    brand: "",
    stage: "Ideation",
    description: "",
    imageUrl: "",
    subscribedUserIds: [],
  });

  // Auto-select the current user when component mounts
  useEffect(() => {
    if (
      user &&
      user.userId &&
      formData.subscribedUserIds &&
      !formData.subscribedUserIds.includes(user.userId)
    ) {
      setFormData((prev) => ({
        ...prev,
        subscribedUserIds: [user.userId],
      }));
    }
  }, [user, formData.subscribedUserIds]);

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsSubmitting(true);

    // Validate form
    const validationErrors = validateForm(createProductSchema, formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors as FormErrors);
      setIsSubmitting(false);
      return;
    }

    setErrors({});

    try {
      const product = await createNPD({
        name: formData.name,
        brand: formData.brand,
        stage: formData.stage,
        description: formData.description || undefined,
        imageUrl: formData.imageUrl || undefined,
        subscribedUserIds: formData.subscribedUserIds || [],
      });

      toast.success(`NPD "${formData.name}" created successfully!`, {
        description: `Overview tab has been auto-created. Navigate to the NPD page to start adding data.`,
        duration: 4000,
      });

      // Set brand filter in localStorage before redirecting
      localStorage.setItem("npd_selected_brand", formData.brand);

      // Redirect to the new NPD page with Overview tab selected
      router.push(`/dashboard/npd/${product.slug}?tab=overview`);
    } catch (error) {
      console.error("Error creating NPD:", error);
      toast.error("Failed to create NPD. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    field: keyof CreateProductFormData,
    value: string | string[]
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const brandOptions = BRANDS.map((brand) => ({
    value: brand,
    label: brand,
  }));

  const stageOptions = PRODUCT_STAGES.map((stage) => ({
    value: stage.value,
    label: stage.label,
  }));

  return (
    <div className="h-full w-full flex flex-col">
      <PageHeader
        title="Add New Product"
        description="Add a new NPD product and start tracking its development"
        onBack={() => router.back()}
        actionButton={
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <Spinner loading={true} size={8} />
            ) : (
              <PackageIcon className="h-4 w-4" />
            )}
            {isSubmitting ? "Creating..." : "Create"}
          </Button>
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* NPD Info Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Information
              </CardTitle>
              <CardDescription>
                Fill in the basic details for your new NPD product. Required
                fields are marked with an asterisk (*).
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextField
                    label="Product Name"
                    value={formData.name}
                    onChange={(value) => handleInputChange("name", value)}
                    error={errors.name}
                    required
                    placeholder="Enter product name"
                  />

                  <SelectField
                    label="Brand"
                    value={formData.brand}
                    onChange={(value) => handleInputChange("brand", value)}
                    error={errors.brand}
                    required
                    placeholder="Select a brand"
                    options={brandOptions}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <StageSelectField
                    label="Development Stage"
                    value={formData.stage}
                    onChange={(value) => handleInputChange("stage", value)}
                    error={errors.stage}
                    required
                    placeholder="Select development stage"
                    options={stageOptions}
                  />

                  <TextField
                    label="Product Image"
                    value={formData.imageUrl}
                    onChange={(value) => handleInputChange("imageUrl", value)}
                    error={errors.imageUrl}
                    type="url"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>

                <TextField
                  label="Description"
                  value={formData.description}
                  onChange={(value) => handleInputChange("description", value)}
                  error={errors.description}
                  placeholder="One line description of the product"
                />

                <UserSelectionField
                  label="Subscribe Users (Optional)"
                  selectedUserIds={formData.subscribedUserIds || []}
                  onChange={(userIds) =>
                    handleInputChange("subscribedUserIds", userIds)
                  }
                  users={users}
                  isLoading={isLoadingUsers}
                  error={errors.subscribedUserIds}
                />
              </form>
            </CardContent>
          </Card>

          {/* Preview Card - Following add tab pattern */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your new NPD product will include these features that you can
                customize and expand after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium">Progress Tracking</h3>
                    <p className="text-sm text-muted-foreground">
                      Monitor development stages and milestones
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <FileText className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium">NPD Tabs</h3>
                    <p className="text-sm text-muted-foreground">
                      Add overview, sourcing, and section tabs
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <Users className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-medium">Team Collaboration</h3>
                    <p className="text-sm text-muted-foreground">
                      Chat, notifications, and activity tracking
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <ImageIcon className="h-8 w-8 text-orange-600" />
                  <div>
                    <h3 className="font-medium">Media Management</h3>
                    <p className="text-sm text-muted-foreground">
                      Upload and organize product images
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Product Dashboard</Badge>
                  <span className="text-sm text-muted-foreground">
                    Centralized view of all product information
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Activity Log</Badge>
                  <span className="text-sm text-muted-foreground">
                    Track all changes and team interactions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Real-time Chat</Badge>
                  <span className="text-sm text-muted-foreground">
                    Communicate with your team about this product
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
