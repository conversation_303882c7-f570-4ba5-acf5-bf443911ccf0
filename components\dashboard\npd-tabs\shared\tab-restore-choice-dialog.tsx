"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { RotateCcw, PlusCircle, AlertTriangle } from "lucide-react";

interface TabRestoreChoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tabName: string;
  productName: string;
  archivedAt?: Date | null;
  archivedBy?: string | null;
  onRestoreOriginal: () => void;
  onStartFresh: () => void;
}

export function TabRestoreChoiceDialog({
  isOpen,
  onClose,
  tabName,
  productName,
  archivedAt,
  archivedBy,
  onRestoreOriginal,
  onStartFresh,
}: TabRestoreChoiceDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5 text-blue-500" />
            Archived Tab Found
          </AlertDialogTitle>
          <AlertDialogDescription>
            A <strong>&quot;{tabName}&quot;</strong> tab for{" "}
            <strong>&quot;{productName}&quot;</strong> was previously archived
            {archivedAt && (
              <span className="text-muted-foreground">
                {" "}
                on {new Date(archivedAt).toLocaleDateString()}
              </span>
            )}
            {archivedBy && (
              <span className="text-muted-foreground"> by {archivedBy}</span>
            )}
            . How would you like to proceed?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-3">
          {/* Restore Original Option */}
          <div className="border rounded-lg p-4 space-y-2">
            <div className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Restore Original Tab</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Restore the archived tab with all its original data, fields, and
              content intact.
            </p>
            <div className="text-xs text-green-600 dark:text-green-400">
              ✅ All your previous work will be preserved
            </div>
          </div>

          {/* Start Fresh Option */}
          <div className="border rounded-lg p-4 space-y-2">
            <div className="flex items-center gap-2">
              <PlusCircle className="h-4 w-4 text-green-500" />
              <span className="font-medium">Start Fresh</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Create a new tab with clean, empty fields and default values.
            </p>
            <div className="flex items-center gap-1 text-xs text-amber-600 dark:text-amber-400">
              <AlertTriangle className="h-3 w-3" />
              The archived tab data will be permanently lost
            </div>
          </div>
        </div>

        <AlertDialogFooter className="flex-col sm:flex-row gap-2">
          <AlertDialogCancel onClick={onClose}>Cancel</AlertDialogCancel>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                onRestoreOriginal();
                onClose();
              }}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Restore Original
            </Button>

            <AlertDialogAction
              onClick={() => {
                onStartFresh();
                onClose();
              }}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              <PlusCircle className="h-4 w-4" />
              Start Fresh
            </AlertDialogAction>
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
