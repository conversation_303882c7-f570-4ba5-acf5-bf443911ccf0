import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

// PUT /api/admin/permissions/[id] - Update permission
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await validateServerAction();
    const { id } = await params;
    
    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(role => role.name === "SUPER_ADMIN");
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    const { code } = await request.json();

    if (!code || typeof code !== "string") {
      return NextResponse.json(
        { message: "Permission code is required" },
        { status: 400 }
      );
    }

    // Validate permission code format
    const permissionCode = code.trim().toUpperCase();
    if (!/^[A-Z_]+$/.test(permissionCode)) {
      return NextResponse.json(
        { message: "Permission code must contain only uppercase letters and underscores" },
        { status: 400 }
      );
    }

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id },
    });

    if (!existingPermission) {
      return NextResponse.json(
        { message: "Permission not found" },
        { status: 404 }
      );
    }

    // Check if another permission with the same code exists
    const duplicatePermission = await prisma.permission.findFirst({
      where: {
        code: permissionCode,
        id: { not: id },
      },
    });

    if (duplicatePermission) {
      return NextResponse.json(
        { message: "Permission with this code already exists" },
        { status: 409 }
      );
    }

    const permission = await prisma.permission.update({
      where: { id },
      data: { code: permissionCode },
    });

    return NextResponse.json(permission);
  } catch (error) {
    console.error("Error updating permission:", error);
    return NextResponse.json(
      { message: "Failed to update permission" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/permissions/[id] - Delete permission
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await validateServerAction();
    const { id } = await params;
    
    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(role => role.name === "SUPER_ADMIN");
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id },
      include: {
        roles: true,
      },
    });

    if (!existingPermission) {
      return NextResponse.json(
        { message: "Permission not found" },
        { status: 404 }
      );
    }

    // Check if permission is being used by any roles
    if (existingPermission.roles.length > 0) {
      return NextResponse.json(
        { 
          message: `Cannot delete permission. It is currently assigned to ${existingPermission.roles.length} role(s).`,
          roles: existingPermission.roles.map(role => role.name)
        },
        { status: 409 }
      );
    }

    await prisma.permission.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Permission deleted successfully" });
  } catch (error) {
    console.error("Error deleting permission:", error);
    return NextResponse.json(
      { message: "Failed to delete permission" },
      { status: 500 }
    );
  }
}
