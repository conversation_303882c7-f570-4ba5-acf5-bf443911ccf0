"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tab<PERSON>eader, LoadingButton } from "../shared";
import { Package, BarChart3, Users } from "lucide-react";
import { getTabOrder } from "@/lib/constants/tab-order";
import { toast } from "sonner";

interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

interface AddSection11TabProps {
  productName: string;
  onBack: () => void;
  onSave: (tabData: TabData) => Promise<{ _isRestored?: boolean } | void>;
}

export function AddSection11Tab({
  productName,
  onBack,
  onSave,
}: AddSection11TabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsLoading(true);

    try {
      await onSave({
        tabName: "1.1",
        order: getTabOrder("1.1"),
        fields: {
          features: "",
          color: "",
          size: "",
          pricerange: "",
          competitors: [],
          user: "", // Will be set by the server
          editedAt: new Date().toISOString(),
          editedBy: "", // Will be set by the server
          isEdited: false,
          versions: [],
        },
      });

      // Success toast will be shown after page reload via URL parameters
      // No need to show toast here since page will reload immediately
    } catch (error) {
      // Show error toast
      toast.error("Failed to add Section 1.1", {
        description:
          error instanceof Error ? error.message : "Please try again",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <TabHeader
        title="Add 1.1 Tab"
        description="Create a competitor analysis tab for {productName}"
        productName={productName}
        onBack={onBack}
        actionButton={
          <LoadingButton
            isLoading={isLoading}
            onClick={handleSubmit}
            loadingText="Creating..."
            defaultText="Create Section 1.1"
          />
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Structure Overview */}
          <Card>
            <CardHeader>
              <CardTitle>1.1 Structure</CardTitle>
              <CardDescription>
                This will create a comprehensive competitor analysis tab with
                the following sections ready for your content.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Product Information */}
                <div className="flex items-start gap-3 p-4 border rounded-lg">
                  <Package className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h3 className="font-medium">Product Information</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Basic product characteristics including features, color,
                      size, and price range
                    </p>
                  </div>
                </div>

                {/* Competitor Analysis */}
                <div className="flex items-start gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h3 className="font-medium">Competitor Analysis</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Comprehensive competitor data table with pricing, ratings,
                      and market analysis
                    </p>
                  </div>
                </div>

                {/* Market Research */}
                <div className="flex items-start gap-3 p-4 border rounded-lg md:col-span-2">
                  <Users className="h-5 w-5 text-purple-500 mt-0.5" />
                  <div>
                    <h3 className="font-medium">Market Research & Insights</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Detailed competitor comparison with ASIN data, sales
                      metrics, BSR rankings, and fulfillment analysis
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What you'll get */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your section 1.1 tab will include these features that you can
                edit and customize after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Product Information</Badge>
                  <span className="text-sm text-muted-foreground">
                    Features, color, size, and price range fields
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Competitor Analysis Table</Badge>
                  <span className="text-sm text-muted-foreground">
                    Comprehensive data table with pricing and ratings
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Market Research</Badge>
                  <span className="text-sm text-muted-foreground">
                    ASIN data, sales metrics, and BSR rankings
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Fulfillment Analysis</Badge>
                  <span className="text-sm text-muted-foreground">
                    Seller information and fulfillment methods
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Export & Filtering</Badge>
                  <span className="text-sm text-muted-foreground">
                    Column visibility controls and data export options
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
