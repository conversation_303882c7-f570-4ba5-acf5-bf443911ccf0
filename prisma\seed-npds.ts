import { PrismaClient } from "../lib/generated/prisma";

const prisma = new PrismaClient();

/**
 * Seeds the database with sample NPD projects and their tabs
 * This is separate from the main seed to allow optional NPD data seeding
 */
async function seedNPDs() {
  console.log("🌱 Starting NPD data seeding...");

  // Create NPD Projects
  await prisma.nPDProduct.upsert({
    where: { slug: "back-ice-pack" },
    update: {},
    create: {
      name: "Back Ice Pack",
      slug: "back-ice-pack",
      brand: "Perfect Remedy",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "disposable-face-towel" },
    update: {},
    create: {
      name: "Disposable Face Towel",
      slug: "disposable-face-towel",
      brand: "grace & stella",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "hypochlorous-acid-spray" },
    update: {},
    create: {
      name: "Hypochlorous Acid Spray",
      slug: "hypochlorous-acid-spray",
      brand: "grace & stella",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "face-moisturizer" },
    update: {},
    create: {
      name: "Face Moisturizer",
      slug: "face-moisturizer",
      brand: "grace & stella",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "facial-puff" },
    update: {},
    create: {
      name: "Facial Puff",
      slug: "facial-puff",
      brand: "grace & stella",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "face-tape" },
    update: {},
    create: {
      name: "Face Tape",
      slug: "face-tape",
      brand: "grace & stella",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "heartleaf-cleanser" },
    update: {},
    create: {
      name: "Heartleaf Cleanser",
      slug: "heartleaf-cleanser",
      brand: "LeGushe",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "bio-collagen-face-mask" },
    update: {},
    create: {
      name: "Bio-collagen Face Mask",
      slug: "bio-collagen-face-mask",
      brand: "LeGushe",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "rice-powder-wash" },
    update: {},
    create: {
      name: "Rice Powder Wash",
      slug: "rice-powder-wash",
      brand: "LeGushe",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "snail-mucin-eye-cream" },
    update: {},
    create: {
      name: "Snail Mucin Eye Cream",
      slug: "snail-mucin-eye-cream",
      brand: "Baebody",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "wound-closure-strips" },
    update: {},
    create: {
      name: "Wound Closure Strips",
      slug: "wound-closure-strips",
      brand: "Protect Life",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "camping-survival-kit" },
    update: {},
    create: {
      name: "Camping Survival Kit",
      slug: "camping-survival-kit",
      brand: "Protect Life",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "toothpaste-tablets" },
    update: {},
    create: {
      name: "Toothpaste Tablets",
      slug: "toothpaste-tablets",
      brand: "Venus Visage",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "retainer-cleaner-tablets" },
    update: {},
    create: {
      name: "Retainer Cleaner Tablets",
      slug: "retainer-cleaner-tablets",
      brand: "Venus Visage",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "hydroxyapatite-chewing-gum" },
    update: {},
    create: {
      name: "Hydroxyapatite Chewing Gum",
      slug: "hydroxyapatite-chewing-gum",
      brand: "Venus Visage",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "ankle-ice-pack-wrap" },
    update: {},
    create: {
      name: "Ankle Ice Pack Wrap",
      slug: "ankle-ice-pack-wrap",
      brand: "Perfect Remedy",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "back-ice-pack-wrap" },
    update: {},
    create: {
      name: "Back Ice Pack Wrap",
      slug: "back-ice-pack-wrap",
      brand: "Perfect Remedy",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "plantar-fasciitis-night-splint" },
    update: {},
    create: {
      name: "Plantar Fasciitis Night Splint",
      slug: "plantar-fasciitis-night-splint",
      brand: "Ballotte",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "plantar-fasciitis-insoles" },
    update: {},
    create: {
      name: "Plantar Fasciitis Insoles",
      slug: "plantar-fasciitis-insoles",
      brand: "Ballotte",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "gel-heel-cups" },
    update: {},
    create: {
      name: "Gel Heel Cups",
      slug: "gel-heel-cups",
      brand: "Ballotte",
      stage: "Ideation",
      userId: null,
    },
  });

  // Add more products
  await prisma.nPDProduct.upsert({
    where: { slug: "toenail-clippers-for-seniors" },
    update: {},
    create: {
      name: "Toenail Clippers For Seniors",
      slug: "toenail-clippers-for-seniors",
      brand: "EZPIK",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "fidget-pen" },
    update: {},
    create: {
      name: "Fidget Pen",
      slug: "fidget-pen",
      brand: "Pilpoc",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "ferrite-putty" },
    update: {},
    create: {
      name: "Ferrite Putty",
      slug: "ferrite-putty",
      brand: "Pilpoc",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "fiber-gummies" },
    update: {},
    create: {
      name: "Fiber Gummies",
      slug: "fiber-gummies",
      brand: "Dr. Moritz",
      stage: "Ideation",
      userId: null,
    },
  });

  await prisma.nPDProduct.upsert({
    where: { slug: "lotion-applicator" },
    update: {},
    create: {
      name: "Lotion Applicator",
      slug: "lotion-applicator",
      brand: "EZPIK",
      stage: "Ideation",
      userId: null,
    },
  });

  console.log("✅ Created sample NPD projects");
  console.log("🎉 NPD data seeding completed successfully!");
}

// Export the function for use in other scripts
export { seedNPDs };

// Run if called directly
if (require.main === module) {
  seedNPDs()
    .catch((e) => {
      console.error("❌ Error during NPD seeding:", e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
