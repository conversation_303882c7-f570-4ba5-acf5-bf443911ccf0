"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";
import {
  getChatUnreadCount,
  updateChatReadStatus,
  markAllChatMessagesAsRead,
  type ChatUnreadData,
} from "@/actions/chat.actions";

// Remove duplicate interface since it's imported from actions

export function useChatUnread(productId: string) {
  const [data, setData] = useState<ChatUnreadData>({
    unreadCount: 0,
    totalCount: 0,
    readCount: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Refs for polling
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch unread count using Server Action
  const fetchUnreadCount = useCallback(async () => {
    if (!productId) {
      setData({ unreadCount: 0, totalCount: 0, readCount: 0 });
      setIsLoading(false);
      return;
    }

    try {
      setError(null);

      const result = await getChatUnreadCount(productId);
      setData(result);
    } catch (error) {
      console.error("Error fetching chat unread count:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(`Failed to fetch unread count: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  }, [productId]);

  // Mark message as read using Server Action
  const markAsRead = useCallback(
    async (messageId: string) => {
      if (!productId || !messageId) return false;

      try {
        await updateChatReadStatus(messageId, productId, true);
        // Refresh unread count
        await fetchUnreadCount();
        return true;
      } catch (error) {
        console.error("Error marking message as read:", error);
        return false;
      }
    },
    [productId, fetchUnreadCount]
  );

  // Mark message as unread using Server Action
  const markAsUnread = useCallback(
    async (messageId: string) => {
      if (!productId || !messageId) return false;

      try {
        await updateChatReadStatus(messageId, productId, false);
        // Refresh unread count
        await fetchUnreadCount();
        return true;
      } catch (error) {
        console.error("Error marking message as unread:", error);
        return false;
      }
    },
    [productId, fetchUnreadCount]
  );

  // Mark all messages as read using Server Action
  const markAllAsRead = useCallback(async () => {
    if (!productId) return false;

    try {
      await markAllChatMessagesAsRead(productId);
      // Refresh unread count
      await fetchUnreadCount();
      return true;
    } catch (error) {
      console.error("Error marking all messages as read:", error);
      return false;
    }
  }, [productId, fetchUnreadCount]);

  // Setup polling for unread count updates
  useEffect(() => {
    if (!productId || !isPollingEnabled("NOTE_READ_STATUS")) return;

    const interval = getPollingInterval("NOTE_READ_STATUS");

    const poll = () => {
      fetchUnreadCount();
    };

    pollTimeoutRef.current = setTimeout(function pollInterval() {
      poll();
      pollTimeoutRef.current = setTimeout(pollInterval, interval);
    }, interval);

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, [productId, fetchUnreadCount]);

  // Initial fetch
  useEffect(() => {
    fetchUnreadCount();
  }, [fetchUnreadCount]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, []);

  return {
    unreadCount: data.unreadCount,
    totalCount: data.totalCount,
    readCount: data.readCount,
    isLoading,
    error,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    refetch: fetchUnreadCount,
  };
}
