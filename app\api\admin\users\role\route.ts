import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";
import { notifyUserRoleChange } from "@/lib/auth/notifications";

export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { userId, roleId } = await request.json();

    if (!userId || !roleId) {
      return NextResponse.json(
        { error: "User ID and Role ID are required" },
        { status: 400 }
      );
    }

    // Check if the role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent non-super admins from assigning super admin role
    if (
      role.name === "SUPER_ADMIN" &&
      !currentUser.roles.includes("SUPER_ADMIN")
    ) {
      return NextResponse.json(
        { error: "Only super admins can assign super admin role" },
        { status: 403 }
      );
    }

    // Remove existing roles and assign the new one
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roles: {
          set: [{ id: roleId }],
        },
      },
      include: { roles: true },
    });

    // Notify the user about the role change
    const currentRoles = updatedUser.roles.map((role) => role.name);
    await notifyUserRoleChange(userId, currentRoles);

    return NextResponse.json({
      success: true,
      message: "User role updated successfully",
    });
  } catch (error) {
    console.error("Error updating user role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { roles: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent removing super admin role unless current user is super admin
    const hasSuperAdminRole = user.roles.some(
      (role) => role.name === "SUPER_ADMIN"
    );
    if (hasSuperAdminRole && !currentUser.roles.includes("SUPER_ADMIN")) {
      return NextResponse.json(
        { error: "Only super admins can remove super admin role" },
        { status: 403 }
      );
    }

    // Remove all roles from the user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roles: {
          set: [],
        },
      },
      include: { roles: true },
    });

    // Notify the user about the role change
    const currentRoles = updatedUser.roles.map((role) => role.name);
    await notifyUserRoleChange(userId, currentRoles);

    return NextResponse.json({
      success: true,
      message: "User roles removed successfully",
    });
  } catch (error) {
    console.error("Error removing user roles:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
