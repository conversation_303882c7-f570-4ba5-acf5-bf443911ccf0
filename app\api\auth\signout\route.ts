import { NextResponse } from "next/server";
import { remove<PERSON>uth<PERSON><PERSON><PERSON> } from "@/lib/auth";
import { isProduction } from "@/lib/constants";

export async function POST() {
  try {
    // Remove the auth cookie using server-side method
    await removeAuthCookie();

    const response = NextResponse.json({ success: true });

    // Vercel-optimized cookie clearing - use exact attributes as when setting
    response.cookies.set("quickt-auth-token", "", {
      httpOnly: true,
      secure: isProduction,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
      maxAge: 0,
    });

    // Also clear without httpOnly (fallback for any client-side cookies)
    response.cookies.set("quickt-auth-token", "", {
      secure: isProduction,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
      maxAge: 0,
    });

    // Prevent caching of sign-out response
    response.headers.set(
      "Cache-Control",
      "no-cache, no-store, must-revalidate"
    );
    response.headers.set("Pragma", "no-cache");

    return response;
  } catch (error) {
    console.error("Sign out error:", error);
    return NextResponse.json({ error: "Failed to sign out" }, { status: 500 });
  }
}
