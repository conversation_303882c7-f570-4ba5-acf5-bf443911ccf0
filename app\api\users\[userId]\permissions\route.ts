import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const { userId } = await context.params;

    // Users can only get their own permissions unless they're admin
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (userId !== currentUser.userId && !hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get user with roles and permissions
    const userWithPermissions = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            permissions: true,
          },
        },
      },
    });

    if (!userWithPermissions) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // If user has no roles, return empty permissions (don't error)
    if (!userWithPermissions.roles || userWithPermissions.roles.length === 0) {
      return NextResponse.json({
        permissions: [],
        roles: [],
        user: {
          id: userWithPermissions.id,
          name: userWithPermissions.name,
          email: userWithPermissions.email,
        },
      });
    }

    // Flatten all permissions from all user's roles
    const allPermissions = userWithPermissions.roles.flatMap((role) =>
      role.permissions.map((permission) => permission.code)
    );

    // Remove duplicates
    const uniquePermissions = [...new Set(allPermissions)];

    return NextResponse.json({
      permissions: uniquePermissions,
      roles: userWithPermissions.roles.map((role) => role.name),
      user: {
        id: userWithPermissions.id,
        name: userWithPermissions.name,
        email: userWithPermissions.email,
      },
    });
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
