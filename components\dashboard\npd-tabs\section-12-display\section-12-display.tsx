"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useImperativeHandle,
  forwardRef,
  useMemo,
  useRef,
} from "react";
import { updateTabData } from "@/actions/npd.actions";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import { TabDangerZone } from "../shared/tab-danger-zone";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BarChart3, Plus, Trash2 } from "lucide-react";
import {
  useReactTable,
  getCoreRowModel,
  ColumnResizeMode,
  getSortedRowModel,
  type SortingState,
  type CellContext,
} from "@tanstack/react-table";
import {
  loadColumnSettings,
  updateColumnWidths,
  updateSortingState,
  updateColumnVisibility,
  resetColumnVisibility,
  resetColumnWidths,
  type ColumnSettings,
} from "./utils/columnSettings";
import { ColumnToggle } from "./components/ColumnToggle";
import {
  TABLE_STYLES,
  TabsTableContainer,
  TabsTableHeader,
  TabsTableBody,
} from "../shared";

// Data structure for pricing scenario row
interface PricingScenario {
  date: string; // Store individual date for each record
  priceScenario: string;
  priceUsd: string;
  estRefundRate: string;
  referalFeePercent: string;
  fbaFeeUsd: string;
  landedCostPercent: string;
  tacosPercent: string;
  commentary: string;
  isVisible?: boolean;
}

// Type for table row data
type TableRow = PricingScenario & { index: number };

// Data structure for Section 1.2
interface Section12Data {
  pricingScenarios: PricingScenario[];
  user?: string | null;
  editedAt?: string;
  editedBy?: string | null;
  isEdited?: boolean;
  versions?: unknown[];
}

interface Section12DisplayProps {
  tabId: string;
  initialData: Section12Data;
  isEditing: boolean;
  onDirtyChange: (isDirty: boolean) => void;
  onUserMention?: (username: string) => void;
  productId: string;
  productName: string;
  productSlug: string;
  tabName?: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
}

export interface Section12DisplayRef {
  save: () => Promise<void>;
}

export const Section12Display = forwardRef<
  Section12DisplayRef,
  Section12DisplayProps
>(function Section12Display(
  {
    tabId,
    initialData,
    isEditing,
    onDirtyChange,
    productId,
    productName,
    productSlug,
    tabName,
    tabCreatorId,
    productOwnerId,
  },
  ref
) {
  const { user } = useAuth();
  const [data, setData] = useState<Section12Data>(initialData);
  const [pricingData, setPricingData] = useState<PricingScenario[]>(
    initialData.pricingScenarios || []
  );
  const [modifiedFields, setModifiedFields] = useState<Set<string>>(new Set());
  const [copiedCell, setCopiedCell] = useState<string | null>(null);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Column settings state - initialize from localStorage
  const [columnSettings, setColumnSettings] = useState<ColumnSettings>(() => {
    return loadColumnSettings();
  });

  // Table sorting state - initialize from localStorage
  const [sorting, setSorting] = useState<SortingState>(() => {
    const savedSettings = loadColumnSettings();
    return savedSettings.sorting;
  });

  // Track if data has been modified
  const isDirty = modifiedFields.size > 0;

  // Handle column width changes
  const handleColumnResize = useCallback(
    (updaterOrValue: unknown) => {
      const newSizing =
        typeof updaterOrValue === "function"
          ? updaterOrValue(columnSettings.widths)
          : updaterOrValue;
      updateColumnWidths(newSizing as Record<string, number>);
      setColumnSettings((prev) => ({
        ...prev,
        widths: newSizing as Record<string, number>,
      }));
    },
    [columnSettings.widths]
  );

  // Handle sorting changes
  const handleSortingChange = useCallback(
    (updaterOrValue: unknown) => {
      const newSorting =
        typeof updaterOrValue === "function"
          ? updaterOrValue(sorting)
          : updaterOrValue;
      setSorting(newSorting as SortingState);
      updateSortingState(newSorting as SortingState);
    },
    [sorting]
  );

  // Handle column visibility changes
  const handleVisibilityChange = useCallback(
    (visibility: Record<string, boolean>) => {
      updateColumnVisibility(visibility);
      setColumnSettings((prev) => ({ ...prev, visibility }));
    },
    []
  );

  // Handle reset visibility to defaults
  const handleResetVisibility = useCallback(() => {
    const defaultVisibility = resetColumnVisibility();
    setColumnSettings((prev) => ({
      ...prev,
      visibility: defaultVisibility,
    }));
  }, []);

  // Handle reset widths to defaults
  const handleResetWidths = useCallback(() => {
    const defaultWidths = resetColumnWidths();
    setColumnSettings((prev) => ({
      ...prev,
      widths: defaultWidths,
    }));
  }, []);

  // Notify parent of dirty state changes
  useEffect(() => {
    onDirtyChange(isDirty);
  }, [isDirty, onDirtyChange]);

  // Handle pricing scenario changes
  const handlePricingChange = useCallback(
    (rowIndex: number, field: keyof PricingScenario, value: string) => {
      setPricingData((prev) => {
        const updated = [...prev];
        // Update the field value and also update the date to today when any field is changed
        updated[rowIndex] = {
          ...updated[rowIndex],
          [field]: value,
          // Only update date if it's not the date field itself being changed
          ...(field !== "date" && { date: new Date().toLocaleDateString() }),
        };
        return updated;
      });
      setModifiedFields((prev) =>
        new Set(prev).add(`pricing-${rowIndex}-${field}`)
      );
    },
    []
  );

  // Helper function to check if a field is modified
  const isFieldModified = useCallback(
    (rowIndex: number, field: string): boolean => {
      return modifiedFields.has(`pricing-${rowIndex}-${field}`);
    },
    [modifiedFields]
  );

  // Handle adding a new pricing scenario row
  const handleAddPricingScenario = useCallback(() => {
    const newScenario: PricingScenario = {
      date: new Date().toLocaleDateString(), // Set today's date for new records
      priceScenario: "",
      priceUsd: "",
      estRefundRate: "",
      referalFeePercent: "",
      fbaFeeUsd: "",
      landedCostPercent: "",
      tacosPercent: "",
      commentary: "",
      isVisible: true,
    };

    setPricingData((prev) => [...prev, newScenario]);
    setModifiedFields((prev) => new Set(prev).add("pricingScenarios"));
  }, []);

  // Handle deleting a pricing scenario
  const handleDeletePricingScenario = useCallback((rowIndex: number) => {
    setPricingData((prev) => prev.filter((_, index) => index !== rowIndex));
    setModifiedFields((prev) => new Set(prev).add("pricingScenarios"));
  }, []);

  // Handle copy functionality
  const handleCopy = useCallback(async (text: string, cellId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCell(cellId);
      setTimeout(() => setCopiedCell(null), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  }, []);

  // Save function
  const save = useCallback(async () => {
    if (!isDirty) return;

    try {
      const updatedData = {
        ...data,
        pricingScenarios: pricingData,
        editedAt: new Date().toISOString(),
        editedBy: user?.name || null,
        isEdited: true,
      };

      await updateTabData(tabId, updatedData);
      setData(updatedData);
      setModifiedFields(new Set());
      toast.success("Pricing scenarios saved successfully");
    } catch (error) {
      console.error("Error saving pricing scenarios:", error);
      toast.error("Failed to save pricing scenarios");
    }
  }, [tabId, data, pricingData, isDirty, user?.name]);

  // Expose save function to parent
  useImperativeHandle(ref, () => ({ save }), [save]);

  // Helper functions for formatting
  const formatCurrency = useCallback((value: string | number): string => {
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "$0.00";
    return "$" + num.toFixed(2);
  }, []);

  const formatPercentage = useCallback((value: string | number): string => {
    const num = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(num)) return "0%";
    return num.toFixed(2) + "%";
  }, []);

  // Helper function to check if a price scenario is already used
  const isPriceScenarioUsed = useCallback(
    (scenario: string, currentRowIndex: number): boolean => {
      if (!scenario || scenario.trim() === "") return false;
      return pricingData.some(
        (row, index) =>
          index !== currentRowIndex &&
          row.priceScenario &&
          row.priceScenario.toLowerCase().trim() ===
            scenario.toLowerCase().trim()
      );
    },
    [pricingData]
  );

  // Helper functions for calculations
  const calculateFbaFeePercent = useCallback(
    (fbaFeeUsd: string, priceUsd: string): string => {
      const fbaFee = parseFloat(fbaFeeUsd) || 0;
      const price = parseFloat(priceUsd) || 0;
      if (price === 0) return "0%";
      return ((fbaFee / price) * 100).toFixed(2) + "%";
    },
    []
  );

  const calculateLandedCostUsd = useCallback(
    (landedCostPercent: string, priceUsd: string): string => {
      const landedCostPct = parseFloat(landedCostPercent) || 0;
      const price = parseFloat(priceUsd) || 0;
      return "$" + ((landedCostPct / 100) * price).toFixed(2);
    },
    []
  );

  const calculateNetProfitPercent = useCallback(
    (
      priceUsd: string,
      referalFeePercent: string,
      fbaFeeUsd: string,
      landedCostPercent: string,
      tacosPercent: string
    ): string => {
      const price = parseFloat(priceUsd) || 0;
      const referalFee = (parseFloat(referalFeePercent) || 0) / 100;
      const fbaFee = parseFloat(fbaFeeUsd) || 0;
      const landedCost = (parseFloat(landedCostPercent) || 0) / 100;
      const tacos = (parseFloat(tacosPercent) || 0) / 100;

      if (price === 0) return "0%";

      const totalCosts =
        referalFee * price + fbaFee + landedCost * price + tacos * price;
      const netProfit = price - totalCosts;
      const netProfitPercent = (netProfit / price) * 100;

      return netProfitPercent.toFixed(2) + "%";
    },
    []
  );

  const calculateNetProfitUsd = useCallback(
    (
      priceUsd: string,
      referalFeePercent: string,
      fbaFeeUsd: string,
      landedCostPercent: string,
      tacosPercent: string
    ): string => {
      const price = parseFloat(priceUsd) || 0;
      const referalFee = (parseFloat(referalFeePercent) || 0) / 100;
      const fbaFee = parseFloat(fbaFeeUsd) || 0;
      const landedCost = (parseFloat(landedCostPercent) || 0) / 100;
      const tacos = (parseFloat(tacosPercent) || 0) / 100;

      const totalCosts =
        referalFee * price + fbaFee + landedCost * price + tacos * price;
      const netProfit = price - totalCosts;

      return "$" + netProfit.toFixed(2);
    },
    []
  );

  // Create table data with index
  const tableData = useMemo(() => {
    return pricingData.map((scenario, index) => ({
      ...scenario,
      index,
    }));
  }, [pricingData]);

  // Create table columns
  const columns = useMemo(() => {
    const widths = columnSettings.widths;
    return [
      {
        accessorKey: "date",
        header: () => <div className="text-center">Date</div>,
        size: widths.date,
        enableSorting: true,
        enableResizing: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `date-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              <div
                className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all text-muted-foreground"
                onClick={() => handleCopy(value, cellId)}
                title={value}
                style={{ fontSize: "12px" }}
              >
                <div className="relative">
                  {value || "-"}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "priceScenario",
        header: () => <div className="text-center">Price Scenario</div>,
        size: widths.priceScenario,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `priceScenario-${context.row.index}`;
          const predefinedOptions = [
            "Breakeven",
            "Lowest",
            "Highest",
            "Suggested",
          ];
          const isCustomValue = !predefinedOptions.includes(value);
          const isDuplicate = isPriceScenarioUsed(value, context.row.index);

          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <div className="flex flex-col gap-1">
                  <Select
                    value={isCustomValue ? "custom" : value}
                    onValueChange={(newValue) => {
                      if (newValue === "custom") {
                        // Clear the value so user can type custom
                        handlePricingChange(
                          context.row.index,
                          "priceScenario",
                          ""
                        );
                        return;
                      }
                      // Check if the selected value is already used
                      if (isPriceScenarioUsed(newValue, context.row.index)) {
                        // Don't allow selection of duplicate values
                        return;
                      }
                      handlePricingChange(
                        context.row.index,
                        "priceScenario",
                        newValue
                      );
                    }}
                  >
                    <SelectTrigger
                      className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300 ${
                        isDuplicate ? "border-red-300 bg-red-50" : ""
                      }`}
                    >
                      <SelectValue placeholder="Select or type custom" />
                    </SelectTrigger>
                    <SelectContent>
                      {predefinedOptions.map((option) => {
                        const isUsed = isPriceScenarioUsed(
                          option,
                          context.row.index
                        );
                        return (
                          <SelectItem
                            key={option}
                            value={option}
                            disabled={isUsed}
                            className={
                              isUsed ? "opacity-50 cursor-not-allowed" : ""
                            }
                          >
                            {option} {isUsed ? "(Used)" : ""}
                          </SelectItem>
                        );
                      })}
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                  {isCustomValue && (
                    <div className="flex flex-col gap-1">
                      <Input
                        value={value}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          // Check for duplicates as user types
                          handlePricingChange(
                            context.row.index,
                            "priceScenario",
                            newValue
                          );
                        }}
                        className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300 ${
                          isDuplicate ? "border-red-300 bg-red-50" : ""
                        }`}
                        placeholder="Type custom scenario"
                        autoFocus
                      />
                      {isDuplicate && (
                        <div className="text-xs text-red-600 px-1">
                          This scenario is already used
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div
                  className={`px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${
                    isDuplicate ? "bg-red-50 border-red-200" : ""
                  }`}
                  onClick={() => handleCopy(value, cellId)}
                  title={isDuplicate ? `${value} (Duplicate scenario)` : value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative">
                    <span className={isDuplicate ? "text-red-600" : ""}>
                      {value || "-"}
                      {isDuplicate && " ⚠️"}
                    </span>
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      // Add more columns for the remaining fields
      {
        accessorKey: "priceUsd",
        header: () => <div className="text-center">Price</div>,
        size: widths.priceUsd,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `priceUsd-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "priceUsd",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "priceUsd")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="$0.00"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatCurrency(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "estRefundRate",
        header: () => <div className="text-center">Est Refund Rate</div>,
        size: widths.estRefundRate,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `estRefundRate-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "estRefundRate",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "estRefundRate")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="0%"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatPercentage(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "referalFeePercent",
        header: () => <div className="text-center">Referral Fee</div>,
        size: widths.referalFeePercent,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `referalFeePercent-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "referalFeePercent",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "referalFeePercent")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="0%"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatPercentage(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      // Add remaining columns
      {
        accessorKey: "fbaFeeUsd",
        header: () => <div className="text-center">FBA Fee</div>,
        size: widths.fbaFeeUsd,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `fbaFeeUsd-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "fbaFeeUsd",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "fbaFeeUsd")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="$0.00"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatCurrency(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },

      {
        accessorKey: "fbaFeePercent",
        header: () => <div className="text-center">FBA Fee Percent</div>,
        size: widths.fbaFeePercent,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const row = context.row.original;
          const calculatedValue = calculateFbaFeePercent(
            row.fbaFeeUsd,
            row.priceUsd
          );
          const cellId = `fbaFeePercent-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              <div
                className="px-1 py-0.5 text-center cursor-pointer rounded text-primary font-medium border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                onClick={() => handleCopy(calculatedValue, cellId)}
                title={`Calculated: ${calculatedValue}`}
                style={{ fontSize: "12px" }}
              >
                <div className="relative truncate w-full">
                  {calculatedValue}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "landedCostPercent",
        header: () => <div className="text-center">Landed Cost Percent</div>,
        size: widths.landedCostPercent,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `landedCostPercent-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "landedCostPercent",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "landedCostPercent")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="0%"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatPercentage(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "landedCostUsd",
        header: () => <div className="text-center">Landed Cost</div>,
        size: widths.landedCostUsd,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const row = context.row.original;
          const calculatedValue = calculateLandedCostUsd(
            row.landedCostPercent,
            row.priceUsd
          );
          const cellId = `landedCostUsd-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              <div
                className="px-1 py-0.5 text-center cursor-pointer rounded text-primary font-medium border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                onClick={() => handleCopy(calculatedValue, cellId)}
                title={`Calculated: ${calculatedValue}`}
                style={{ fontSize: "12px" }}
              >
                <div className="relative truncate w-full">
                  {calculatedValue}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "tacosPercent",
        header: () => <div className="text-center">TACOS</div>,
        size: widths.tacosPercent,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `tacosPercent-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "tacosPercent",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "tacosPercent")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="0%"
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value ? formatPercentage(value) : "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "netProfitPercent",
        header: () => <div className="text-center">Net Profit Percent</div>,
        size: widths.netProfitPercent,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const row = context.row.original;
          const calculatedValue = calculateNetProfitPercent(
            row.priceUsd,
            row.referalFeePercent,
            row.fbaFeeUsd,
            row.landedCostPercent,
            row.tacosPercent
          );
          const cellId = `netProfitPercent-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              <div
                className="px-1 py-0.5 text-center cursor-pointer rounded text-primary font-medium border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                onClick={() => handleCopy(calculatedValue, cellId)}
                title={`Calculated: ${calculatedValue}`}
                style={{ fontSize: "12px" }}
              >
                <div className="relative truncate w-full">
                  {calculatedValue}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "netProfitUsd",
        header: () => <div className="text-center">Net Profit</div>,
        size: widths.netProfitUsd,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const row = context.row.original;
          const calculatedValue = calculateNetProfitUsd(
            row.priceUsd,
            row.referalFeePercent,
            row.fbaFeeUsd,
            row.landedCostPercent,
            row.tacosPercent
          );
          const cellId = `netProfitUsd-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              <div
                className="px-1 py-0.5 text-center cursor-pointer rounded text-primary font-medium border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center justify-center overflow-hidden"
                onClick={() => handleCopy(calculatedValue, cellId)}
                title={`Calculated: ${calculatedValue}`}
                style={{ fontSize: "12px" }}
              >
                <div className="relative truncate w-full">
                  {calculatedValue}
                  {copiedCell === cellId && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                      Copied!
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "commentary",
        header: () => <div className="text-center">Commentary</div>,
        size: widths.commentary,
        enableSorting: true,
        cell: (context: CellContext<TableRow, unknown>) => {
          const value = String(context.getValue() || "");
          const cellId = `commentary-${context.row.index}`;
          return (
            <div className="px-1 py-0.5">
              {isEditing ? (
                <Input
                  value={value}
                  onChange={(e) =>
                    handlePricingChange(
                      context.row.index,
                      "commentary",
                      e.target.value
                    )
                  }
                  className={`h-6 text-xs border-0 bg-transparent p-1 focus:bg-white focus:border ${
                    isFieldModified(context.row.index, "commentary")
                      ? "focus:border-orange-500 ring-2 ring-orange-200"
                      : "focus:border-blue-300"
                  }`}
                  placeholder="Add commentary..."
                />
              ) : (
                <div
                  className="px-1 py-0.5 text-left cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-8 flex items-center overflow-hidden"
                  onClick={() => handleCopy(value, cellId)}
                  title={value}
                  style={{ fontSize: "12px" }}
                >
                  <div className="relative truncate w-full">
                    {value || "-"}
                    {copiedCell === cellId && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                        Copied!
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        },
      },
      // Delete action column
      {
        accessorKey: "actions",
        header: () => <div className="text-center">Actions</div>,
        size: widths.actions,
        enableResizing: false,
        enableSorting: false,
        cell: (context: CellContext<TableRow, unknown>) => {
          return (
            <div className="flex items-center justify-center py-0.5">
              {isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDeletePricingScenario(context.row.index)}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          );
        },
      },
    ];
  }, [
    isEditing,
    handlePricingChange,
    handleCopy,
    copiedCell,
    handleDeletePricingScenario,
    calculateFbaFeePercent,
    calculateLandedCostUsd,
    calculateNetProfitPercent,
    calculateNetProfitUsd,
    formatCurrency,
    formatPercentage,
    isPriceScenarioUsed,
    columnSettings.widths,
    isFieldModified,
  ]);

  // Create table instance
  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      sorting,
      columnSizing: columnSettings.widths,
      columnVisibility: columnSettings.visibility,
    },
    onSortingChange: handleSortingChange,
    onColumnSizingChange: handleColumnResize,
    onColumnVisibilityChange: (updater) => {
      const newVisibility =
        typeof updater === "function"
          ? updater(columnSettings.visibility)
          : updater;
      handleVisibilityChange(newVisibility);
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    columnResizeMode: "onChange" as ColumnResizeMode,
    enableColumnResizing: true,
    enableHiding: true,
  });

  return (
    <div className="h-full w-full overflow-y-auto">
      <div className="space-y-6 p-6">
        {/* Pricing Scenarios Table */}
        <Card className="flex-shrink-0">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                <span>Pricing Scenarios</span>
                {pricingData.length > 0 && (
                  <Badge variant="secondary">
                    {pricingData.length} scenario
                    {pricingData.length !== 1 ? "s" : ""}
                  </Badge>
                )}
                {isDirty && (
                  <div className="flex items-center gap-1 ml-2">
                    <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                    <span className="text-xs text-amber-600 font-normal">
                      Unsaved changes
                    </span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {pricingData.length > 0 && (
                  <ColumnToggle
                    columnVisibility={columnSettings.visibility}
                    onVisibilityChange={handleVisibilityChange}
                    onResetVisibility={handleResetVisibility}
                    onResetWidths={handleResetWidths}
                  />
                )}
                {isEditing && (
                  <Button size="sm" onClick={handleAddPricingScenario}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Scenario
                  </Button>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {pricingData.length > 0 ? (
              <TabsTableContainer
                maxHeight="600px"
                height={pricingData.length === 0 ? "auto" : "fit-content"}
              >
                <div ref={tableContainerRef}>
                  <table
                    {...{
                      style: {
                        width: table.getCenterTotalSize(),
                        minWidth: "100%",
                      },
                    }}
                    className={TABLE_STYLES.table}
                  >
                    <TabsTableHeader table={table} />
                    <TabsTableBody
                      table={table}
                      getRowClassName={(rowIndex) => {
                        return rowIndex % 2 === 0
                          ? TABLE_STYLES.bodyRow.even
                          : TABLE_STYLES.bodyRow.odd;
                      }}
                    />
                  </table>
                </div>
              </TabsTableContainer>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No pricing scenarios available.</p>
                {isEditing ? (
                  <div className="mt-4">
                    <p className="text-sm mb-4">
                      Click the button above to add your first pricing scenario.
                    </p>
                  </div>
                ) : (
                  <p className="text-sm">
                    Edit this tab to add pricing scenario data.
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tab Danger Zone - Only show in edit mode */}
        {isEditing && tabName && productId && productName && productSlug && (
          <TabDangerZone
            tabId={tabId}
            tabName={tabName}
            productId={productId}
            productName={productName}
            productSlug={productSlug}
            tabCreatorId={tabCreatorId}
            productOwnerId={productOwnerId}
            isEditMode={isEditing}
          />
        )}
      </div>
    </div>
  );
});
