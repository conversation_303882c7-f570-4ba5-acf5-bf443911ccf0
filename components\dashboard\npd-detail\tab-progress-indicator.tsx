"use client";

import React, { useState, useEffect } from "react";
import { getTabCompletions } from "@/actions/tab-completion.actions";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Tab {
  id: string;
  tabName: string;
  order: number;
}

interface TabCompletion {
  npdProductTabId: string;
  completion: number;
  npdProductTab: {
    id: string;
    tabName: string;
    order: number;
  };
}

interface TabProgressIndicatorProps {
  tabs: Tab[];
  productId: string;
  className?: string;
  // Trigger for forcing refresh
  refreshTrigger?: number;
}

// Get tab display name for UI
function getTabDisplayName(tabName: string): string {
  const displayNames: Record<string, string> = {
    Overview: "Overview",
    "1.1": "Section 1.1",
    "1.2": "Section 1.2",
    "1.3": "Section 1.3",
    "Sourcing Brief": "Sourcing Brief",
  };
  return displayNames[tabName] || tabName;
}

export function TabProgressIndicator({
  tabs,
  productId,
  className = "",
  refreshTrigger = 0,
}: TabProgressIndicatorProps) {
  const [completions, setCompletions] = useState<TabCompletion[]>([]);
  const [loading, setLoading] = useState(true);

  // Load completion data from database
  useEffect(() => {
    const loadCompletions = async () => {
      try {
        setLoading(true);
        const data = await getTabCompletions(productId);
        setCompletions(data);
      } catch (error) {
        console.error("Failed to load tab completions:", error);
        setCompletions([]);
      } finally {
        setLoading(false);
      }
    };

    loadCompletions();
  }, [productId, refreshTrigger]);

  if (!tabs || tabs.length === 0) {
    return null;
  }

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center">
          <div className="w-80 relative h-1.5 bg-muted rounded-full overflow-hidden">
            <div className="h-full bg-muted animate-pulse rounded-full" />
          </div>
          <span className="text-xs text-muted-foreground font-medium min-w-[3rem] text-right ml-2">
            ---%
          </span>
        </div>
      </div>
    );
  }

  // Create a map of tab completions for quick lookup
  const completionMap = new Map<string, number>();
  completions.forEach((completion) => {
    completionMap.set(completion.npdProductTabId, completion.completion);
  });

  // Sort tabs by order and get their completion values
  const sortedTabs = tabs
    .sort((a, b) => a.order - b.order)
    .map((tab) => ({
      ...tab,
      completion: completionMap.get(tab.id) || 0,
      displayName: getTabDisplayName(tab.tabName),
    }));

  // Calculate overall progress
  const overallProgress =
    sortedTabs.length > 0
      ? Math.round(
          sortedTabs.reduce((sum, tab) => sum + tab.completion, 0) /
            sortedTabs.length
        )
      : 0;

  return (
    <div className={`${className}`}>
      {/* Progress label and bar */}
      <div className="flex items-center">
        <div className="w-80 relative h-1.5 bg-muted rounded-full overflow-hidden">
          <div
            className="h-full bg-primary transition-all duration-300 rounded-full"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-xs text-muted-foreground font-medium min-w-[3rem] text-right cursor-default ml-2">
              {overallProgress}%
            </span>
          </TooltipTrigger>
          <TooltipContent side="bottom" align="end" className="max-w-xs">
            <div className="space-y-1">
              <div className="font-medium text-xs mb-2">Progress Breakdown</div>
              {sortedTabs.map((tab) => (
                <div
                  key={tab.id}
                  className="flex justify-between items-center text-xs"
                >
                  <span>{tab.displayName}:</span>
                  <span className="font-medium ml-2">{tab.completion}%</span>
                </div>
              ))}
              <div className="border-t pt-1 mt-2">
                <div className="flex justify-between items-center text-xs font-medium">
                  <span>Overall:</span>
                  <span>{overallProgress}%</span>
                </div>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
