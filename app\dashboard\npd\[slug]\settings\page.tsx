import { ProductSettingsContent } from "@/components/dashboard/npd-settings-content";

interface NPDSettingsPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: NPDSettingsPageProps) {
  const { slug } = await params;

  // Simple metadata for internal app - no need for database lookup
  return {
    title: `${slug} Settings - NPD`,
    description: "NPD settings",
  };
}

// Force dynamic rendering
export const dynamic = "force-dynamic";

const NPDSettingsPage = async ({ params }: NPDSettingsPageProps) => {
  const { slug } = await params;
  
  return <ProductSettingsContent slug={slug} />;
};

export default NPDSettingsPage;
