import { z } from "zod";

// Common form validation schemas
export const createProductSchema = z.object({
  name: z
    .string()
    .min(1, "Product name is required")
    .max(255, "Product name is too long"),
  brand: z.string().min(1, "Brand is required"),
  stage: z.string().min(1, "Stage is required"),
  description: z.string().optional().or(z.literal("")),
  imageUrl: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  subscribedUserIds: z.array(z.string()).optional(),
});

export type CreateProductFormData = z.infer<typeof createProductSchema>;

export interface FormErrors {
  name?: string;
  brand?: string;
  stage?: string;
  description?: string;
  imageUrl?: string;
  subscribedUserIds?: string;
}

// Validation helper function
export function validateForm<T>(
  schema: z.ZodSchema<T>,
  data: T
): Record<string, string> {
  const result = schema.safeParse(data);

  if (result.success) {
    return {};
  }

  const formErrors: Record<string, string> = {};
  result.error.errors.forEach((error) => {
    const path = error.path[0] as string;
    formErrors[path] = error.message;
  });

  return formErrors;
}
