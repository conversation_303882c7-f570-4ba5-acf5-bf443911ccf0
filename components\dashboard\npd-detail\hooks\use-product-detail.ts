"use client";

import { useState, useEffect, useCallback } from "react";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useNPDContext } from "@/context/npd-context";
import { useNavigationGuard } from "@/context/navigation-guard-context";

// Helper function for tab name URL conversion
const tabNameToUrlSlug = (tabName: string): string => {
  return tabName
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^a-z0-9-]/g, "")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
};

export function useProductDetail() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const slug = params.slug as string;
  const tabParam = searchParams.get("tab");
  const openChatParam = searchParams.get("openChat");

  // State management
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<() => void>(
    () => () => {}
  );
  const [localLoading, setLocalLoading] = useState(true);
  const [currentSlug, setCurrentSlug] = useState<string | null>(null);
  const [previousProductId, setPreviousProductId] = useState<string | null>(
    null
  );

  // Panel state
  const [openPanel, setOpenPanel] = useState<"activity" | "chat" | null>(null);
  const [panelWidth, setPanelWidth] = useState<number | null>(null);

  const {
    selectedProduct,
    isLoadingSelectedProduct,
    isLoadingProducts,
    selectedProductError,
    selectProductBySlug: selectProductBySlugAction,
    pendingSlug,
  } = useNPDContext();

  // Create a stable reference to selectProductBySlug
  const selectProductBySlug = useCallback(
    (slug: string) => {
      selectProductBySlugAction(slug);
    },
    [selectProductBySlugAction]
  );

  // Compute loading state
  const isLoading =
    localLoading ||
    pendingSlug ||
    isLoadingSelectedProduct ||
    isLoadingProducts;

  // Select product when slug changes
  useEffect(() => {
    if (slug && slug !== currentSlug) {
      setCurrentSlug(slug);
      setLocalLoading(true);
      selectProductBySlug(slug);
    }
  }, [slug, currentSlug, selectProductBySlug]);

  // Clear local loading when we have the correct product
  useEffect(() => {
    if (selectedProduct && selectedProduct.slug === currentSlug) {
      setLocalLoading(false);
    } else if (selectedProductError) {
      setLocalLoading(false);
    }
  }, [selectedProduct, selectedProductError, currentSlug]);

  // Safety mechanism: clear local loading after timeout
  useEffect(() => {
    if (localLoading) {
      const timeout = setTimeout(() => {
        setLocalLoading(false);
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [localLoading]);

  // Initialize panel width
  useEffect(() => {
    if (panelWidth === null) {
      const savedWidth = localStorage.getItem("product_panel_width");
      if (savedWidth) {
        setPanelWidth(parseFloat(savedWidth));
      } else {
        setPanelWidth(30);
      }
    }
  }, [panelWidth]);

  // Handle product changes and unsaved changes
  useEffect(() => {
    if (
      selectedProduct?.id &&
      selectedProduct.id !== previousProductId &&
      previousProductId !== null
    ) {
      if (hasUnsavedChanges && isEditing) {
        const confirmed = window.confirm(
          "You have unsaved changes. Are you sure you want to switch products? All changes will be lost."
        );
        if (!confirmed) {
          window.history.back();
          return;
        }
        setHasUnsavedChanges(false);
        setIsEditing(false);
      }
    }

    if (selectedProduct?.id) {
      setPreviousProductId(selectedProduct.id);
    }
  }, [selectedProduct?.id, previousProductId, hasUnsavedChanges, isEditing]);

  // Set active tab when product changes or tab URL parameter is present
  useEffect(() => {
    if (selectedProduct?.tabs && selectedProduct.tabs.length > 0) {
      const sortedTabs = selectedProduct.tabs.sort((a, b) => a.order - b.order);

      // Check if there's a tab parameter in the URL
      if (tabParam) {
        const tabFromUrl = selectedProduct.tabs.find(
          (tab) =>
            tabNameToUrlSlug(tab.tabName) === tabParam ||
            tab.tabName === tabParam ||
            tab.id === tabParam
        );
        if (tabFromUrl) {
          setActiveTab(tabFromUrl.id);
          // Update URL to use slug format if needed
          const expectedSlug = tabNameToUrlSlug(tabFromUrl.tabName);
          if (expectedSlug !== tabParam) {
            const url = new URL(window.location.href);
            url.searchParams.set("tab", expectedSlug);
            router.replace(url.pathname + url.search, { scroll: false });
          }
          return;
        }
        // Tab not found, falling back to first tab
      }

      const currentTabExists =
        activeTab && selectedProduct.tabs.some((tab) => tab.id === activeTab);
      if (!activeTab || !currentTabExists) {
        setActiveTab(sortedTabs[0]?.id || null);
      }
    } else {
      setActiveTab("no-tabs");
    }
  }, [selectedProduct, activeTab, tabParam, router]);

  // Auto-open chat panel when openChat parameter is present
  useEffect(() => {
    if (openChatParam === "true" && selectedProduct) {
      setOpenPanel("chat");

      // Clean up the URL parameter after opening the panel
      const url = new URL(window.location.href);
      url.searchParams.delete("openChat");
      router.replace(url.pathname + url.search, { scroll: false });
    }
  }, [openChatParam, selectedProduct, router]);

  // Helper function to update URL with tab parameter
  const updateUrlWithTab = useCallback(
    (tabId: string) => {
      if (selectedProduct && tabId !== "no-tabs") {
        const tab = selectedProduct.tabs.find((t) => t.id === tabId);
        if (tab) {
          const url = new URL(window.location.href);
          url.searchParams.set("tab", tabNameToUrlSlug(tab.tabName));
          router.replace(url.pathname + url.search, { scroll: false });
        } else {
          // If tab not found, try again after a short delay
          setTimeout(() => {
            const retryTab = selectedProduct.tabs.find((t) => t.id === tabId);
            if (retryTab) {
              const url = new URL(window.location.href);
              url.searchParams.set("tab", tabNameToUrlSlug(retryTab.tabName));
              router.replace(url.pathname + url.search, { scroll: false });
            }
          }, 200);
        }
      } else {
        const url = new URL(window.location.href);
        url.searchParams.delete("tab");
        router.replace(url.pathname + url.search, { scroll: false });
      }
    },
    [selectedProduct, router]
  );

  // Navigation guard setup
  const navigationGuard = useNavigationGuard();
  const { setHasUnsavedChanges: setGlobalUnsavedChanges, setOnConfirmAction } =
    navigationGuard;

  const handleNavigationConfirm = useCallback(() => {
    setIsEditing(false);
    setHasUnsavedChanges(false);
  }, []);

  useEffect(() => {
    setGlobalUnsavedChanges(hasUnsavedChanges && isEditing);
    setOnConfirmAction(handleNavigationConfirm);
  }, [
    hasUnsavedChanges,
    isEditing,
    setGlobalUnsavedChanges,
    setOnConfirmAction,
    handleNavigationConfirm,
  ]);

  // Browser beforeunload handler
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && isEditing) {
        e.preventDefault();
        e.returnValue =
          "You have unsaved changes. Are you sure you want to leave?";
        return "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges, isEditing]);

  // Document title update
  useEffect(() => {
    const originalTitle = document.title;
    if (hasUnsavedChanges && isEditing) {
      document.title = `● ${originalTitle}`;
    } else {
      document.title = originalTitle.replace("● ", "");
    }

    return () => {
      document.title = originalTitle.replace("● ", "");
    };
  }, [hasUnsavedChanges, isEditing]);

  return {
    // State
    slug,
    activeTab,
    isEditing,
    hasUnsavedChanges,
    showConfirmDialog,
    pendingAction,
    localLoading,
    currentSlug,
    openPanel,
    panelWidth,

    // Product data
    selectedProduct,
    selectedProductError,
    isLoading,

    // Setters
    setActiveTab,
    setIsEditing,
    setHasUnsavedChanges,
    setShowConfirmDialog,
    setPendingAction,
    setOpenPanel,
    setPanelWidth,

    // Helpers
    updateUrlWithTab,
    tabNameToUrlSlug,
  };
}
