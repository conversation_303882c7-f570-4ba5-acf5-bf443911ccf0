"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { EllipsisVertical, UserIcon, Bell, Eye, EyeOff, X } from "lucide-react";
import ModeToggle from "./mode-toggle";
import { AuthModal } from "@/components/auth-modal";
import { UserMenu } from "@/components/user-menu";
import Spinner from "@/components/spinner";
import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useNotifications, type Notification } from "@/hooks/use-notifications";
import { useNavigationGuard } from "@/context/navigation-guard-context";
import { useRouter } from "next/navigation";

const Menu = ({
  showNotifications = false,
}: {
  showNotifications?: boolean;
}) => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] =
    useState(false);
  const { user, isLoading, signOut } = useAuth();
  const router = useRouter();

  // Use real notifications hook

  const {
    notifications: allNotifications,
    markNotificationAsRead,
    markAsUnread,
    hideNotifications,
  } = useNotifications({
    enabled: !!user && showNotifications,
    limit: 20,
    includeHidden: true,
  });

  // Local state for visible notifications
  const [visibleNotifications, setVisibleNotifications] = useState(
    allNotifications.filter((n) => !n.hidden)
  );

  useEffect(() => {
    setVisibleNotifications(allNotifications.filter((n) => !n.hidden));
  }, [allNotifications]);

  // Try to use navigation guard, but fallback to router if not available
  let navigateWithConfirmation: ((url: string) => void) | null = null;
  try {
    const navigationGuard = useNavigationGuard();
    navigateWithConfirmation = navigationGuard.navigateWithConfirmation;
  } catch {
    // NavigationGuardProvider not available, use router directly
    navigateWithConfirmation = (url: string) => router.push(url);
  }

  const handleSignOut = async () => {
    await signOut();
  };

  const toggleReadStatus = async (
    notificationId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent triggering the main notification click

    const notification = allNotifications.find((n) => n.id === notificationId);
    if (!notification) return;

    try {
      if (!notification.read) {
        await markNotificationAsRead(notificationId);
      } else {
        await markAsUnread([notificationId]);
      }
    } catch (error) {
      console.error("Error updating notification status:", error);
    }
  };

  const handleHideNotification = async (
    notificationId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent triggering the main notification click
    // Optimistically remove from visible notifications
    setVisibleNotifications((prev) =>
      prev.filter((n) => n.id !== notificationId)
    );
    try {
      await hideNotifications([notificationId]);
    } catch (error) {
      console.error("Error hiding notification:", error);
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read when clicked
    if (!notification.read) {
      try {
        await markNotificationAsRead(notification.id);
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    }

    // Close the notification panel
    setNotificationDropdownOpen(false);

    // Navigate based on notification type and data
    let actionUrl = "/dashboard";

    if (notification.type === "role_update") {
      actionUrl = "/dashboard/profile";
    } else if (notification.type === "product_assignment") {
      // Extract product slug from notification data if available
      const productSlug = notification.data?.productSlug;
      if (productSlug) {
        actionUrl = `/dashboard/npd/${productSlug}`;
      }
    } else if (notification.type === "product_activity") {
      // Extract product slug from notification data if available
      const productSlug = notification.data?.productSlug;
      if (productSlug) {
        actionUrl = `/dashboard/npd/${productSlug}`;
      }
    } else if (notification.type === "user_mention") {
      // Extract specific URL from notification data if available
      const targetUrl = notification.data?.actionUrl as string;
      if (targetUrl) {
        actionUrl = targetUrl;
      }
    } else if (
      notification.type === "deletion_request" ||
      notification.type === "next_step_assignment"
    ) {
      // Handle overview-related notifications
      const productSlug = notification.data?.productSlug;
      const tabId = notification.data?.tabId;
      const entryType = notification.data?.entryType;
      const entryId = notification.data?.entryId;

      if (productSlug && tabId) {
        // Use "overview" as the tab slug for overview tabs
        actionUrl = `/dashboard/npd/${productSlug}?tab=overview`;

        // Add highlight parameter if we have entry details
        if (entryType && entryId) {
          actionUrl += `&highlight=${entryType}-${entryId}`;
        }
      } else if (productSlug) {
        // Fallback to just the product page
        actionUrl = `/dashboard/npd/${productSlug}`;
      }
    }

    // Navigate to the notification's action URL with confirmation
    if (navigateWithConfirmation) {
      navigateWithConfirmation(actionUrl);
    }
  };

  return (
    <div className="flex justify-end gap-3">
      {/* Desktop menu */}
      <nav className="hidden md:flex w-full max-w-xs gap-1">
        {/* Notification Bell - Only show for logged in users */}
        {showNotifications && user && (
          <Sheet
            open={notificationDropdownOpen}
            onOpenChange={setNotificationDropdownOpen}
          >
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative h-10 w-10"
              >
                <Bell className="h-5 w-5" />
                {/* Notification Badge */}
                {(() => {
                  const visibleUnreadCount = visibleNotifications.filter(
                    (n) => !n.read
                  ).length;
                  return visibleUnreadCount > 0 ? (
                    <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {visibleUnreadCount}
                    </span>
                  ) : null;
                })()}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80 sm:w-96 p-6">
              <SheetTitle>Notifications</SheetTitle>
              <SheetDescription className="text-sm text-muted-foreground mb-4">
                Manage your notifications and stay updated
              </SheetDescription>

              {/* Notifications List */}
              <div className="space-y-2">
                {visibleNotifications.map((notification) => {
                  const isRead = notification.read;
                  const timeAgo = new Date(
                    notification.createdAt
                  ).toLocaleString();

                  return (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors hover:bg-accent ${
                        isRead
                          ? "opacity-60 bg-muted/50"
                          : "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800"
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex w-full justify-between items-start">
                        <div className="flex items-start gap-3 flex-1">
                          {/* Unread indicator */}
                          {!isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          )}
                          <div className={!isRead ? "" : "ml-5"}>
                            <p className="text-sm font-medium">
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-1 ml-2">
                          <span className="text-xs text-muted-foreground">
                            {timeAgo}
                          </span>
                          {/* Toggle Read/Unread Button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer"
                            onClick={(e) =>
                              toggleReadStatus(notification.id, e)
                            }
                            title={isRead ? "Mark as unread" : "Mark as read"}
                          >
                            {isRead ? (
                              <EyeOff className="h-3 w-3" />
                            ) : (
                              <Eye className="h-3 w-3" />
                            )}
                          </Button>
                          {/* Hide Notification Button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-red-200 dark:hover:bg-red-700 cursor-pointer"
                            onClick={(e) =>
                              handleHideNotification(notification.id, e)
                            }
                            title="Hide notification"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {visibleNotifications.length === 0 && (
                  <div className="text-center text-sm text-muted-foreground p-8">
                    <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No notifications</p>
                  </div>
                )}
              </div>

              {/* Footer Actions */}
              <div className="mt-6 pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    setNotificationDropdownOpen(false);
                    router.push("/dashboard/profile#notifications-section");
                  }}
                >
                  View All Notifications
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        )}
        <ModeToggle />
        {isLoading ? (
          <Button variant="default" disabled className="min-w-[100px] h-10">
            <UserIcon />
            <Spinner loading={true} />
          </Button>
        ) : user ? (
          <UserMenu user={user} onSignOut={handleSignOut} variant="desktop" />
        ) : (
          <Button
            variant="default"
            onClick={() => setIsAuthModalOpen(true)}
            className="min-w-[100px] h-10"
          >
            <UserIcon />
            Sign In
          </Button>
        )}
      </nav>

      {/* Mobile menu */}
      <nav className="md:hidden">
        <Sheet>
          <SheetTrigger className="align-middle">
            <EllipsisVertical />
          </SheetTrigger>
          <SheetContent className="flex flex-col items-start p-8">
            <SheetTitle>Menu</SheetTitle>
            <SheetDescription></SheetDescription>
            <ModeToggle />
            {isLoading ? (
              <Button disabled className="min-w-[100px] h-10">
                <UserIcon />
                <Spinner loading={true} />
              </Button>
            ) : user ? (
              <UserMenu
                user={user}
                onSignOut={handleSignOut}
                variant="mobile"
              />
            ) : (
              <Button
                onClick={() => setIsAuthModalOpen(true)}
                className="min-w-[100px] h-10"
              >
                <UserIcon />
                Sign In
              </Button>
            )}
          </SheetContent>
        </Sheet>
      </nav>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </div>
  );
};

export default Menu;
