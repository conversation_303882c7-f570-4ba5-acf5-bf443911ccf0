import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

// GET /api/admin/roles - Get all roles
export async function GET() {
  try {
    const user = await validateServerAction();

    // Check if user has admin or super admin role
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const hasAdminAccess = userWithRoles?.roles.some(
      (role) => role.name === "ADMIN" || role.name === "SUPER_ADMIN"
    );

    if (!hasAdminAccess) {
      return NextResponse.json(
        { message: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          select: {
            id: true,
            code: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json(roles);
  } catch (error) {
    console.error("Error fetching roles:", error);
    return NextResponse.json(
      { message: "Failed to fetch roles" },
      { status: 500 }
    );
  }
}

// POST /api/admin/roles - Create new role
export async function POST(request: NextRequest) {
  try {
    const user = await validateServerAction();

    // Check if user is Super Admin
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.userId },
      include: { roles: true },
    });

    const isSuperAdmin = userWithRoles?.roles.some(
      (role) => role.name === "SUPER_ADMIN"
    );

    if (!isSuperAdmin) {
      return NextResponse.json(
        { message: "Access denied. Super Admin role required." },
        { status: 403 }
      );
    }

    const { name, permissionIds } = await request.json();

    if (!name || typeof name !== "string") {
      return NextResponse.json(
        { message: "Role name is required" },
        { status: 400 }
      );
    }

    // Validate role name format
    const roleName = name.trim().toUpperCase();
    if (!/^[A-Z_&]+$/.test(roleName)) {
      return NextResponse.json(
        {
          message:
            "Role name must contain only uppercase letters, underscores, and ampersands",
        },
        { status: 400 }
      );
    }

    // Check if role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: roleName },
    });

    if (existingRole) {
      return NextResponse.json(
        { message: "Role with this name already exists" },
        { status: 409 }
      );
    }

    // Validate permission IDs if provided
    if (
      permissionIds &&
      Array.isArray(permissionIds) &&
      permissionIds.length > 0
    ) {
      const validPermissions = await prisma.permission.findMany({
        where: { id: { in: permissionIds } },
      });

      if (validPermissions.length !== permissionIds.length) {
        return NextResponse.json(
          { message: "One or more permission IDs are invalid" },
          { status: 400 }
        );
      }
    }

    const role = await prisma.role.create({
      data: {
        name: roleName,
        permissions:
          permissionIds && permissionIds.length > 0
            ? {
                connect: permissionIds.map((id: string) => ({ id })),
              }
            : undefined,
      },
      include: {
        permissions: {
          select: {
            id: true,
            code: true,
          },
        },
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    return NextResponse.json(role, { status: 201 });
  } catch (error) {
    console.error("Error creating role:", error);
    return NextResponse.json(
      { message: "Failed to create role" },
      { status: 500 }
    );
  }
}
