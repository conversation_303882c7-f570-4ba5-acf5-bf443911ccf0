import { NextRequest, NextResponse } from "next/server";
import { signToken, setAuthCookieOnResponse } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";
import { notifyAdminsAboutNewUser } from "@/lib/utils/notifications";

interface GoogleTokenResponse {
  access_token: string;
  id_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  refresh_token?: string;
}

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const state = searchParams.get("state") || "/dashboard";
  const error = searchParams.get("error");

  // Get the base URL from the request
  const protocol = process.env.NODE_ENV === "production" ? "https" : "http";
  const host = request.headers.get("host") || "localhost:3000";
  const baseUrl = `${protocol}://${host}`;

  if (error) {
    console.error("OAuth error:", error);
    return NextResponse.redirect(`${baseUrl}?error=oauth_error`);
  }

  if (!code) {
    return NextResponse.redirect(`${baseUrl}?error=missing_code`);
  }

  try {
    // Exchange code for tokens
    const redirectUri = `${baseUrl}/api/auth/google/callback`;

    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: "authorization_code",
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error("Token exchange failed:", {
        status: tokenResponse.status,
        error: errorText,
      });
      throw new Error(
        `Failed to exchange code for tokens: ${tokenResponse.status}`
      );
    }

    const tokens: GoogleTokenResponse = await tokenResponse.json();

    // Get user info from Google
    const userResponse = await fetch(
      "https://www.googleapis.com/oauth2/v2/userinfo",
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      }
    );

    if (!userResponse.ok) {
      throw new Error("Failed to get user info");
    }

    const googleUser: GoogleUserInfo = await userResponse.json();

    // Find or create user in database
    let user = await prisma.user.findUnique({
      where: { email: googleUser.email },
      include: { roles: true },
    });

    if (!user) {
      // Create new user without any roles (pending approval)
      user = await prisma.user.create({
        data: {
          email: googleUser.email,
          name: googleUser.name,
          image: googleUser.picture,
          provider: "google",
          providerId: googleUser.id,
          lastLoginAt: new Date(),
        },
        include: { roles: true },
      });

      // Notify admins about new user registration (don't await to avoid blocking)
      notifyAdminsAboutNewUser(user.id, user.name, user.email).catch(
        (error) => {
          console.error("Failed to notify admins about new user:", error);
        }
      );
    } else {
      // Update existing user
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginAt: new Date(),
          // Update image and provider info
          image: googleUser.picture,
          provider: user.provider || "google",
          providerId: user.providerId || googleUser.id,
        },
        include: { roles: true },
      });
    }

    // Create JWT token
    const token = await signToken({
      userId: user.id,
      email: user.email,
      name: user.name,
      image: user.image,
      provider: user.provider,
      roles: user.roles.map((role: { name: string }) => role.name),
    });

    // Set auth cookie and redirect
    const response = NextResponse.redirect(`${baseUrl}${state}`);
    setAuthCookieOnResponse(response, token);

    return response;
  } catch (error) {
    console.error("Auth error:", error);
    return NextResponse.redirect(`${baseUrl}?error=auth_error`);
  }
}
