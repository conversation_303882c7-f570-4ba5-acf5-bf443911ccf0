import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import { DashboardLayoutClient } from "@/components/dashboard/dashboard-layout-client";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getCurrentUser();

  // Check if user is authenticated
  if (!user) {
    redirect("/");
  }

  // Check if user has any role - if not, redirect to a "pending approval" page
  if (!user.roles || user.roles.length === 0) {
    redirect("/pending-approval");
  }

  return <DashboardLayoutClient>{children}</DashboardLayoutClient>;
}
