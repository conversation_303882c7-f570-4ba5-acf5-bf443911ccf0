"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useNPDContext } from "@/context/npd-context";
import { useAuth } from "@/context/auth-context";
import { usePermissions } from "@/hooks/use-permissions";
import { fetchAllUsers } from "@/actions/user.actions";
import { deleteNPD } from "@/actions/npd.actions";
import { requestProductDeletion } from "@/actions/npd-deletion.actions";
import { BRANDS } from "@/lib/constants/brands";
import {
  PRODUCT_STAGES,
  getProductStageColor,
  type ProductStage,
} from "@/lib/constants/product-stages";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  PackageIcon,
  UserIcon,
  SettingsIcon,
  TrashIcon,
  ArrowLeftIcon,
  SaveIcon,
  EditIcon,
  UploadIcon,
  ImageIcon,
} from "lucide-react";
import Spinner from "@/components/spinner";
import { toast } from "sonner";
import { useNavigationGuard } from "@/context/navigation-guard-context";

interface ProductSettingsContentProps {
  slug: string;
}

interface ProductSettingsForm {
  name: string;
  brand: string;
  stage: string;
  description: string;
  imageUrl: string;
  slug: string;
}

interface User {
  id: string;
  name: string;
  email: string;
}

export function ProductSettingsContent({ slug }: ProductSettingsContentProps) {
  const router = useRouter();
  const {
    selectedProduct,
    isLoadingSelectedProduct,
    selectedProductError,
    selectProductBySlug,
    updateProductSettings,
  } = useNPDContext();

  const { user } = useAuth();
  const { isAdmin, isSuperAdmin } = usePermissions();

  const [formData, setFormData] = useState<ProductSettingsForm>({
    name: "",
    brand: "",
    stage: "",
    description: "",
    imageUrl: "",
    slug: "",
  });

  const [users, setUsers] = useState<User[]>([]);
  const [subscribedUsers, setSubscribedUsers] = useState<User[]>([]);
  const [unsubscribedUsers, setUnsubscribedUsers] = useState<User[]>([]);
  const [originalSubscribedUsers, setOriginalSubscribedUsers] = useState<
    User[]
  >([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDeleteRequestDialog, setShowDeleteRequestDialog] = useState(false);
  const [deleteRequestReason, setDeleteRequestReason] = useState("");
  const [slugValidation, setSlugValidation] = useState<{
    isValid: boolean;
    isChecking: boolean;
    message: string;
  }>({ isValid: true, isChecking: false, message: "" });

  const { setHasUnsavedChanges: setGlobalUnsavedChanges } =
    useNavigationGuard();

  // Debounce timer ref
  const debounceTimerRef = React.useRef<NodeJS.Timeout | null>(null);

  // Load product data when component mounts or slug changes
  useEffect(() => {
    if (slug && slug !== selectedProduct?.slug) {
      selectProductBySlug(slug);
    }
  }, [slug, selectedProduct?.slug, selectProductBySlug]);

  // Update form data when product loads
  useEffect(() => {
    if (selectedProduct) {
      setFormData({
        name: selectedProduct.name,
        brand: selectedProduct.brand,
        stage: selectedProduct.stage,
        description: selectedProduct.description || "",
        imageUrl: selectedProduct.imageUrl || "",
        slug: selectedProduct.slug,
      });
      setHasUnsavedChanges(false);
    }
  }, [selectedProduct]);

  // Set global unsaved changes state
  useEffect(() => {
    setGlobalUnsavedChanges(hasUnsavedChanges);
  }, [hasUnsavedChanges, setGlobalUnsavedChanges]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Validation function for required fields
  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.name.trim()) {
      errors.push("Product name is required");
    }

    if (!formData.slug.trim()) {
      errors.push("Product slug is required");
    }

    if (!formData.brand.trim()) {
      errors.push("Brand is required");
    }

    if (!formData.stage.trim()) {
      errors.push("Stage is required");
    }

    return errors;
  };

  // Load users and subscriptions for this product
  useEffect(() => {
    const loadUsersAndSubscriptions = async () => {
      try {
        const fetchedUsers = await fetchAllUsers();
        setUsers(fetchedUsers);

        if (selectedProduct && selectedProduct.subscriptions) {
          // Get subscribed users from the product's subscription data
          const subscribed = selectedProduct.subscriptions.map(
            (sub) => sub.user
          );
          const subscribedIds = new Set(subscribed.map((u) => u.id));

          // Split users into subscribed and unsubscribed
          const subscribed_users = subscribed;
          const unsubscribed_users = fetchedUsers.filter(
            (user) => !subscribedIds.has(user.id)
          );

          setSubscribedUsers(subscribed_users);
          setUnsubscribedUsers(unsubscribed_users);
          setOriginalSubscribedUsers(subscribed_users);
        } else {
          // No subscription data available, treat all users as unsubscribed
          setSubscribedUsers([]);
          setUnsubscribedUsers(fetchedUsers);
          setOriginalSubscribedUsers([]);
        }
      } catch (error) {
        console.error("Error loading users and subscriptions:", error);
        toast.error("Failed to load users");
      } finally {
        setIsLoadingUsers(false);
      }
    };

    if (selectedProduct) {
      loadUsersAndSubscriptions();
    }
  }, [selectedProduct]);

  // Function to check if current form data differs from original product data
  const checkForChanges = (newFormData: ProductSettingsForm) => {
    if (!selectedProduct) return false;

    const formChanged =
      newFormData.name !== selectedProduct.name ||
      newFormData.brand !== selectedProduct.brand ||
      newFormData.stage !== selectedProduct.stage ||
      newFormData.slug !== selectedProduct.slug ||
      newFormData.description !== (selectedProduct.description || "") ||
      newFormData.imageUrl !== (selectedProduct.imageUrl || "");

    // Check if subscription changes were made
    const currentSubscribedIds = new Set(subscribedUsers.map((u) => u.id));
    const originalSubscribedIds = new Set(
      originalSubscribedUsers.map((u) => u.id)
    );
    const subscriptionChanged =
      currentSubscribedIds.size !== originalSubscribedIds.size ||
      [...currentSubscribedIds].some((id) => !originalSubscribedIds.has(id));

    return formChanged || subscriptionChanged;
  };

  const handleInputChange = (
    field: keyof ProductSettingsForm,
    value: string
  ) => {
    const newFormData = {
      ...formData,
      [field]: value,
    };

    setFormData(newFormData);

    // If slug is being changed, validate its uniqueness with debounce
    if (field === "slug" && value !== selectedProduct?.slug) {
      // Clear previous timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set loading state immediately
      setSlugValidation({
        isValid: true,
        isChecking: true,
        message: "Checking availability...",
      });

      // Start new timer
      debounceTimerRef.current = setTimeout(() => {
        validateSlugUniqueness(value);
      }, 500);
    } else if (field === "slug" && value === selectedProduct?.slug) {
      // Reset validation if slug is back to original
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      setSlugValidation({ isValid: true, isChecking: false, message: "" });
    }

    setHasUnsavedChanges(checkForChanges(newFormData));
  };

  // Function to validate slug uniqueness
  const validateSlugUniqueness = async (slug: string) => {
    if (!slug.trim()) {
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message: "Slug is required",
      });
      return;
    }

    // Basic slug format validation
    const slugPattern = /^[a-z0-9-]+$/;
    if (!slugPattern.test(slug)) {
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message:
          "Slug can only contain lowercase letters, numbers, and hyphens",
      });
      return;
    }

    try {
      // Check if slug exists by trying to fetch it
      const response = await fetch(
        `/api/products/validate-slug?slug=${encodeURIComponent(
          slug
        )}&currentId=${selectedProduct?.id}`
      );
      const data = await response.json();

      if (data.exists) {
        setSlugValidation({
          isValid: false,
          isChecking: false,
          message: "This slug is already taken. Please choose a different one.",
        });
      } else {
        setSlugValidation({
          isValid: true,
          isChecking: false,
          message: "Slug is available",
        });
      }
    } catch (error) {
      console.error("Error validating slug:", error);
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message: "Error checking slug availability",
      });
    }
  };

  // Update subscription handlers to include change detection
  const handleSubscribeUser = (user: User) => {
    setUnsubscribedUsers((prev) => prev.filter((u) => u.id !== user.id));
    setSubscribedUsers((prev) => {
      const newSubscribed = [...prev, user];
      // Check for changes immediately with the new subscription state
      const currentSubscribedIds = new Set(newSubscribed.map((u) => u.id));
      const originalSubscribedIds = new Set(
        originalSubscribedUsers.map((u) => u.id)
      );
      const subscriptionChanged =
        currentSubscribedIds.size !== originalSubscribedIds.size ||
        [...currentSubscribedIds].some((id) => !originalSubscribedIds.has(id));

      const formChanged = selectedProduct
        ? formData.name !== selectedProduct.name ||
          formData.brand !== selectedProduct.brand ||
          formData.stage !== selectedProduct.stage ||
          formData.slug !== selectedProduct.slug
        : false;

      setHasUnsavedChanges(formChanged || subscriptionChanged);
      return newSubscribed;
    });
  };

  const handleUnsubscribeUser = (user: User) => {
    setSubscribedUsers((prev) => {
      const newSubscribed = prev.filter((u) => u.id !== user.id);
      // Check for changes immediately with the new subscription state
      const currentSubscribedIds = new Set(newSubscribed.map((u) => u.id));
      const originalSubscribedIds = new Set(
        originalSubscribedUsers.map((u) => u.id)
      );
      const subscriptionChanged =
        currentSubscribedIds.size !== originalSubscribedIds.size ||
        [...currentSubscribedIds].some((id) => !originalSubscribedIds.has(id));

      const formChanged = selectedProduct
        ? formData.name !== selectedProduct.name ||
          formData.brand !== selectedProduct.brand ||
          formData.stage !== selectedProduct.stage ||
          formData.slug !== selectedProduct.slug
        : false;

      setHasUnsavedChanges(formChanged || subscriptionChanged);
      return newSubscribed;
    });
    setUnsubscribedUsers((prev) => [...prev, user]);
  };

  const handleSave = async () => {
    if (!selectedProduct) return;

    // Validate form before saving
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      toast.error("Please fix the following errors:", {
        description: validationErrors.join(", "),
      });
      return;
    }

    // Check slug validation
    if (!slugValidation.isValid && formData.slug !== selectedProduct.slug) {
      toast.error("Please fix the slug error before saving");
      return;
    }

    setIsLoading(true);
    try {
      // Determine what has changed
      const updates: Parameters<typeof updateProductSettings>[1] = {};

      // Check for basic field changes
      if (formData.brand !== selectedProduct.brand) {
        updates.brand = formData.brand;
      }

      if (formData.stage !== selectedProduct.stage) {
        updates.stage = formData.stage as ProductStage;
      }

      if (formData.name !== selectedProduct.name) {
        updates.name = formData.name;
      }

      if (formData.slug !== selectedProduct.slug) {
        updates.slug = formData.slug;
      }

      if (formData.description !== (selectedProduct.description || "")) {
        updates.description = formData.description;
      }

      if (formData.imageUrl !== (selectedProduct.imageUrl || "")) {
        updates.imageUrl = formData.imageUrl;
      }

      // Handle subscription changes
      const currentSubscribedIds = new Set(subscribedUsers.map((u) => u.id));
      const originalSubscribedIds = new Set(
        originalSubscribedUsers.map((u) => u.id)
      );

      // Find users to add (in current but not in original)
      const usersToAdd = subscribedUsers
        .filter((u) => !originalSubscribedIds.has(u.id))
        .map((u) => u.id);
      if (usersToAdd.length > 0) {
        updates.usersToAdd = usersToAdd;
      }

      // Find users to remove (in original but not in current)
      const usersToRemove = originalSubscribedUsers
        .filter((u) => !currentSubscribedIds.has(u.id))
        .map((u) => u.id);
      if (usersToRemove.length > 0) {
        updates.usersToRemove = usersToRemove;
      }

      // Only call the update if there are actual changes
      if (Object.keys(updates).length > 0) {
        await updateProductSettings(selectedProduct.id, updates);
      }

      // Update the original subscribed users to reflect the saved state
      setOriginalSubscribedUsers([...subscribedUsers]);

      setHasUnsavedChanges(false);
      setIsEditMode(false); // Exit edit mode after successful save
      toast.success("Product settings saved successfully", {
        description: "All changes have been applied to the product",
      });
    } catch (error) {
      console.error("Failed to save product settings:", error);

      // Check if it's a slug uniqueness error
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (
        errorMessage.includes("slug") &&
        errorMessage.includes("already exists")
      ) {
        toast.error("Slug already exists", {
          description: errorMessage,
        });
        // Also update the slug validation state to show the error
        setSlugValidation({
          isValid: false,
          isChecking: false,
          message: "This slug is already taken. Please choose a different one.",
        });
      } else {
        toast.error("Failed to save settings", {
          description:
            "Please try again or contact support if the issue persists",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to leave this page?"
      );
      if (!confirmed) return;
    }
    router.back();
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to discard them?"
      );
      if (!confirmed) return;

      // Reset form data to original product values
      if (selectedProduct) {
        setFormData({
          name: selectedProduct.name,
          brand: selectedProduct.brand,
          stage: selectedProduct.stage,
          description: selectedProduct.description || "",
          imageUrl: selectedProduct.imageUrl || "",
          slug: selectedProduct.slug,
        });

        // Reset subscriptions to original state
        setSubscribedUsers([...originalSubscribedUsers]);
        setUnsubscribedUsers(
          users.filter(
            (user) =>
              !originalSubscribedUsers.some((subUser) => subUser.id === user.id)
          )
        );

        // Reset slug validation
        setSlugValidation({ isValid: true, isChecking: false, message: "" });
      }
      setHasUnsavedChanges(false);
    }

    // Exit edit mode
    setIsEditMode(false);
  };

  // Toggle edit mode
  const handleToggleEditMode = () => {
    if (isEditMode && hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to exit edit mode?"
      );
      if (!confirmed) return;

      // Reset to original values if cancelling
      handleCancel();
    } else {
      setIsEditMode(!isEditMode);
    }
  };

  const handleDelete = async () => {
    if (!selectedProduct?.id) {
      toast.error("Product not found");
      return;
    }

    setIsLoading(true);

    try {
      const result = await deleteNPD(selectedProduct.id);

      if (result.success) {
        toast.success(result.message);
        setShowDeleteDialog(false);

        // Clear unsaved changes state since product is being deleted
        setHasUnsavedChanges(false);
        setGlobalUnsavedChanges(false);

        // Navigate back to NPD list after successful deletion
        router.push("/dashboard/npd");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to delete product. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRequest = async () => {
    if (!selectedProduct?.id) {
      toast.error("Product not found");
      return;
    }

    setIsLoading(true);

    try {
      const result = await requestProductDeletion(
        selectedProduct.id,
        deleteRequestReason
      );

      if (result.success) {
        toast.success(result.message);
        setShowDeleteRequestDialog(false);
        setDeleteRequestReason("");
      }
    } catch (error) {
      console.error("Error requesting product deletion:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send deletion request. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can delete product
  const canDeleteProduct = () => {
    if (!selectedProduct || !user) return false;

    // Owner, admin, or super admin can delete
    return selectedProduct.userId === user.userId || isAdmin || isSuperAdmin;
  };

  // Loading state
  if (isLoadingSelectedProduct) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <div className="mb-2">
            <Spinner loading={true} size={12} />
          </div>
          <p className="text-muted-foreground">Loading product settings...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (selectedProductError) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <PackageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Product not found</h1>
          <p className="text-muted-foreground mb-4">{selectedProductError}</p>
          <Button onClick={() => router.push("/dashboard/products")}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  // No product selected
  if (!selectedProduct) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <SettingsIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">No product selected</h1>
          <p className="text-muted-foreground mb-4">
            Please select a product to view its settings
          </p>
          <Button onClick={() => router.push("/dashboard/products")}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="h-9 px-3"
              >
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back
              </Button>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold tracking-tight">
                    Product Settings
                  </h1>
                  {hasUnsavedChanges && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-amber-700 bg-amber-100 rounded-md dark:bg-amber-900/20 dark:text-amber-400">
                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      Unsaved changes
                    </span>
                  )}
                </div>
                <p className="text-muted-foreground">
                  Manage settings for {selectedProduct.name}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {!isEditMode ? (
                <Button onClick={handleToggleEditMode} disabled={isLoading}>
                  <EditIcon className="mr-2 h-4 w-4" />
                  Edit Product
                </Button>
              ) : (
                <>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={
                      isLoading ||
                      (!slugValidation.isValid &&
                        formData.slug !== selectedProduct?.slug)
                    }
                  >
                    {isLoading ? (
                      <Spinner loading={true} size={8} />
                    ) : (
                      <SaveIcon className="mr-2 h-4 w-4" />
                    )}
                    Save Changes
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <div className="p-6 space-y-6">
          {/* Basic Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PackageIcon className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Core product details and identification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Name, Brand, and Stage in one line */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center gap-2">
                    Product Name
                    {isEditMode && formData.name !== selectedProduct?.name && (
                      <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                        Changed
                      </span>
                    )}
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter product name"
                    disabled={!isEditMode}
                    className={!isEditMode ? "bg-muted" : ""}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="brand" className="flex items-center gap-2">
                    Brand
                    {isEditMode &&
                      formData.brand !== selectedProduct?.brand && (
                        <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
                          <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                          Changed
                        </span>
                      )}
                  </Label>
                  <Select
                    value={formData.brand}
                    onValueChange={(value) => handleInputChange("brand", value)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger className={!isEditMode ? "bg-muted" : ""}>
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {BRANDS.map((brand) => (
                        <SelectItem key={brand} value={brand}>
                          {brand}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="stage" className="flex items-center gap-2">
                    Stage
                    {isEditMode &&
                      formData.stage !== selectedProduct?.stage && (
                        <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
                          <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                          Changed
                        </span>
                      )}
                  </Label>
                  <Select
                    value={formData.stage}
                    onValueChange={(value) => handleInputChange("stage", value)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger className={!isEditMode ? "bg-muted" : ""}>
                      <SelectValue placeholder="Select stage" />
                    </SelectTrigger>
                    <SelectContent>
                      {PRODUCT_STAGES.map((stage) => {
                        const stageColor = getProductStageColor(stage.value);
                        return (
                          <SelectItem key={stage.value} value={stage.value}>
                            <div className="flex items-center gap-2">
                              <div
                                className={`w-2 h-2 rounded-full ${stageColor}`}
                              />
                              {stage.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="description"
                  className="flex items-center gap-2"
                >
                  Description
                  {isEditMode &&
                    formData.description !== selectedProduct?.description && (
                      <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                        Changed
                      </span>
                    )}
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Enter product description..."
                  rows={3}
                  disabled={!isEditMode}
                  className={!isEditMode ? "bg-muted" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="imageUrl" className="flex items-center gap-2">
                  Product Image
                  {isEditMode &&
                    formData.imageUrl !== selectedProduct?.imageUrl && (
                      <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
                        <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                        Changed
                      </span>
                    )}
                </Label>
                <div className="space-y-2">
                  <Input
                    id="imageUrl"
                    value={formData.imageUrl}
                    onChange={(e) =>
                      handleInputChange("imageUrl", e.target.value)
                    }
                    placeholder="https://example.com/image.jpg"
                    disabled={!isEditMode}
                    className={!isEditMode ? "bg-muted" : ""}
                  />
                  {isEditMode && (
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-muted-foreground">or</div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        disabled
                        className="flex items-center gap-2"
                      >
                        <UploadIcon className="h-4 w-4" />
                        Upload Image
                      </Button>
                      <span className="text-xs text-muted-foreground">
                        (Cloudinary integration coming soon)
                      </span>
                    </div>
                  )}
                  {formData.imageUrl && (
                    <div className="mt-2">
                      <div className="relative w-32 h-32 border rounded-lg overflow-hidden">
                        <Image
                          src={formData.imageUrl}
                          alt="Product preview"
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = "none";
                            const errorDiv =
                              target.parentElement?.querySelector(
                                ".error-placeholder"
                              );
                            if (errorDiv) {
                              errorDiv.classList.remove("hidden");
                            }
                          }}
                        />
                        <div className="error-placeholder hidden absolute inset-0 bg-muted">
                          <div className="flex items-center justify-center h-full">
                            <ImageIcon className="h-8 w-8 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  Product image will be displayed on the product page.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* User Subscriptions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                User Subscriptions
              </CardTitle>
              <CardDescription>
                Manage which users are subscribed to this product.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isLoadingUsers ? (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Spinner loading={true} size={8} />
                  Loading users...
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Currently Subscribed Users Section */}
                  <div>
                    <Label className="text-sm font-medium mb-3 block">
                      Currently Subscribed Users ({subscribedUsers.length})
                    </Label>
                    {subscribedUsers.length === 0 ? (
                      <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
                        <UserIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm font-medium">
                          No users are currently subscribed
                        </p>
                        <p className="text-xs mt-1">
                          Use the checkboxes below to add users to this product
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto border rounded-md p-3 bg-background">
                        {subscribedUsers.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-start space-x-3 p-2 rounded-md bg-muted/50"
                          >
                            <Checkbox
                              id={`subscribed-${user.id}`}
                              checked={true}
                              onCheckedChange={() =>
                                handleUnsubscribeUser(user)
                              }
                              disabled={!isEditMode}
                            />
                            <div className="grid gap-1.5 leading-none">
                              <Label
                                htmlFor={`subscribed-${user.id}`}
                                className="text-sm font-normal cursor-pointer"
                              >
                                {user.name}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Available Users Section */}
                  <div>
                    <Label className="text-sm font-medium mb-3 block">
                      Available Users ({unsubscribedUsers.length})
                    </Label>
                    {unsubscribedUsers.length === 0 ? (
                      <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
                        <UserIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm font-medium">
                          All users are subscribed
                        </p>
                        <p className="text-xs mt-1">
                          Great! Everyone has access to this product
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-48 overflow-y-auto border rounded-md p-3 bg-background">
                        {unsubscribedUsers.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted/50 transition-colors"
                          >
                            <Checkbox
                              id={`unsubscribed-${user.id}`}
                              checked={false}
                              onCheckedChange={() => handleSubscribeUser(user)}
                              disabled={!isEditMode}
                            />
                            <div className="grid gap-1.5 leading-none">
                              <Label
                                htmlFor={`unsubscribed-${user.id}`}
                                className="text-sm font-normal cursor-pointer"
                              >
                                {user.name}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Danger Zone Card */}
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <TrashIcon className="h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  {canDeleteProduct()
                    ? "You have permission to archive this product. Archived products can be restored by administrators."
                    : "You can request product deletion. Administrators and the product owner will be notified."}
                </div>

                {canDeleteProduct() ? (
                  <AlertDialog
                    open={showDeleteDialog}
                    onOpenChange={setShowDeleteDialog}
                  >
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={isLoading}
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        Archive Product
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Archive Product?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will archive the product &quot;
                          {selectedProduct.name}&quot;. The product will be
                          hidden from users but can be restored by
                          administrators.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="space-y-3">
                        <div className="text-sm text-muted-foreground">
                          What happens when you archive:
                        </div>
                        <ul className="list-disc list-inside text-sm space-y-1 ml-4 text-muted-foreground">
                          <li>Product becomes invisible to all users</li>
                          <li>All data is preserved (tabs, activity, chat)</li>
                          <li>Product can be restored by administrators</li>
                          <li>URL slug is reserved to prevent conflicts</li>
                        </ul>
                        <div className="text-sm font-medium text-green-600 dark:text-green-400">
                          No data will be permanently lost.
                        </div>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel disabled={isLoading}>
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          disabled={isLoading}
                          className="bg-destructive text-white hover:bg-destructive/90"
                        >
                          {isLoading ? (
                            <>
                              <Spinner loading={true} size={8} />
                              <span className="ml-2">Archiving...</span>
                            </>
                          ) : (
                            "Archive Product"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                ) : (
                  <AlertDialog
                    open={showDeleteRequestDialog}
                    onOpenChange={setShowDeleteRequestDialog}
                  >
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={isLoading}
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        Request Deletion
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Request Product Deletion
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          Send a deletion request for &quot;
                          {selectedProduct.name}&quot; to administrators and the
                          product owner.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="deleteReason">
                            Reason (optional)
                          </Label>
                          <Textarea
                            id="deleteReason"
                            value={deleteRequestReason}
                            onChange={(e) =>
                              setDeleteRequestReason(e.target.value)
                            }
                            placeholder="Why should this product be deleted?"
                            rows={3}
                          />
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Notifications will be sent to:
                        </div>
                        <ul className="list-disc list-inside text-sm space-y-1 ml-4 text-muted-foreground">
                          <li>All administrators</li>
                          <li>The product owner (if different from you)</li>
                        </ul>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel disabled={isLoading}>
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteRequest}
                          disabled={isLoading}
                          className="bg-destructive text-white hover:bg-destructive/90"
                        >
                          {isLoading ? (
                            <>
                              <Spinner loading={true} size={8} />
                              <span className="ml-2">Sending...</span>
                            </>
                          ) : (
                            "Send Request"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
