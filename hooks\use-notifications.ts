"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";

export interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, unknown> | null;
  read: boolean;
  hidden: boolean;
  createdAt: string;
}

interface UseNotificationsOptions {
  enabled?: boolean;
  limit?: number;
  unreadOnly?: boolean;
  includeHidden?: boolean;
}

export function useNotifications(options: UseNotificationsOptions = {}) {
  // Mark notifications as unread
  const markAsUnread = useCallback(async (notificationIds: string[]) => {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notificationIds, action: "unread" }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark notifications as unread");
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notificationIds.includes(notification.id)
            ? { ...notification, read: false }
            : notification
        )
      );

      // Update unread count
      setUnreadCount((prev) => prev + notificationIds.length);
    } catch (err) {
      console.error("Error marking notifications as unread:", err);
      throw err;
    }
  }, []);
  const {
    enabled = true,
    limit = 10,
    unreadOnly = false,
    includeHidden = false,
  } = options;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);

  // Fetch notifications from API
  const fetchNotifications = useCallback(
    async (silent = false) => {
      if (!enabled || isPollingRef.current) return;

      isPollingRef.current = true;

      if (!silent) {
        setIsLoading(true);
      }

      try {
        const params = new URLSearchParams({
          limit: limit.toString(),
          silent: silent.toString(),
          ...(unreadOnly && { unreadOnly: "true" }),
          ...(includeHidden && { includeHidden: "true" }),
        });

        const response = await fetch(`/api/notifications?${params}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `Notifications API error (${response.status}):`,
            errorText
          );
          throw new Error(
            `Failed to fetch notifications: ${response.status} ${response.statusText}`
          );
        }

        const data = await response.json();
        const fetchedNotifications = data.notifications || [];

        setNotifications(fetchedNotifications);

        // Calculate unread count
        const unread = fetchedNotifications.filter(
          (n: Notification) => !n.read
        ).length;
        setUnreadCount(unread);

        setError(null);
      } catch (err) {
        console.error("Error fetching notifications:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch notifications"
        );
      } finally {
        setIsLoading(false);
        isPollingRef.current = false;
      }
    },
    [enabled, limit, unreadOnly, includeHidden]
  );

  // Mark notifications as read
  const markAsRead = useCallback(async (notificationIds: string[]) => {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notificationIds }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark notifications as read");
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notificationIds.includes(notification.id)
            ? { ...notification, read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - notificationIds.length));
    } catch (err) {
      console.error("Error marking notifications as read:", err);
      throw err;
    }
  }, []);

  // Mark single notification as read
  const markNotificationAsRead = useCallback(
    async (notificationId: string) => {
      await markAsRead([notificationId]);
    },
    [markAsRead]
  );

  // Hide/unhide notifications
  const hideNotifications = useCallback(async (notificationIds: string[]) => {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notificationIds, action: "hide" }),
      });

      if (!response.ok) {
        throw new Error("Failed to hide notifications");
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notificationIds.includes(notification.id)
            ? { ...notification, hidden: true }
            : notification
        )
      );
    } catch (err) {
      console.error("Error hiding notifications:", err);
      throw err;
    }
  }, []);

  const unhideNotifications = useCallback(async (notificationIds: string[]) => {
    try {
      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notificationIds, action: "unhide" }),
      });

      if (!response.ok) {
        throw new Error("Failed to unhide notifications");
      }

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notificationIds.includes(notification.id)
            ? { ...notification, hidden: false }
            : notification
        )
      );
    } catch (err) {
      console.error("Error unhiding notifications:", err);
      throw err;
    }
  }, []);

  // Hide single notification
  const hideNotification = useCallback(
    async (notificationId: string) => {
      await hideNotifications([notificationId]);
    },
    [hideNotifications]
  );

  // Unhide single notification
  const unhideNotification = useCallback(
    async (notificationId: string) => {
      await unhideNotifications([notificationId]);
    },
    [unhideNotifications]
  );

  // Refresh notifications manually
  const refresh = useCallback(() => {
    fetchNotifications(false);
  }, [fetchNotifications]);

  // Force immediate refresh (bypasses polling checks)
  const forceRefresh = useCallback(() => {
    isPollingRef.current = false; // Reset polling flag
    fetchNotifications(false);
  }, [fetchNotifications]);

  // Set up polling
  useEffect(() => {
    if (!enabled || !isPollingEnabled("NOTIFICATIONS")) {
      return;
    }

    // Initial fetch
    fetchNotifications(false);

    // Set up polling interval
    const interval = getPollingInterval("NOTIFICATIONS");
    pollingIntervalRef.current = setInterval(() => {
      fetchNotifications(true); // Silent polling updates
    }, interval);

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [enabled, fetchNotifications]);

  // Pause polling when tab is not visible (optional optimization)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
      } else if (enabled && isPollingEnabled("NOTIFICATIONS")) {
        // Resume polling when tab becomes visible
        const interval = getPollingInterval("NOTIFICATIONS");
        pollingIntervalRef.current = setInterval(() => {
          fetchNotifications(true);
        }, interval);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [enabled, fetchNotifications]);

  // Listen for custom refresh events (e.g., from auto-subscription)
  useEffect(() => {
    const handleRefreshEvent = () => {
      forceRefresh();
    };

    window.addEventListener("refreshNotifications", handleRefreshEvent);

    return () => {
      window.removeEventListener("refreshNotifications", handleRefreshEvent);
    };
  }, [forceRefresh]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markNotificationAsRead,
    markAsUnread,
    refresh,
    forceRefresh,
    hideNotifications,
    unhideNotifications,
    hideNotification,
    unhideNotification,
  };
}
