"use server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";
import { revalidatePath } from "next/cache";

// Types for activity operations
export interface ActivityLog {
  id: string;
  productTabId: string | null;
  productId: string;
  userId: string | null;
  action: string;
  description: string;
  changes: Record<string, unknown> | null;
  metadata: Record<string, unknown> | null;
  createdAt: string;
  isRead: boolean;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string | null;
  } | null;
  productTab?: {
    id: string;
    tabName: string;
  } | null;
}

export interface ActivityUnreadData {
  unreadCount: number;
  totalCount: number;
  readCount: number;
}

// Get activity logs with read status for a product
export async function getActivityLogsWithReadStatus(
  productId: string
): Promise<ActivityLog[]> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!productId) {
    throw new Error("productId is required");
  }

  // Optimized query: Get activity logs and read status in parallel
  const [activityLogs, readStatuses] = await Promise.all([
    prisma.nPDProductActivityLog.findMany({
      where: { npdProductId: productId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        npdProductTab: {
          select: {
            id: true,
            tabName: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    }),
    prisma.nPDProductActivityReadStatus.findMany({
      where: {
        userId: user.userId,
        npdProductId: productId,
      },
      select: {
        activityId: true,
        readAt: true,
      },
    }),
  ]);

  // Create a Map for O(1) lookup performance
  const readStatusMap = new Map(
    readStatuses.map((status) => [status.activityId, status.readAt])
  );

  // Transform to include isRead status using the Map for efficient lookup
  const logsWithReadStatus: ActivityLog[] = activityLogs.map((log) => ({
    id: log.id,
    productTabId: log.npdProductTabId,
    productId: log.npdProductId,
    userId: log.userId,
    action: log.action,
    description: log.description,
    changes: log.changes as Record<string, unknown> | null,
    metadata: log.metadata as Record<string, unknown> | null,
    createdAt: log.createdAt.toISOString(),
    isRead: readStatusMap.has(log.id), // Efficient O(1) lookup
    user: log.user,
    productTab: log.npdProductTab,
  }));

  return logsWithReadStatus;
}

// Get unread count for activity logs in a product
export async function getActivityUnreadCount(
  productId: string
): Promise<ActivityUnreadData> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!productId) {
    throw new Error("productId is required");
  }

  // Get total activity logs count
  const totalCount = await prisma.nPDProductActivityLog.count({
    where: { npdProductId: productId },
  });

  // Get read activity logs count for this user
  const readCount = await prisma.nPDProductActivityReadStatus.count({
    where: {
      userId: user.userId,
      npdProductId: productId,
    },
  });

  const unreadCount = Math.max(0, totalCount - readCount);

  return {
    unreadCount,
    totalCount,
    readCount,
  };
}

// Mark activity log as read or unread
export async function updateActivityReadStatus(
  activityId: string,
  productId: string,
  isRead: boolean
): Promise<void> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!activityId || !productId) {
    throw new Error("activityId and productId are required");
  }

  // Verify the activity log exists and belongs to the specified product
  const activityLog = await prisma.nPDProductActivityLog.findFirst({
    where: {
      id: activityId,
      npdProductId: productId,
    },
    include: {
      npdProduct: {
        select: {
          slug: true,
        },
      },
    },
  });

  if (!activityLog) {
    throw new Error("Activity log not found");
  }

  if (isRead) {
    // Mark as read - create or update read status
    await prisma.nPDProductActivityReadStatus.upsert({
      where: {
        userId_activityId: {
          userId: user.userId,
          activityId: activityId,
        },
      },
      update: {
        readAt: new Date(),
      },
      create: {
        userId: user.userId,
        activityId: activityId,
        npdProductId: productId,
        readAt: new Date(),
      },
    });
  } else {
    // Mark as unread - delete read status
    await prisma.nPDProductActivityReadStatus.deleteMany({
      where: {
        userId: user.userId,
        activityId: activityId,
      },
    });
  }

  // Revalidate the product page
  revalidatePath(`/dashboard/products/${activityLog.npdProduct.slug}`);
}

// Mark all activity logs as read for a product
export async function markAllActivityLogsAsRead(
  productId: string
): Promise<void> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!productId) {
    throw new Error("productId is required");
  }

  // Verify product exists
  const product = await prisma.nPDProduct.findUnique({
    where: { id: productId },
    select: { slug: true },
  });

  if (!product) {
    throw new Error("Product not found");
  }

  // Get all activity logs for this product
  const activityLogs = await prisma.nPDProductActivityLog.findMany({
    where: { npdProductId: productId },
    select: { id: true },
  });

  // Create read status for each activity log that doesn't have one
  const readStatusData = activityLogs.map((log) => ({
    userId: user.userId,
    activityId: log.id,
    npdProductId: productId,
    readAt: new Date(),
  }));

  // Use createMany with skipDuplicates to avoid conflicts
  await prisma.nPDProductActivityReadStatus.createMany({
    data: readStatusData,
    skipDuplicates: true,
  });

  // Revalidate the product page
  revalidatePath(`/dashboard/products/${product.slug}`);
}

// Get unread counts for multiple products (for global polling)
export async function getActivityUnreadCountsForProducts(
  productIds: string[]
): Promise<{ unreadCounts: Record<string, number>; totalUnread: number }> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!productIds || productIds.length === 0) {
    return { unreadCounts: {}, totalUnread: 0 };
  }

  try {
    // Get unread counts for each product
    const unreadCounts: Record<string, number> = {};
    let totalUnread = 0;

    for (const productId of productIds) {
      const result = await getActivityUnreadCount(productId);
      unreadCounts[productId] = result.unreadCount;
      totalUnread += result.unreadCount;
    }

    return { unreadCounts, totalUnread };
  } catch (error) {
    console.error("Error fetching activity unread counts for products:", error);
    throw new Error("Failed to fetch unread counts");
  }
}

// Mark multiple activity logs as read
export async function markMultipleActivityLogsAsRead(
  activityIds: string[],
  productId: string
): Promise<void> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!activityIds || activityIds.length === 0 || !productId) {
    throw new Error("activityIds and productId are required");
  }

  // Verify product exists
  const product = await prisma.nPDProduct.findUnique({
    where: { id: productId },
    select: { slug: true },
  });

  if (!product) {
    throw new Error("Product not found");
  }

  try {
    // Create read status for each activity log
    const readStatusData = activityIds.map((activityId) => ({
      userId: user.userId,
      activityId: activityId,
      npdProductId: productId,
      readAt: new Date(),
    }));

    // Use createMany with skipDuplicates to avoid conflicts
    await prisma.nPDProductActivityReadStatus.createMany({
      data: readStatusData,
      skipDuplicates: true,
    });

    // Revalidate the product page
    revalidatePath(`/dashboard/products/${product.slug}`);
  } catch (error) {
    console.error("Error marking multiple activity logs as read:", error);
    throw new Error("Failed to mark activities as read");
  }
}

// Mark multiple activity logs as unread
export async function markMultipleActivityLogsAsUnread(
  activityIds: string[]
): Promise<void> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  if (!activityIds || activityIds.length === 0) {
    throw new Error("activityIds are required");
  }

  try {
    // Delete read status for these activities
    await prisma.nPDProductActivityReadStatus.deleteMany({
      where: {
        userId: user.userId,
        activityId: { in: activityIds },
      },
    });

    // Note: We can't easily revalidate specific product pages here since we don't know which products
    // The global polling will pick up the changes
  } catch (error) {
    console.error("Error marking multiple activity logs as unread:", error);
    throw new Error("Failed to mark activities as unread");
  }
}
