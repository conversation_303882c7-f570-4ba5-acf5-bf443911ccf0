"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";
import { createProductActivityLog } from "@/actions/activity-log.actions";

/**
 * Request product deletion - sends notification to admins and product owner
 */
export async function requestProductDeletion(
  npdProductId: string,
  reason?: string
) {
  const user = await validateServerAction();

  try {
    // Get product details
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    if (product.archived) {
      throw new Error("Product is already archived");
    }

    // Get all admins and super admins
    const admins = await prisma.user.findMany({
      where: {
        roles: {
          some: {
            name: {
              in: ["ADMIN", "SUPER_ADMIN"],
            },
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    // Create notifications for admins and product owner
    const notificationRecipients = [...admins];

    // Add product owner if they exist and aren't already in the list
    if (
      product.user &&
      !admins.some((admin) => admin.id === product.user!.id)
    ) {
      notificationRecipients.push(product.user);
    }

    // Remove the requesting user from recipients
    const filteredRecipients = notificationRecipients.filter(
      (recipient) => recipient.id !== user.userId
    );

    // Create notifications
    const notifications = filteredRecipients.map((recipient) => ({
      userId: recipient.id,
      type: "PRODUCT_DELETE_REQUEST",
      title: "Product Deletion Request",
      message: `${user.name} has requested deletion of product "${
        product.name
      }"${reason ? `: ${reason}` : ""}`,
      data: {
        npdProductId: product.id,
        productName: product.name,
        productSlug: product.slug,
        requestedBy: user.userId,
        requestedByName: user.name,
        reason: reason || null,
      },
    }));

    if (notifications.length > 0) {
      await prisma.notification.createMany({
        data: notifications,
      });
    }

    // Create activity log
    await createProductActivityLog({
      npdProductId,
      userId: user.userId,
      action: "deletion_requested",
      description: `Requested product deletion${reason ? `: ${reason}` : ""}`,
      metadata: {
        field: "deletion_request",
        reason: reason || null,
        notificationsSent: notifications.length,
      },
    });

    return {
      success: true,
      message: "Deletion request sent to administrators and product owner",
      notificationsSent: notifications.length,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Archive product (soft delete) - only for admins and product owners
 */
export async function archiveProduct(npdProductId: string) {
  const user = await validateServerAction();

  try {
    // Get product details
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: {
        id: true,
        name: true,
        slug: true,
        userId: true,
        archived: true,
      },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    if (product.archived) {
      throw new Error("Product is already archived");
    }

    // Check permissions
    const hasAdminRole =
      user.roles.includes("ADMIN") || user.roles.includes("SUPER_ADMIN");
    const isOwner = product.userId === user.userId;

    if (!isOwner && !hasAdminRole) {
      throw new Error("You don't have permission to archive this product");
    }

    // Archive the product
    await prisma.nPDProduct.update({
      where: { id: npdProductId },
      data: {
        archived: true,
        archivedAt: new Date(),
        archivedBy: user.userId,
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId,
      userId: user.userId,
      action: "archived",
      description: `Archived product "${product.name}"`,
      metadata: {
        field: "archived",
        archivedAt: new Date().toISOString(),
        archivedBy: user.userId,
      },
    });

    return {
      success: true,
      message: `Product "${product.name}" has been archived`,
      productName: product.name,
    };
  } catch (error) {
    console.error("Error archiving product:", error);
    throw error;
  }
}

/**
 * Restore archived product - only for admins
 */
export async function restoreProduct(npdProductId: string) {
  const user = await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    // Get product details
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: {
        id: true,
        name: true,
        slug: true,
        archived: true,
      },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    if (!product.archived) {
      throw new Error("Product is not archived");
    }

    // Check if slug is available (in case another product took it)
    const existingProduct = await prisma.nPDProduct.findFirst({
      where: {
        slug: product.slug,
        archived: false,
      },
    });

    if (existingProduct) {
      throw new Error(
        `Cannot restore: slug "${product.slug}" is already in use by another product`
      );
    }

    // Restore the product
    await prisma.nPDProduct.update({
      where: { id: npdProductId },
      data: {
        archived: false,
        archivedAt: null,
        archivedBy: null,
      },
    });

    // Create activity log
    await createProductActivityLog({
      npdProductId,
      userId: user.userId,
      action: "restored",
      description: `Restored product "${product.name}" from archive`,
      metadata: {
        field: "restored",
        restoredAt: new Date().toISOString(),
        restoredBy: user.userId,
      },
    });

    return {
      success: true,
      message: `Product "${product.name}" has been restored`,
      productName: product.name,
    };
  } catch (error) {
    console.error("Error restoring product:", error);
    throw error;
  }
}

/**
 * Permanently delete product - only for super admins
 */
export async function permanentlyDeleteProduct(npdProductId: string) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    // Get product details
    const product = await prisma.nPDProduct.findUnique({
      where: { id: npdProductId },
      select: {
        id: true,
        name: true,
        slug: true,
        archived: true,
      },
    });

    if (!product) {
      throw new Error("Product not found");
    }

    // Use the existing deleteProduct logic but without permission checks
    // since we already validated super admin access
    await prisma.$transaction(async (tx) => {
      // Delete chat read status records
      await tx.nPDProductChatReadStatus.deleteMany({
        where: {
          chat: {
            npdProductId: product.id,
          },
        },
      });

      // Delete product chat messages
      await tx.nPDProductChat.deleteMany({
        where: { npdProductId: product.id },
      });

      // Delete product tabs
      await tx.nPDProductTab.deleteMany({
        where: { npdProductId: product.id },
      });

      // Delete the product
      await tx.nPDProduct.delete({
        where: { id: product.id },
      });
    });

    return {
      success: true,
      message: `Product "${product.name}" has been permanently deleted`,
      productName: product.name,
    };
  } catch (error) {
    console.error("Error permanently deleting product:", error);
    throw error;
  }
}
