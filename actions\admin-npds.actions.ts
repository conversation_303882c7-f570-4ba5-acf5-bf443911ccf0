"use server";

import { prisma } from "@/lib/db/prisma";
import { validateServerAction } from "@/lib/server-auth";

/**
 * Fetch all archived products - only for admins
 */
export async function fetchArchivedProducts() {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const archivedProducts = await prisma.nPDProduct.findMany({
      where: {
        archived: true,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            tabs: true,
            activityLogs: true,
            chats: true,
            subscriptions: true,
          },
        },
      },
      orderBy: {
        archivedAt: "desc",
      },
    });

    return archivedProducts;
  } catch (error) {
    throw error;
  }
}

/**
 * Get archived product statistics - only for admins
 */
export async function getArchivedProductStats() {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const [totalArchived, archivedThisMonth, archivedByUser] =
      await Promise.all([
        // Total archived NPDs
        prisma.nPDProduct.count({
          where: {
            archived: true,
          },
        }),

        // Archived this month
        prisma.nPDProduct.count({
          where: {
            archived: true,
            archivedAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
        }),

        // Archived NPDs by user
        prisma.nPDProduct.groupBy({
          by: ["archivedBy"],
          where: {
            archived: true,
            archivedBy: {
              not: null,
            },
          },
          _count: {
            id: true,
          },
          orderBy: {
            _count: {
              id: "desc",
            },
          },
          take: 5,
        }),
      ]);

    // Get user details for the top archivers
    const archivedByUserIds = archivedByUser
      .map((item) => item.archivedBy)
      .filter(Boolean);
    const users = await prisma.user.findMany({
      where: {
        id: {
          in: archivedByUserIds as string[],
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    const archivedByUserWithDetails = archivedByUser.map((item) => {
      const user = users.find((u) => u.id === item.archivedBy);
      return {
        userId: item.archivedBy,
        userName: user?.name || "Unknown",
        userEmail: user?.email || "Unknown",
        count: item._count.id,
      };
    });

    return {
      totalArchived,
      archivedThisMonth,
      archivedByUser: archivedByUserWithDetails,
    };
  } catch (error) {
    console.error("Error fetching archived product stats:", error);
    throw error;
  }
}

/**
 * Search archived products - only for admins
 */
export async function searchArchivedProducts(query: string) {
  await validateServerAction(["ADMIN", "SUPER_ADMIN"]);

  try {
    const archivedProducts = await prisma.nPDProduct.findMany({
      where: {
        archived: true,
        OR: [
          {
            name: {
              contains: query,
              mode: "insensitive",
            },
          },
          {
            brand: {
              contains: query,
              mode: "insensitive",
            },
          },
          {
            slug: {
              contains: query,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: query,
              mode: "insensitive",
            },
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        archivedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            tabs: true,
            activityLogs: true,
            chats: true,
            subscriptions: true,
          },
        },
      },
      orderBy: {
        archivedAt: "desc",
      },
      take: 50, // Limit results
    });

    return archivedProducts;
  } catch (error) {
    throw error;
  }
}

/**
 * Bulk restore products - only for super admins
 */
export async function bulkRestoreProducts(productIds: string[]) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    // Check if any NPDs have slug conflicts
    const products = await prisma.nPDProduct.findMany({
      where: {
        id: {
          in: productIds,
        },
        archived: true,
      },
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });

    // Check for slug conflicts
    const slugs = products.map((p) => p.slug);
    const existingProducts = await prisma.nPDProduct.findMany({
      where: {
        slug: {
          in: slugs,
        },
        archived: false,
      },
      select: {
        slug: true,
      },
    });

    if (existingProducts.length > 0) {
      const conflictingSlugs = existingProducts.map((p) => p.slug);
      throw new Error(
        `Cannot restore: slugs already in use: ${conflictingSlugs.join(", ")}`
      );
    }

    // Restore NPDs
    const restoredProducts = await prisma.nPDProduct.updateMany({
      where: {
        id: {
          in: productIds,
        },
        archived: true,
      },
      data: {
        archived: false,
        archivedAt: null,
        archivedBy: null,
      },
    });

    return {
      success: true,
      message: `Successfully restored ${restoredProducts.count} products`,
      restoredCount: restoredProducts.count,
    };
  } catch (error) {
    console.error("Error bulk restoring products:", error);
    throw error;
  }
}

/**
 * Bulk permanently delete products - only for super admins
 */
export async function bulkPermanentlyDeleteProducts(productIds: string[]) {
  await validateServerAction(["SUPER_ADMIN"]);

  try {
    let deletedCount = 0;

    // Delete each product in a transaction
    for (const productId of productIds) {
      await prisma.$transaction(async (tx) => {
        // Delete chat read status records
        await tx.nPDProductChatReadStatus.deleteMany({
          where: {
            chat: {
              npdProductId: productId,
            },
          },
        });

        // Delete NPD chat messages
        await tx.nPDProductChat.deleteMany({
          where: { npdProductId: productId },
        });

        // Delete NPD tabs
        await tx.nPDProductTab.deleteMany({
          where: { npdProductId: productId },
        });

        // Delete the NPD
        await tx.nPDProduct.delete({
          where: { id: productId },
        });

        deletedCount++;
      });
    }

    return {
      success: true,
      message: `Successfully permanently deleted ${deletedCount} products`,
      deletedCount,
    };
  } catch (error) {
    console.error("Error bulk deleting products:", error);
    throw error;
  }
}
