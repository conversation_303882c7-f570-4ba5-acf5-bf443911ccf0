"use server";

import {
  createNextStepAssignmentNotification,
  createDeletionRequestNotification,
  getUserIdsByUsernames,
} from "@/lib/utils/overview-notifications";
import { validateServerAction } from "@/lib/server-auth";

/**
 * Server action to send assignment notifications for Next Steps
 */
export async function sendAssignmentNotifications(
  assigneeUsernames: string[],
  stepTitle: string,
  productId: string,
  productName: string,
  productSlug: string,
  tabId: string,
  stepId: string
) {
  const user = await validateServerAction();

  try {
    // Get user IDs for assignees
    const userMap = await getUserIdsByUsernames(assigneeUsernames);
    const assigneeUserIds = assigneeUsernames
      .map((username) => userMap[username.toLowerCase()])
      .filter(Boolean);

    if (assigneeUserIds.length > 0) {
      await createNextStepAssignmentNotification(
        assigneeUserIds,
        user.name,
        stepTitle,
        productId,
        productName,
        productSlug,
        tabId,
        stepId
      );

      return {
        success: true,
        message: `Assignment notifications sent to ${assigneeUserIds.length} user(s)`,
      };
    } else {
      return {
        success: false,
        message: "No valid users found for assignment",
      };
    }
  } catch (error) {
    console.error("Error sending assignment notifications:", error);
    return {
      success: false,
      message: "Failed to send assignment notifications",
    };
  }
}

/**
 * Server action to send deletion request notification
 */
export async function sendDeletionRequestNotification(
  creatorUsername: string,
  entryType: "key-action" | "next-step" | "dependency" | "milestone",
  entryTitle: string,
  productId: string,
  productName: string,
  productSlug: string,
  tabId: string,
  entryId: string
) {
  const user = await validateServerAction();

  try {
    // Get creator's user ID
    const userMap = await getUserIdsByUsernames([creatorUsername]);
    const creatorUserId = userMap[creatorUsername.toLowerCase()];

    if (creatorUserId) {
      await createDeletionRequestNotification(
        creatorUserId,
        user.name,
        entryType,
        entryTitle,
        productId,
        productName,
        productSlug,
        tabId,
        entryId
      );

      return {
        success: true,
        message: `Deletion request sent to @${creatorUsername}`,
      };
    } else {
      return {
        success: false,
        message: `Could not find user @${creatorUsername}`,
      };
    }
  } catch (error) {
    console.error("Error sending deletion request:", error);
    return {
      success: false,
      message: "Failed to send deletion request",
    };
  }
}
