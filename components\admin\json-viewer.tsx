"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronDown,
  ChevronRight,
  Edit,
  Save,
  X,
  FileText,
  Database,
  Users,
  Package,
} from "lucide-react";

interface JsonViewerProps {
  data: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  onDataChange: (newData: any) => void; // eslint-disable-line @typescript-eslint/no-explicit-any
  isEditing: boolean;
}

interface ExportMetadata {
  exportedAt: string;
  version: string;
  totalRecords: number;
  models: Record<string, number>;
}

interface TreeNodeProps {
  keyName: string;
  value: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  level: number;
  path: string[];
  onValueChange: (path: string[], newValue: unknown) => void;
  isEditing: boolean;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  keyName,
  value,
  level,
  path,
  onValueChange,
  isEditing,
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Auto-expand first 2 levels
  const [isEditingValue, setIsEditingValue] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const isObject =
    typeof value === "object" && value !== null && !Array.isArray(value);
  const isArray = Array.isArray(value);
  const isPrimitive = !isObject && !isArray;

  const getValueType = () => {
    if (isArray) return "array";
    if (isObject) return "object";
    if (typeof value === "string") return "string";
    if (typeof value === "number") return "number";
    if (typeof value === "boolean") return "boolean";
    return "null";
  };

  const handleSaveValue = () => {
    let parsedValue = tempValue;

    // Try to parse the value based on the original type
    if (typeof value === "number" && typeof tempValue === "string") {
      parsedValue = parseFloat(tempValue) || 0;
    } else if (typeof value === "boolean" && typeof tempValue === "string") {
      parsedValue = tempValue.toLowerCase() === "true";
    }

    onValueChange(path, parsedValue);
    setIsEditingValue(false);
  };

  const handleCancelEdit = () => {
    setTempValue(value);
    setIsEditingValue(false);
  };

  const getIcon = () => {
    if (keyName === "metadata") return <FileText className="h-4 w-4" />;
    if (keyName === "data") return <Database className="h-4 w-4" />;
    if (keyName.includes("user") || keyName.includes("User"))
      return <Users className="h-4 w-4" />;
    if (keyName.includes("product") || keyName.includes("Product"))
      return <Package className="h-4 w-4" />;
    return null;
  };

  return (
    <div className="select-none">
      <div
        className={`flex items-center gap-2 py-1 px-2 rounded hover:bg-gray-50 ${
          level === 0 ? "font-semibold" : ""
        }`}
        style={{ paddingLeft: `${level * 20 + 8}px` }}
      >
        {/* Expand/Collapse Button */}
        {(isObject || isArray) && (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        )}

        {/* Icon */}
        {level <= 1 && getIcon()}

        {/* Key Name */}
        <span
          className={`font-medium ${
            level === 0 ? "text-blue-700" : "text-gray-700"
          }`}
        >
          {keyName}
        </span>

        {/* Value Type Badge */}
        <Badge variant="outline" className="text-xs">
          {getValueType()}
          {isArray && ` (${value.length})`}
          {isObject && ` (${Object.keys(value).length})`}
        </Badge>

        {/* Primitive Value Display/Edit */}
        {isPrimitive && (
          <div className="flex items-center gap-2 ml-auto">
            {isEditingValue && isEditing ? (
              <div className="flex items-center gap-1">
                <Input
                  value={tempValue}
                  onChange={(e) => setTempValue(e.target.value)}
                  className="h-6 text-xs w-32"
                  type={typeof value === "number" ? "number" : "text"}
                />
                <Button
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={handleSaveValue}
                >
                  <Save className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 w-6 p-0"
                  onClick={handleCancelEdit}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <span
                  className={`text-sm ${
                    typeof value === "string"
                      ? "text-green-600"
                      : typeof value === "number"
                      ? "text-blue-600"
                      : typeof value === "boolean"
                      ? "text-purple-600"
                      : "text-gray-500"
                  }`}
                >
                  {typeof value === "string" ? `"${value}"` : String(value)}
                </span>
                {isEditing && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-4 w-4 p-0"
                    onClick={() => setIsEditingValue(true)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Child Nodes */}
      {isExpanded && (isObject || isArray) && (
        <div>
          {isObject &&
            Object.entries(value).map(([key, val]) => (
              <TreeNode
                key={key}
                keyName={key}
                value={val}
                level={level + 1}
                path={[...path, key]}
                onValueChange={onValueChange}
                isEditing={isEditing}
              />
            ))}
          {isArray &&
            value.map((val: unknown, index: number) => (
              <TreeNode
                key={index}
                keyName={`[${index}]`}
                value={val}
                level={level + 1}
                path={[...path, index.toString()]}
                onValueChange={onValueChange}
                isEditing={isEditing}
              />
            ))}
        </div>
      )}
    </div>
  );
};

const MetadataEditor: React.FC<{
  metadata: ExportMetadata;
  onMetadataChange: (newMetadata: ExportMetadata) => void;
  isEditing: boolean;
}> = ({ metadata, onMetadataChange, isEditing }) => {
  const [editedMetadata, setEditedMetadata] = useState(metadata);

  const handleChange = (field: string, value: string | number) => {
    const newMetadata = { ...editedMetadata, [field]: value };
    setEditedMetadata(newMetadata);
    onMetadataChange(newMetadata);
  };

  const handleModelCountChange = (model: string, count: number) => {
    const newModels = { ...editedMetadata.models, [model]: count };
    const newMetadata = { ...editedMetadata, models: newModels };
    setEditedMetadata(newMetadata);
    onMetadataChange(newMetadata);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="version">Version</Label>
          <Input
            id="version"
            value={editedMetadata.version || ""}
            onChange={(e) => handleChange("version", e.target.value)}
            disabled={!isEditing}
          />
        </div>
        <div>
          <Label htmlFor="exportedAt">Export Date</Label>
          <Input
            id="exportedAt"
            value={editedMetadata.exportedAt || ""}
            onChange={(e) => handleChange("exportedAt", e.target.value)}
            disabled={!isEditing}
            type="datetime-local"
          />
        </div>
      </div>

      <div>
        <Label>Total Records</Label>
        <Input
          value={editedMetadata.totalRecords || 0}
          onChange={(e) =>
            handleChange("totalRecords", parseInt(e.target.value) || 0)
          }
          disabled={!isEditing}
          type="number"
        />
      </div>

      <div>
        <Label>Records by Model</Label>
        <div className="space-y-2 mt-2">
          {Object.entries(editedMetadata.models || {}).map(([model, count]) => (
            <div key={model} className="flex items-center gap-2">
              <span className="w-32 text-sm font-medium">{model}:</span>
              <Input
                value={count as number}
                onChange={(e) =>
                  handleModelCountChange(model, parseInt(e.target.value) || 0)
                }
                disabled={!isEditing}
                type="number"
                className="w-24 h-8"
              />
              <Badge variant="outline">{count as number} records</Badge>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function JsonViewer({
  data,
  onDataChange,
  isEditing,
}: JsonViewerProps) {
  const [viewMode, setViewMode] = useState<"tree" | "form" | "raw">("tree");

  const handleValueChange = (path: string[], newValue: unknown) => {
    const newData = JSON.parse(JSON.stringify(data)); // Deep clone

    // Navigate to the correct location and set the value
    let current = newData;
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }
    current[path[path.length - 1]] = newValue;

    onDataChange(newData);
  };

  const handleMetadataChange = (newMetadata: ExportMetadata) => {
    const newData = { ...data, metadata: newMetadata };

    // Recalculate total records
    const totalRecords = Object.values(newData.data || {}).reduce(
      (sum: number, records: unknown) =>
        sum + (Array.isArray(records) ? records.length : 0),
      0
    );
    newData.metadata.totalRecords = totalRecords;

    onDataChange(newData);
  };

  return (
    <div className="space-y-4">
      {/* View Mode Selector */}
      <div className="flex gap-2">
        <Button
          variant={viewMode === "tree" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("tree")}
        >
          Tree View
        </Button>
        <Button
          variant={viewMode === "form" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("form")}
        >
          Form View
        </Button>
        <Button
          variant={viewMode === "raw" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("raw")}
        >
          Raw JSON
        </Button>
      </div>

      {/* Content based on view mode */}
      {viewMode === "tree" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Data Structure</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 max-h-96 overflow-y-auto bg-gray-50">
              <TreeNode
                keyName="root"
                value={data}
                level={0}
                path={[]}
                onValueChange={handleValueChange}
                isEditing={isEditing}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {viewMode === "form" && (
        <Tabs defaultValue="metadata" className="w-full">
          <TabsList>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
            <TabsTrigger value="summary">Data Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="metadata">
            <Card>
              <CardHeader>
                <CardTitle>Export Metadata</CardTitle>
              </CardHeader>
              <CardContent>
                <MetadataEditor
                  metadata={data.metadata}
                  onMetadataChange={handleMetadataChange}
                  isEditing={isEditing}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="summary">
            <Card>
              <CardHeader>
                <CardTitle>Data Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(data.data || {}).map(([model, records]) => (
                    <Card key={model} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium capitalize">{model}</h4>
                          <p className="text-sm text-gray-500">
                            {Array.isArray(records) ? records.length : 0}{" "}
                            records
                          </p>
                        </div>
                        <Badge variant="secondary">
                          {Array.isArray(records) ? records.length : 0}
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {viewMode === "raw" && (
        <Card>
          <CardHeader>
            <CardTitle>Raw JSON</CardTitle>
          </CardHeader>
          <CardContent>
            <textarea
              value={JSON.stringify(data, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  onDataChange(parsed);
                } catch {
                  // Invalid JSON, don't update
                }
              }}
              readOnly={!isEditing}
              className={`w-full h-96 p-3 text-sm font-mono border rounded-lg resize-y ${
                isEditing
                  ? "bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  : "bg-gray-50 border-gray-200"
              }`}
              placeholder="JSON content will appear here..."
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
