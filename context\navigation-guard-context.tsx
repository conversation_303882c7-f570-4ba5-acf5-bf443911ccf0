import React, {
  createContext,
  useContext,
  useState,
  useC<PERSON>back,
  useMemo,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";

interface NavigationGuardContextType {
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  navigateWithConfirmation: (url: string) => void;
  navigateWithCustomAction: (action: () => void | Promise<void>) => void;
  onConfirmAction?: () => void;
  setOnConfirmAction: (action?: () => void) => void;
}

const NavigationGuardContext = createContext<NavigationGuardContextType | null>(
  null
);

export function useNavigationGuard() {
  const context = useContext(NavigationGuardContext);
  if (!context) {
    throw new Error(
      "useNavigationGuard must be used within a NavigationGuardProvider"
    );
  }
  return context;
}

interface NavigationGuardProviderProps {
  children: ReactNode;
}

export function NavigationGuardProvider({
  children,
}: NavigationGuardProviderProps) {
  const router = useRouter();
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null
  );
  const [pendingCustomAction, setPendingCustomAction] = useState<
    (() => void | Promise<void>) | null
  >(null);
  const [onConfirmAction, setOnConfirmActionInternal] = useState<
    (() => void) | undefined
  >();

  // Wrapper to properly handle function state updates
  const setOnConfirmAction = useCallback((action?: () => void) => {
    setOnConfirmActionInternal(() => action);
  }, []);

  const navigateWithConfirmation = useCallback(
    (url: string) => {
      if (hasUnsavedChanges) {
        setPendingNavigation(url);
        setPendingCustomAction(null); // Clear any pending custom action
        setShowConfirmDialog(true);
      } else {
        router.push(url);
      }
    },
    [hasUnsavedChanges, router]
  );

  const navigateWithCustomAction = useCallback(
    (action: () => void | Promise<void>) => {
      if (hasUnsavedChanges) {
        setPendingCustomAction(() => action);
        setPendingNavigation(null); // Clear any pending URL navigation
        setShowConfirmDialog(true);
      } else {
        action();
      }
    },
    [hasUnsavedChanges]
  );

  const handleConfirm = useCallback(async () => {
    setShowConfirmDialog(false);
    if (onConfirmAction) {
      onConfirmAction();
    }
    if (pendingNavigation) {
      router.push(pendingNavigation);
      setPendingNavigation(null);
    } else if (pendingCustomAction) {
      await pendingCustomAction();
      setPendingCustomAction(null);
    }
    setHasUnsavedChanges(false);
  }, [pendingNavigation, pendingCustomAction, router, onConfirmAction]);

  const handleCancel = useCallback(() => {
    setShowConfirmDialog(false);
    setPendingNavigation(null);
    setPendingCustomAction(null);
  }, []);

  const contextValue: NavigationGuardContextType = useMemo(
    () => ({
      hasUnsavedChanges,
      setHasUnsavedChanges,
      navigateWithConfirmation,
      navigateWithCustomAction,
      onConfirmAction,
      setOnConfirmAction,
    }),
    [
      hasUnsavedChanges,
      navigateWithConfirmation,
      navigateWithCustomAction,
      onConfirmAction,
      setOnConfirmAction,
    ]
  );

  return (
    <NavigationGuardContext.Provider value={contextValue}>
      {children}

      {/* Global Navigation Guard Dialog */}
      <ConfirmationDialog
        open={showConfirmDialog}
        onOpenChange={() => {}} // Prevent closing by clicking outside
        title="Unsaved Changes"
        description="You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost."
        confirmText="Leave Page"
        cancelText="Stay Here"
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        variant="destructive"
      />
    </NavigationGuardContext.Provider>
  );
}
