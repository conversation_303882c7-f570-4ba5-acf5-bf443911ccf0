"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getAvailableTabNames } from "@/lib/constants/tab-order";
import { type TabComponentName } from "@/components/dashboard/npd-tabs";
import { TagIcon, Plus, Activity, MessageCircle } from "lucide-react";
import { useUnreadCounts } from "@/hooks/use-unread-counts";

interface Tab {
  id: string;
  tabName: string;
  order: number;
}

interface Product {
  id: string;
  name: string;
  tabs: Tab[];
  isSubscribed?: boolean;
}

interface TabNavigationProps {
  product: Product;
  activeTab?: string; // Optional since parent Tabs component manages active state
  onTabChange: (tabId: string) => void;
  onAddTab: (tabName: TabComponentName) => void;
  openPanel: "activity" | "chat" | null;
  onOpenPanel: (panel: "activity" | "chat") => void;
  // Removed unread counts - component will fetch its own data
}

export function TabNavigation({
  product,
   
  activeTab,
  onTabChange,
  onAddTab,
  openPanel,
  onOpenPanel,
}: TabNavigationProps) {
  // Use lightweight hook to fetch only unread counts for badges
  // This avoids loading full data during product switching
  const { activityUnreadCount, chatUnreadCount } = useUnreadCounts(
    product.id,
    !openPanel // Only fetch when no panel is open (for badge display)
  );

  return (
    <div className="flex-shrink-0 px-6 border-b py-2 bg-muted/20">
      <div className="flex items-center justify-between">
        <TabsList
          className="inline-flex h-10 items-center justify-start rounded-lg bg-muted/40 p-1 text-muted-foreground shadow-sm"
          style={{ width: "fit-content" }}
        >
          {/* Existing tabs */}
          {product.tabs
            .sort((a, b) => a.order - b.order)
            .map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="cursor-pointer relative inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium bg-muted/30 text-muted-foreground transition-all hover:bg-muted/60 hover:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:border-primary/20 data-[state=active]:shadow-md"
                onClick={() => onTabChange(tab.id)}
              >
                {/* Active indicator bar */}
                <div
                  className={`absolute left-0 top-1/2 -translate-y-1/2 w-1 h-4 bg-primary rounded-r-full transition-opacity ${
                    activeTab === tab.id ? "opacity-100" : "opacity-0"
                  }`}
                />
                <span className="relative">{tab.tabName}</span>
              </TabsTrigger>
            ))}

          {/* Plus Button */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 px-2 ml-1 rounded-md bg-muted/30 hover:bg-muted/60 text-muted-foreground hover:text-foreground cursor-pointer transition-all"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {(() => {
                const allTabNames = getAvailableTabNames();
                const existingTabNames = product.tabs.map((tab) => tab.tabName);
                const availableTabs = allTabNames.filter(
                  (tabName) => !existingTabNames.includes(tabName)
                );

                if (availableTabs.length === 0) {
                  return (
                    <DropdownMenuItem disabled>
                      No additional tabs available
                    </DropdownMenuItem>
                  );
                }

                return availableTabs.map((tabName) => (
                  <DropdownMenuItem
                    key={tabName}
                    onClick={() => onAddTab(tabName as TabComponentName)}
                  >
                    <TagIcon className="mr-2 h-4 w-4" />
                    {tabName}
                  </DropdownMenuItem>
                ));
              })()}
            </DropdownMenuContent>
          </DropdownMenu>
        </TabsList>

        {/* Activity and Chat Controls - hidden when panel is open */}
        {!openPanel && (
          <div className="flex items-center gap-2 border rounded-lg px-3 py-1.5 bg-muted/30">
            {/* Activity button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenPanel("activity")}
              className="h-7 px-2 gap-1.5 hover:bg-muted/70 hover:text-foreground hover:shadow-sm cursor-pointer transition-all duration-200 hover:scale-105"
            >
              <Activity className="h-3.5 w-3.5" />
              <span className="text-xs">Activity</span>
              {product && product.isSubscribed && activityUnreadCount > 0 && (
                <div className="bg-red-500 text-white text-xs rounded-full min-w-[16px] h-3.5 flex items-center justify-center px-1 font-medium text-[10px]">
                  {activityUnreadCount > 99 ? "99+" : activityUnreadCount}
                </div>
              )}
            </Button>

            {/* Chat button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenPanel("chat")}
              className="h-7 px-2 gap-1.5 hover:bg-muted/70 hover:text-foreground hover:shadow-sm cursor-pointer transition-all duration-200 hover:scale-105"
            >
              <MessageCircle className="h-3.5 w-3.5" />
              <span className="text-xs">Chat</span>
              {product && product.isSubscribed && chatUnreadCount > 0 && (
                <div className="bg-red-500 text-white text-xs rounded-full min-w-[16px] h-3.5 flex items-center justify-center px-1 font-medium text-[10px]">
                  {chatUnreadCount > 99 ? "99+" : chatUnreadCount}
                </div>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
