"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Image as ImageIcon, Upload, X, Link } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface UnifiedImageCardProps {
  title?: string;
  icon?: React.ReactNode;
  images: string[];
  isEditing: boolean;
  onImagesChange: (images: string[]) => void;
  onNewFiles?: (files: File[]) => void;
  onDeletedImages?: (urls: string[]) => void;
  maxFileSize?: number; // in MB
  allowMultiple?: boolean;
  showUploadButton?: boolean;
  showAddUrlButton?: boolean;
  className?: string;
}

export function UnifiedImageCard({
  title = "Images",
  icon,
  images = [],
  isEditing,
  onImagesChange,
  onNewFiles,
  onDeletedImages,
  maxFileSize = 5,
  allowMultiple = true,
  showUploadButton = true,
  showAddUrlButton = true,
  className = "",
}: UnifiedImageCardProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );
  const [showUrlDialog, setShowUrlDialog] = useState(false);
  const [urlInput, setUrlInput] = useState("");
  const [previewUrls, setPreviewUrls] = useState<string[]>(images);

  // Update preview URLs when images prop changes
  useEffect(() => {
    setPreviewUrls(images);
  }, [images]);

  // Keyboard navigation for image modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (selectedImageIndex === null) return;

      switch (e.key) {
        case "Escape":
          setSelectedImageIndex(null);
          break;
        case "ArrowLeft":
          if (selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
          }
          break;
        case "ArrowRight":
          if (selectedImageIndex < previewUrls.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
          }
          break;
      }
    };

    if (selectedImageIndex !== null) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [selectedImageIndex, previewUrls.length]);

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (!files) return;

      const validFiles = Array.from(files).filter((file) => {
        if (!file.type.startsWith("image/")) {
          toast.error(`${file.name} is not a valid image file`);
          return false;
        }
        if (file.size > maxFileSize * 1024 * 1024) {
          toast.error(`${file.name} is too large (max ${maxFileSize}MB)`);
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) return;

      // Create preview URLs for new files
      const newPreviews = validFiles.map((file) => URL.createObjectURL(file));
      const updatedPreviews = [...previewUrls, ...newPreviews];

      setPreviewUrls(updatedPreviews);

      // Notify parent about new files
      if (onNewFiles) {
        onNewFiles(validFiles);
      }

      // Update images array
      onImagesChange(updatedPreviews);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [maxFileSize, previewUrls, onImagesChange, onNewFiles]
  );

  const handleAddUrl = useCallback(() => {
    if (!urlInput.trim()) {
      toast.error("Please enter a valid URL");
      return;
    }

    // Basic URL validation
    try {
      new URL(urlInput);
    } catch {
      toast.error("Please enter a valid URL");
      return;
    }

    const updatedImages = [...previewUrls, urlInput];
    setPreviewUrls(updatedImages);
    onImagesChange(updatedImages);
    setUrlInput("");
    setShowUrlDialog(false);
    toast.success("Image URL added successfully");
  }, [urlInput, previewUrls, onImagesChange]);

  const handleRemoveImage = useCallback(
    (index: number) => {
      const imageUrl = previewUrls[index];
      const isExistingImage = !imageUrl.startsWith("blob:");

      if (isExistingImage && onDeletedImages) {
        // Mark existing image for deletion
        onDeletedImages([imageUrl]);
      }

      // Remove from preview
      const updatedPreviews = previewUrls.filter((_, i) => i !== index);
      setPreviewUrls(updatedPreviews);
      onImagesChange(updatedPreviews);
    },
    [previewUrls, onImagesChange, onDeletedImages]
  );

  const defaultIcon = <ImageIcon className="h-5 w-5 text-blue-500" />;

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {icon || defaultIcon}
              {title} ({previewUrls.length}{" "}
              {previewUrls.length === 1 ? "image" : "images"})
            </CardTitle>
            {isEditing && (
              <div className="flex items-center gap-2">
                {showUploadButton && (
                  <>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple={allowMultiple}
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      Upload
                    </Button>
                  </>
                )}
                {showAddUrlButton && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowUrlDialog(true)}
                  >
                    <Link className="h-4 w-4 mr-1" />
                    Add URL
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {previewUrls.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {previewUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <div
                      className="cursor-pointer"
                      onClick={() => setSelectedImageIndex(index)}
                    >
                      <Image
                        src={url}
                        alt={`Image ${index + 1}`}
                        width={200}
                        height={128}
                        className="w-full h-32 object-cover rounded-lg border hover:opacity-90 transition-opacity"
                        unoptimized={
                          url.startsWith("blob:") || url.startsWith("data:")
                        }
                      />
                    </div>
                    {isEditing && (
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveImage(index);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No images uploaded yet</p>
                {isEditing && (
                  <p className="text-sm">
                    Click Upload or Add URL to add images
                  </p>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Add URL Dialog */}
      <Dialog open={showUrlDialog} onOpenChange={setShowUrlDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Image URL</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="imageUrl">Image URL</Label>
              <Input
                id="imageUrl"
                type="url"
                placeholder="https://example.com/image.jpg"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddUrl();
                  }
                }}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowUrlDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddUrl}>Add Image</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Full-Screen Image Modal */}
      {selectedImageIndex !== null && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-50 overflow-hidden">
          {/* Close Button */}
          <button
            className="absolute top-4 right-4 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-3 transition-all duration-200 z-30"
            onClick={() => setSelectedImageIndex(null)}
            aria-label="Close image viewer"
          >
            <X className="h-6 w-6" />
          </button>

          {/* Previous Button */}
          {selectedImageIndex > 0 && (
            <button
              className="absolute left-4 top-1/2 -translate-y-1/2 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-4 transition-all duration-200 z-30"
              onClick={() => setSelectedImageIndex(selectedImageIndex - 1)}
              aria-label="Previous image"
            >
              <span className="text-2xl font-bold">‹</span>
            </button>
          )}

          {/* Next Button */}
          {selectedImageIndex < previewUrls.length - 1 && (
            <button
              className="absolute right-4 top-1/2 -translate-y-1/2 text-white/90 hover:text-white bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-4 transition-all duration-200 z-30"
              onClick={() => setSelectedImageIndex(selectedImageIndex + 1)}
              aria-label="Next image"
            >
              <span className="text-2xl font-bold">›</span>
            </button>
          )}

          {/* Image Container */}
          <div className="relative w-full h-full flex items-center justify-center p-4 md:p-8">
            <div
              className="relative w-full h-full flex items-center justify-center"
              style={{
                maxWidth: "calc(100vw - 2rem)",
                maxHeight: "calc(100vh - 8rem)",
              }}
            >
              <Image
                src={previewUrls[selectedImageIndex]}
                alt={`Image ${selectedImageIndex + 1}`}
                fill
                className="object-contain rounded-lg shadow-2xl"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                unoptimized={
                  previewUrls[selectedImageIndex]?.startsWith("blob:") ||
                  previewUrls[selectedImageIndex]?.startsWith("data:")
                }
              />
            </div>
          </div>

          {/* Image Counter */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 text-white/90 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium z-30">
            {selectedImageIndex + 1} of {previewUrls.length}
          </div>
        </div>
      )}
    </>
  );
}
