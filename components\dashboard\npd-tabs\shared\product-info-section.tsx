import { Package } from "lucide-react";
import { EditableDataTable } from "./editable-data-table";
import { useFormFieldStyles } from "@/hooks/use-form-field-styles";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ProductInfo {
  features?: string;
  color?: string;
  size?: string;
  pricerange?: string;
  launchIntoExistingListing?: string;
  dataDiveDashboardName?: string;
  dataDiveDashboardLink?: string;
  totalSellersInNiche?: string;
  googleTrendsKeyword?: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: unknown[];
}

interface ProductInfoSectionProps {
  data: ProductInfo;
  isEditing?: boolean;
  onValuesChange?: (values: ProductInfo) => void;
  onDirtyChange?: (isDirty: boolean) => void;
  modifiedFields?: Set<string>;
  onFieldChange?: (
    field: string,
    value: string,
    originalData: ProductInfo
  ) => void;
}

export function ProductInfoSection({
  data,
  isEditing = false,
  onValuesChange,
  modifiedFields = new Set(),
  onFieldChange,
}: Omit<ProductInfoSectionProps, "onDirtyChange">) {
  // Use centralized form field styling
  const { getFieldStyles } = useFormFieldStyles(modifiedFields);

  // Check if any product info fields have changes
  const hasProductInfoChanges =
    modifiedFields.has("features") ||
    modifiedFields.has("color") ||
    modifiedFields.has("size") ||
    modifiedFields.has("pricerange") ||
    modifiedFields.has("launchIntoExistingListing") ||
    modifiedFields.has("dataDiveDashboardName") ||
    modifiedFields.has("dataDiveDashboardLink") ||
    modifiedFields.has("totalSellersInNiche") ||
    modifiedFields.has("googleTrendsKeyword");

  // Handle input changes
  const handleChange = (field: keyof ProductInfo, value: string) => {
    const newData = { ...data, [field]: value };
    onValuesChange?.(newData);
    onFieldChange?.(field, value, data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5 text-purple-600" />
          Product Information
          {hasProductInfoChanges && (
            <div className="flex items-center gap-1 ml-auto">
              <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
              <span className="text-xs text-amber-600 font-normal">
                Unsaved changes
              </span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <EditableDataTable
          data={[
            {
              id: "features",
              label: "Features",
              value: data.features,
              placeholder: "Enter product features...",
              type: "textarea",
              rows: 3,
              onChange: (value) => handleChange("features", value),
              disabled: !isEditing,
              className: getFieldStyles("features"),
              isChanged: modifiedFields.has("features"),
              isEditing: isEditing,
            },
            {
              id: "color",
              label: "Color",
              value: data.color,
              placeholder: "Enter color...",
              onChange: (value) => handleChange("color", value),
              disabled: !isEditing,
              className: getFieldStyles("color"),
              isChanged: modifiedFields.has("color"),
              isEditing: isEditing,
            },
            {
              id: "size",
              label: "Size",
              value: data.size,
              placeholder: "Enter size...",
              onChange: (value) => handleChange("size", value),
              disabled: !isEditing,
              className: getFieldStyles("size"),
              isChanged: modifiedFields.has("size"),
              isEditing: isEditing,
            },
            {
              id: "pricerange",
              label: "Price Range",
              value: data.pricerange,
              placeholder: "Enter price range...",
              onChange: (value) => handleChange("pricerange", value),
              disabled: !isEditing,
              className: getFieldStyles("pricerange"),
              isChanged: modifiedFields.has("pricerange"),
              isEditing: isEditing,
            },
            {
              id: "launchIntoExistingListing",
              label: "Launch into Existing Listing",
              value: data.launchIntoExistingListing,
              type: "select",
              options: [
                { value: "No", label: "No" },
                { value: "Yes", label: "Yes" },
              ],
              onChange: (value) =>
                handleChange("launchIntoExistingListing", value),
              disabled: !isEditing,
              className: getFieldStyles("launchIntoExistingListing"),
              isChanged: modifiedFields.has("launchIntoExistingListing"),
              isEditing: isEditing,
            },
            {
              id: "dataDiveDashboardName",
              label: "Data Dive Dashboard Name",
              value: data.dataDiveDashboardName,
              placeholder: "Enter dashboard name...",
              onChange: (value) => handleChange("dataDiveDashboardName", value),
              disabled: !isEditing,
              className: getFieldStyles("dataDiveDashboardName"),
              isChanged: modifiedFields.has("dataDiveDashboardName"),
              isEditing: isEditing,
            },
            {
              id: "dataDiveDashboardLink",
              label: "Data Dive Dashboard Link",
              value: data.dataDiveDashboardLink,
              placeholder: "https://...",
              onChange: (value) => handleChange("dataDiveDashboardLink", value),
              disabled: !isEditing,
              className: getFieldStyles("dataDiveDashboardLink"),
              isChanged: modifiedFields.has("dataDiveDashboardLink"),
              isEditing: isEditing,
            },
            {
              id: "totalSellersInNiche",
              label: "Total # of Sellers within Niche",
              value: data.totalSellersInNiche,
              placeholder: "Enter number of sellers...",
              onChange: (value) => handleChange("totalSellersInNiche", value),
              disabled: !isEditing,
              className: getFieldStyles("totalSellersInNiche"),
              isChanged: modifiedFields.has("totalSellersInNiche"),
              isEditing: isEditing,
            },
          ]}
        />
      </CardContent>
    </Card>
  );
}
