// Brand constants
export const BRANDS = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON>otte",
  "Dr. Arthritis",
  "Dr. Moritz",
  "EZPIK",
  "grace & stella",
  "Kiddycare",
  "LeGushe",
  "Perfect Remedy",
  "Pilpoc",
  "Protect Life",
  "Venus Visage",
] as const;

// Type for brand
export type Brand = (typeof BRANDS)[number];

// Helper function to check if a brand is valid
export const isValidBrand = (brand: string): brand is Brand => {
  return BRANDS.includes(brand as Brand);
};

// Helper function to get brand display name
export const getBrandDisplayName = (brand: string): string => {
  if (isValidBrand(brand)) {
    return brand;
  }
  return brand; // Return as-is if not in the predefined list
};
