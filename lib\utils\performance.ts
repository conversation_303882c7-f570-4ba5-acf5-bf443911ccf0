/**
 * Performance monitoring and optimization utilities
 */

import React from "react";
import { isDevelopment } from "@/lib/constants";

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000;

  /**
   * Start timing an operation
   */
  startTimer(name: string): (metadata?: Record<string, unknown>) => void {
    const startTime = performance.now();

    return (metadata?: Record<string, unknown>) => {
      const duration = performance.now() - startTime;
      this.addMetric(name, duration, metadata);
    };
  }

  /**
   * Add a performance metric
   */
  addMetric(
    name: string,
    duration: number,
    metadata?: Record<string, unknown>
  ): void {
    // Only store metrics in development or if explicitly enabled
    if (!isDevelopment && !process.env.ENABLE_PERFORMANCE_MONITORING) {
      return;
    }

    this.metrics.push({
      name,
      duration,
      timestamp: Date.now(),
      metadata,
    });

    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations in development
    if (isDevelopment && duration > 1000) {
      console.warn(
        `🐌 Slow operation detected: ${name} took ${duration.toFixed(2)}ms`,
        metadata
      );
    }
  }

  /**
   * Get performance statistics
   */
  getStats(operationName?: string) {
    const filteredMetrics = operationName
      ? this.metrics.filter((m) => m.name === operationName)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return null;
    }

    const durations = filteredMetrics.map((m) => m.duration);
    const total = durations.reduce((sum, d) => sum + d, 0);
    const avg = total / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);

    // Calculate percentiles
    const sorted = [...durations].sort((a, b) => a - b);
    const p50 = sorted[Math.floor(sorted.length * 0.5)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const p99 = sorted[Math.floor(sorted.length * 0.99)];

    return {
      count: filteredMetrics.length,
      total: total.toFixed(2),
      average: avg.toFixed(2),
      min: min.toFixed(2),
      max: max.toFixed(2),
      p50: p50.toFixed(2),
      p95: p95.toFixed(2),
      p99: p99.toFixed(2),
    };
  }

  /**
   * Get all operation names
   */
  getOperationNames(): string[] {
    return [...new Set(this.metrics.map((m) => m.name))];
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  export(): PerformanceMetric[] {
    return [...this.metrics];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for timing Server Actions
 */
export function withPerformanceMonitoring<T extends unknown[], R>(
  name: string,
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const endTimer = performanceMonitor.startTimer(name);

    try {
      const result = await fn(...args);
      endTimer({ success: true, argsLength: args.length });
      return result;
    } catch (error) {
      endTimer({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        argsLength: args.length,
      });
      throw error;
    }
  };
}

/**
 * Database query performance wrapper
 */
export function withQueryMonitoring<T>(
  queryName: string,
  query: () => Promise<T>
): Promise<T> {
  const endTimer = performanceMonitor.startTimer(`db:${queryName}`);

  return query()
    .then((result) => {
      endTimer({ success: true });
      return result;
    })
    .catch((error) => {
      endTimer({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    });
}

/**
 * React component performance wrapper
 */
export function withComponentMonitoring<P extends object>(
  componentName: string,
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function MonitoredComponent(props: P) {
    const endTimer = performanceMonitor.startTimer(
      `component:${componentName}`
    );

    React.useEffect(() => {
      endTimer({ rendered: true });
    }, [endTimer]);

    return React.createElement(Component, props);
  };
}

/**
 * Debounce function for reducing API calls
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for limiting API calls
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Batch multiple operations together
 */
export class BatchProcessor<T, R> {
  private batch: T[] = [];
  private timeout: NodeJS.Timeout | null = null;
  private readonly batchSize: number;
  private readonly batchTimeout: number;
  private readonly processor: (items: T[]) => Promise<R[]>;

  constructor(
    processor: (items: T[]) => Promise<R[]>,
    batchSize: number = 10,
    batchTimeout: number = 100
  ) {
    this.processor = processor;
    this.batchSize = batchSize;
    this.batchTimeout = batchTimeout;
  }

  add(item: T): Promise<R> {
    return new Promise((resolve, reject) => {
      this.batch.push(item);

      // Process immediately if batch is full
      if (this.batch.length >= this.batchSize) {
        this.processBatch()
          .then((results) => {
            resolve(results[results.length - 1]);
          })
          .catch(reject);
        return;
      }

      // Schedule batch processing
      if (this.timeout) {
        clearTimeout(this.timeout);
      }

      this.timeout = setTimeout(() => {
        this.processBatch()
          .then((results) => {
            resolve(results[results.length - 1]);
          })
          .catch(reject);
      }, this.batchTimeout);
    });
  }

  private async processBatch(): Promise<R[]> {
    if (this.batch.length === 0) {
      return [];
    }

    const items = [...this.batch];
    this.batch = [];

    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }

    return this.processor(items);
  }
}

/**
 * Log performance stats to console (development only)
 */
export function logPerformanceStats(): void {
  if (!isDevelopment) {
    return;
  }

  const operations = performanceMonitor.getOperationNames();

  operations.forEach((operation) => {
    performanceMonitor.getStats(operation);
    // Stats available for programmatic use
  });
}
