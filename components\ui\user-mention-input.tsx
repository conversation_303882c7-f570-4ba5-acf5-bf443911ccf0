/**
 * User mention input component with autocomplete
 */

import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { useUserAutocomplete, User } from "@/hooks/use-user-autocomplete";

interface UserMentionInputProps {
  value: string;
  onChange: (value: string, mentions: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function UserMentionInput({
  value,
  onChange,
  placeholder = "Type @username to mention users",
  className,
  disabled = false,
}: UserMentionInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<User[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mentionStart, setMentionStart] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  const { searchUsers } = useUserAutocomplete();

  // Parse mentions from current value
  const parseMentions = (text: string): string[] => {
    const mentions = text.match(/@(\w+)/g) || [];
    return mentions.map((mention) => mention.substring(1));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    const cursorPosition = e.target.selectionStart || 0;

    // Update value and mentions
    const mentions = parseMentions(newValue);
    onChange(newValue, mentions);

    // Handle autocomplete
    const textBeforeCursor = newValue.slice(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf("@");

    if (lastAtIndex !== -1) {
      const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1);

      if (textAfterAt.length >= 0 && !textAfterAt.match(/\s/)) {
        const filteredUsers = searchUsers(textAfterAt);
        setSuggestions(filteredUsers.slice(0, 5)); // Limit to 5 suggestions
        setShowSuggestions(filteredUsers.length > 0);
        setMentionStart(lastAtIndex);
        setSelectedIndex(0);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  };

  const selectSuggestion = (user: User) => {
    if (mentionStart === -1) return;

    const beforeMention = value.slice(0, mentionStart);
    const afterMention = value.slice(
      inputRef.current?.selectionStart || value.length
    );
    const newValue = `${beforeMention}@${user.name} ${afterMention}`;

    const mentions = parseMentions(newValue);
    onChange(newValue, mentions);
    setShowSuggestions(false);

    // Focus back to input
    setTimeout(() => {
      inputRef.current?.focus();
      const newCursorPos = mentionStart + user.name.length + 2;
      inputRef.current?.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
      case "Tab":
        e.preventDefault();
        if (suggestions[selectedIndex]) {
          selectSuggestion(suggestions[selectedIndex]);
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        break;
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setShowSuggestions(false);
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <div className="relative">
      <Input
        ref={inputRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={className}
        disabled={disabled}
      />

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto z-50">
          {suggestions.map((user, index) => (
            <button
              key={user.id}
              className={`w-full text-left px-3 py-2 hover:bg-gray-100 ${
                index === selectedIndex ? "bg-blue-50 text-blue-600" : ""
              }`}
              onClick={() => selectSuggestion(user)}
            >
              <div className="font-medium">@{user.name}</div>
              <div className="text-sm text-gray-500">{user.email}</div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
