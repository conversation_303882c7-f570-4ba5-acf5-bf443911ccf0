import React from "react";
import { TABLE_STYLES } from "./tabs-table-styles";

interface DataRow {
  label: string;
  value: string | null | undefined;
}

interface SimpleDataTableProps {
  data: DataRow[];
  className?: string;
}

export function SimpleDataTable({ data, className = "" }: SimpleDataTableProps) {
  // Filter out rows with empty values
  const filteredData = data.filter(row => row.value && row.value.trim() !== "");

  if (filteredData.length === 0) {
    return (
      <div className="text-sm text-muted-foreground italic p-4 text-center">
        No data available
      </div>
    );
  }

  return (
    <div className={`${TABLE_STYLES.container} ${className}`}>
      <div className={TABLE_STYLES.scrollContainer}>
        <table className={TABLE_STYLES.table}>
          <tbody className={TABLE_STYLES.bodyContainer}>
            {filteredData.map((row, index) => (
              <tr
                key={index}
                className={`${TABLE_STYLES.bodyRow.base} ${
                  index % 2 === 0
                    ? TABLE_STYLES.bodyRow.even
                    : TABLE_STYLES.bodyRow.odd
                }`}
              >
                <td className={`${TABLE_STYLES.bodyCell} font-medium w-1/3 px-3 py-2`}>
                  {row.label}
                </td>
                <td className={`${TABLE_STYLES.bodyCell} px-3 py-2`}>
                  {row.value}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
