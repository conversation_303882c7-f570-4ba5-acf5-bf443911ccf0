import React, { ReactNode } from "react";
import { flexRender } from "@tanstack/react-table";
import type { Table } from "@tanstack/react-table";

// Common table styling constants that can be used across all tab tables
export const TABLE_STYLES = {
  // Container styles
  container: "border rounded-lg overflow-hidden",
  scrollContainer: "overflow-auto",

  // Table base styles
  table: "w-full text-xs border-collapse table-fixed",

  // Header styles
  headerRow: "border-b",
  headerGroup: "bg-muted border-b sticky top-0 z-20 shadow-sm",
  headerCell: {
    base: "border-r px-2 py-2 text-center font-semibold text-foreground bg-muted relative whitespace-normal break-keep leading-tight min-h-[32px]",
    sortable: "cursor-pointer select-none hover:bg-accent/40",
    nonSortable: "",
  },

  // Body styles
  bodyContainer: "bg-background",
  bodyRow: {
    base: "border-b transition-colors",
    even: "bg-background hover:bg-accent/50",
    odd: "bg-muted/30 hover:bg-accent/50",
    hidden: "bg-gray-100 text-gray-400 opacity-50", // For hidden/disabled rows
    first: "first:rounded-t-lg", // Round top corners for first row
    last: "last:rounded-b-lg last:border-b-0", // Round bottom corners and remove border for last row
  },
  bodyCell:
    "border-r p-0 align-middle text-xs leading-tight first:first:rounded-tl-lg last:first:rounded-tr-lg first:last:rounded-bl-lg last:last:rounded-br-lg",

  // Footer styles
  footerContainer:
    "bg-muted/50 border-t-2 border-primary/20 sticky bottom-0 z-10",
  footerRow: {
    sum: "bg-muted/70 border-t",
    median: "bg-muted/50 border-t",
    average: "bg-muted/40 border-t",
  },
  footerCell: "border-r px-2 py-2 text-center text-xs",

  // Interactive elements
  sortIndicator: {
    asc: "▲",
    desc: "▼",
    container: "inline-flex items-center gap-1",
  },

  // Resize handle
  resizeHandle:
    "absolute right-0 top-0 h-full w-1 bg-primary cursor-col-resize opacity-0 hover:opacity-100 active:opacity-100 transition-opacity",

  // Copy functionality
  copyable: {
    base: "cursor-pointer rounded px-1 border-2 border-transparent hover:border-blue-300 transition-all",
    nonCopyable:
      "rounded px-1 border-2 border-transparent hover:border-blue-300 transition-all",
  },
} as const;

// Reusable Table Header Component
interface TabsTableHeaderProps<T = unknown> {
  table: Table<T>;
}

export function TabsTableHeader<T = unknown>({
  table,
}: TabsTableHeaderProps<T>) {
  return (
    <thead className={TABLE_STYLES.headerGroup}>
      {table.getHeaderGroups().map((headerGroup) => (
        <tr key={headerGroup.id} className={TABLE_STYLES.headerRow}>
          {headerGroup.headers.map((header) => {
            const isSortable =
              header.column.getCanSort?.() &&
              header.column.getIsSorted !== undefined;
            const sorted = header.column.getIsSorted?.();

            return (
              <th
                key={header.id}
                style={{
                  width: header.getSize(),
                  minWidth: header.getSize(),
                }}
                className={`${TABLE_STYLES.headerCell.base} ${
                  isSortable
                    ? TABLE_STYLES.headerCell.sortable
                    : TABLE_STYLES.headerCell.nonSortable
                }`}
                onClick={
                  isSortable
                    ? header.column.getToggleSortingHandler()
                    : undefined
                }
              >
                <span className={TABLE_STYLES.sortIndicator.container}>
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                  {sorted === "asc" && (
                    <span aria-label="sorted ascending" className="text-xs">
                      {TABLE_STYLES.sortIndicator.asc}
                    </span>
                  )}
                  {sorted === "desc" && (
                    <span aria-label="sorted descending" className="text-xs">
                      {TABLE_STYLES.sortIndicator.desc}
                    </span>
                  )}
                </span>
                {header.column.getCanResize() && (
                  <div
                    {...{
                      onMouseDown: header.getResizeHandler(),
                      onTouchStart: header.getResizeHandler(),
                    }}
                    className={TABLE_STYLES.resizeHandle}
                  />
                )}
              </th>
            );
          })}
        </tr>
      ))}
    </thead>
  );
}

// Reusable Table Body Component
interface TabsTableBodyProps<T = unknown> {
  table: Table<T>;
  children?: ReactNode;
  getRowClassName?: (rowIndex: number, rowData: T) => string;
}

export function TabsTableBody<T = unknown>({
  table,
  children,
  getRowClassName,
}: TabsTableBodyProps<T>) {
  return (
    <tbody className={TABLE_STYLES.bodyContainer}>
      {table.getRowModel().rows.map((row, rowIndex) => {
        // Default row styling logic
        const isVisible =
          (row.original as Record<string, unknown>)?.isVisible !== false;
        const defaultClassName = !isVisible
          ? TABLE_STYLES.bodyRow.hidden
          : rowIndex % 2 === 0
          ? TABLE_STYLES.bodyRow.even
          : TABLE_STYLES.bodyRow.odd;

        // Allow custom row className override
        const rowClassName = getRowClassName
          ? getRowClassName(rowIndex, row.original)
          : defaultClassName;

        return (
          <tr
            key={row.id}
            className={`${TABLE_STYLES.bodyRow.base} ${rowClassName}`}
          >
            {row.getVisibleCells().map((cell) => (
              <td
                key={cell.id}
                style={{
                  width: cell.column.getSize(),
                  minWidth: cell.column.getSize(),
                }}
                className={TABLE_STYLES.bodyCell}
              >
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        );
      })}
      {children}
    </tbody>
  );
}

// Reusable Table Container Component
interface TabsTableContainerProps {
  children: ReactNode;
  maxHeight?: string;
  height?: string;
}

export function TabsTableContainer({
  children,
  maxHeight = "600px",
  height = "fit-content",
}: TabsTableContainerProps) {
  return (
    <div className={TABLE_STYLES.container}>
      <div
        className={TABLE_STYLES.scrollContainer}
        style={{ maxHeight, height }}
      >
        {children}
      </div>
    </div>
  );
}

// Utility function to create copyable content
export function createCopyableContent(
  content: ReactNode,
  onCopy?: (text: string) => void,
  isLabelCell: boolean = false
): ReactNode {
  if (!content || isLabelCell) return content;

  const className = onCopy
    ? TABLE_STYLES.copyable.base
    : TABLE_STYLES.copyable.nonCopyable;

  const handleClick = onCopy
    ? () => {
        if (typeof content === "string") {
          onCopy(content);
        } else if (React.isValidElement(content)) {
          const span = content as React.ReactElement<{ children: string }>;
          const textContent = span.props.children;
          if (typeof textContent === "string") {
            onCopy(textContent);
          }
        }
      }
    : undefined;

  return (
    <div
      className={className}
      onClick={handleClick}
      title={onCopy ? "Click to copy" : undefined}
    >
      {content}
    </div>
  );
}
