"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import { usePermissions } from "@/hooks/use-permissions";
import {
  archiveTab,
  requestTabDeletion,
} from "@/actions/tab-archiving.actions";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { TrashIcon } from "lucide-react";
import Spinner from "@/components/spinner";

interface TabDangerZoneProps {
  tabId: string;
  tabName: string;
  productId: string;
  productName: string;
  productSlug: string;
  tabCreatorId?: string | null;
  productOwnerId?: string | null;
  isEditMode: boolean;
}

export function TabDangerZone({
  tabId,
  tabName,
  productId, // eslint-disable-line @typescript-eslint/no-unused-vars
  productName,
  productSlug,
  tabCreatorId,
  productOwnerId,
  isEditMode,
}: TabDangerZoneProps) {
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [showRequestDialog, setShowRequestDialog] = useState(false);
  const [archiveReason, setArchiveReason] = useState("");
  const [requestReason, setRequestReason] = useState("");
  const [loading, setLoading] = useState(false);

  const { user } = useAuth();
  const { isAdmin, isSuperAdmin } = usePermissions();

  // Check if user can archive tab
  const canArchiveTab = () => {
    if (!user) return false;

    // Tab creator, product owner, admin, or super admin can archive
    return (
      tabCreatorId === user.userId ||
      productOwnerId === user.userId ||
      isAdmin ||
      isSuperAdmin
    );
  };

  // Handle archive tab
  const handleArchiveTab = async () => {
    try {
      setLoading(true);
      const result = await archiveTab(tabId, archiveReason);

      if (result.success) {
        setShowArchiveDialog(false);
        setArchiveReason("");

        // Simple solution: reload the page to refresh all state
        // Pass success message via URL parameter to show toast after reload
        const successMessage = encodeURIComponent(result.message);
        window.location.href = `/dashboard/npd/${productSlug}?toast=success&message=${successMessage}`;
      }
    } catch (error) {
      console.error("Error archiving tab:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to archive tab"
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle request tab deletion
  const handleRequestDeletion = async () => {
    try {
      setLoading(true);
      const result = await requestTabDeletion(tabId, requestReason);

      if (result.success) {
        toast.success(result.message);
        setShowRequestDialog(false);
        setRequestReason("");
      }
    } catch (error) {
      console.error("Error requesting tab deletion:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send deletion request"
      );
    } finally {
      setLoading(false);
    }
  };

  // Don't show danger zone if not in edit mode
  if (!isEditMode) {
    return null;
  }

  return (
    <Card className="border-destructive/20 bg-destructive/5">
      <CardHeader>
        <CardTitle className="text-destructive flex items-center gap-2">
          <TrashIcon className="h-5 w-5" />
          Danger Zone
        </CardTitle>
        <CardDescription>
          {canArchiveTab()
            ? "Archive this tab to hide it from users. Archived tabs can be restored by administrators."
            : "Request tab deletion. Administrators and the product owner will be notified."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            {canArchiveTab() ? (
              <>
                <p className="mb-2">What happens when you archive:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Tab becomes invisible to all users</li>
                  <li>All tab data is preserved (fields, history)</li>
                  <li>Tab can be restored by administrators</li>
                </ul>
                <p className="mt-2 font-medium text-green-600 dark:text-green-400">
                  No data will be permanently lost.
                </p>
              </>
            ) : (
              <>
                <p className="mb-2">Deletion request will be sent to:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>All administrators</li>
                  <li>The product owner</li>
                  <li>The tab creator (if different from you)</li>
                </ul>
              </>
            )}
          </div>

          {canArchiveTab() ? (
            <AlertDialog
              open={showArchiveDialog}
              onOpenChange={setShowArchiveDialog}
            >
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={loading}>
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Archive Tab
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Archive Tab?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will archive the tab &quot;{tabName}&quot; in product
                    &quot;
                    {productName}&quot;. The tab will be hidden from users but
                    can be restored by administrators.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="archiveReason">Reason (optional)</Label>
                    <Textarea
                      id="archiveReason"
                      value={archiveReason}
                      onChange={(e) => setArchiveReason(e.target.value)}
                      placeholder="Why are you archiving this tab?"
                      rows={3}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <p className="font-medium text-green-600 dark:text-green-400">
                      No data will be permanently lost.
                    </p>
                  </div>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={loading}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleArchiveTab}
                    disabled={loading}
                    className="bg-destructive text-white hover:bg-destructive/90"
                  >
                    {loading ? (
                      <>
                        <Spinner loading={true} size={8} />
                        <span className="ml-2">Archiving...</span>
                      </>
                    ) : (
                      "Archive Tab"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          ) : (
            <AlertDialog
              open={showRequestDialog}
              onOpenChange={setShowRequestDialog}
            >
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={loading}>
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Request Deletion
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Request Tab Deletion</AlertDialogTitle>
                  <AlertDialogDescription>
                    Send a deletion request for tab &quot;{tabName}&quot; in
                    product &quot;
                    {productName}&quot; to administrators and the product owner.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="requestReason">Reason (optional)</Label>
                    <Textarea
                      id="requestReason"
                      value={requestReason}
                      onChange={(e) => setRequestReason(e.target.value)}
                      placeholder="Why should this tab be deleted?"
                      rows={3}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <p>
                      Notifications will be sent to administrators and the
                      product owner.
                    </p>
                  </div>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={loading}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleRequestDeletion}
                    disabled={loading}
                    className="bg-destructive text-white hover:bg-destructive/90"
                  >
                    {loading ? (
                      <>
                        <Spinner loading={true} size={8} />
                        <span className="ml-2">Sending...</span>
                      </>
                    ) : (
                      "Send Request"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
