"use server";

import { prisma } from "@/lib/db/prisma";
import { revalidatePath } from "next/cache";
import { createProductActivityNotification } from "@/lib/utils/notifications";

export interface ActivityLogData {
  npdProductTabId: string;
  npdProductId: string;
  userId?: string;
  action: string;
  description: string;
  changes?: object | null;
  metadata?: object | null;
}

export async function createActivityLog(data: ActivityLogData) {
  try {
    const activityLog = await prisma.nPDProductActivityLog.create({
      data: {
        npdProductTabId: data.npdProductTabId,
        npdProductId: data.npdProductId,
        userId: data.userId,
        action: data.action,
        description: data.description,
        ...(data.changes && { changes: data.changes }),
        ...(data.metadata && { metadata: data.metadata }),
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    revalidatePath("/dashboard/products");
    return { success: true, data: activityLog };
  } catch {
    return { success: false, error: "Failed to create activity log" };
  }
}

export async function getActivityLogs(npdProductTabId: string) {
  try {
    const activityLogs = await prisma.nPDProductActivityLog.findMany({
      where: {
        npdProductTabId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { success: true, data: activityLogs };
  } catch {
    return { success: false, error: "Failed to fetch activity logs" };
  }
}

export async function getProductActivityLogs(npdProductId: string) {
  try {
    const activityLogs = await prisma.nPDProductActivityLog.findMany({
      where: {
        npdProductId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            image: true,
          },
        },
        npdProductTab: {
          select: {
            tabName: true,
            order: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { success: true, data: activityLogs };
  } catch {
    return { success: false, error: "Failed to fetch product activity logs" };
  }
}

export interface ProductActivityLogData {
  npdProductId: string;
  userId?: string;
  action: string;
  description: string;
  changes?: object | null;
  metadata?: object | null;
}

export async function createProductActivityLog(data: ProductActivityLogData) {
  try {
    // Create product-level activity log directly against the product
    // No need to find a tab - this allows logging even for products without tabs
    const activityLog = await prisma.nPDProductActivityLog.create({
      data: {
        npdProductId: data.npdProductId,
        npdProductTabId: null, // Product-level activities are not tied to specific tabs
        userId: data.userId,
        action: data.action,
        description: data.description,
        ...(data.changes && { changes: data.changes }),
        ...(data.metadata && { metadata: data.metadata }),
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            image: true,
          },
        },
        npdProduct: {
          select: {
            name: true,
            brand: true,
          },
        },
      },
    });

    // Create notifications for significant product updates (but not for subscription activities)
    if (
      data.action !== "subscribed" &&
      data.action !== "unsubscribed" &&
      activityLog.npdProduct
    ) {
      try {
        await createProductActivityNotification(
          data.npdProductId,
          activityLog.npdProduct.name,
          data.npdProductId, // Using npdProductId as slug fallback for now
          data.action,
          data.description,
          data.userId // Exclude the user who made the change
        );
      } catch (error) {
        console.error("Error creating product activity notification:", error);
        // Don't fail the main operation if notification creation fails
      }
    }

    revalidatePath("/dashboard/products");
    return { success: true, data: activityLog };
  } catch (error) {
    console.error("Error creating product activity log:", error);
    return { success: false, error: "Failed to create product activity log" };
  }
}
