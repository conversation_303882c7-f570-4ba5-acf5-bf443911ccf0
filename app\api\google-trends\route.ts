import { NextRequest, NextResponse } from "next/server";

// Helper function to generate realistic mock data
function generateMockTimeSeriesData(
  keyword: string,
  timeframe: string,
  region: string
) {
  const now = new Date();
  const dataPoints = [];

  // Generate different number of points based on timeframe
  let numPoints = 12; // default for 12 months
  let dateIncrement = 30 * 24 * 60 * 60 * 1000; // 30 days

  switch (timeframe) {
    case "past_hour":
      numPoints = 60;
      dateIncrement = 60 * 1000; // 1 minute
      break;
    case "past_day":
      numPoints = 24;
      dateIncrement = 60 * 60 * 1000; // 1 hour
      break;
    case "past_7_days":
      numPoints = 7;
      dateIncrement = 24 * 60 * 60 * 1000; // 1 day
      break;
    case "past_30_days":
      numPoints = 30;
      dateIncrement = 24 * 60 * 60 * 1000; // 1 day
      break;
    case "past_12_months":
      numPoints = 12;
      dateIncrement = 30 * 24 * 60 * 60 * 1000; // 30 days
      break;
  }

  // Base values that vary by keyword
  let baseValue = 50;
  if (keyword.toLowerCase().includes("insoles")) baseValue = 65;
  if (keyword.toLowerCase().includes("shoes")) baseValue = 75;
  if (keyword.toLowerCase().includes("health")) baseValue = 55;

  // Region multiplier
  let regionMultiplier = 1.0;
  if (region === "US") regionMultiplier = 1.2;
  if (region === "GB") regionMultiplier = 0.8;

  for (let i = numPoints - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * dateIncrement);
    const seasonalVariation = Math.sin((i / numPoints) * 2 * Math.PI) * 15;
    const randomVariation = (Math.random() - 0.5) * 20;
    const value = Math.max(
      0,
      Math.min(
        100,
        baseValue * regionMultiplier + seasonalVariation + randomVariation
      )
    );

    dataPoints.push({
      date: date.toISOString(),
      value: Math.round(value),
    });
  }

  return dataPoints;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { keyword, timeframe } = body;

    // Validate required parameters
    if (!keyword) {
      return NextResponse.json(
        { error: "Keyword is required" },
        { status: 400 }
      );
    }

    // Map timeframe to SerpAPI date format
    const getDateParam = (timeframe: string) => {
      switch (timeframe) {
        case "past_5_years":
          return "today 5-y";
        case "past_12_months":
        default:
          return "today 12-m";
      }
    };

    // Fixed settings to conserve API usage
    // searchType: web search is default when gprop is omitted

    // OPTION 1: SerpAPI Google Trends (ENABLED)
    const serpApiKey = process.env.SERP_API_KEY;

    if (serpApiKey) {
      try {
        const serpApiUrl = "https://serpapi.com/search.json";
        const params = new URLSearchParams({
          engine: "google_trends",
          q: keyword,
          data_type: "TIMESERIES",
          date: getDateParam(timeframe || "past_12_months"),
          geo: "", // Fixed: Worldwide
          cat: "0", // Fixed: All categories
          // gprop: omitted for web search (default)
          api_key: serpApiKey,
        });

        const fullUrl = `${serpApiUrl}?${params}`;
        const response = await fetch(fullUrl);
        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        if (data.search_metadata?.status === "Success") {
          // Transform SerpAPI response to our format
          const transformedData = {
            interest_over_time:
              data.interest_over_time?.timeline_data?.map(
                (item: {
                  date: string;
                  values?: { extracted_value?: number; value?: number }[];
                }) => ({
                  date: item.date, // Use the human-readable date string directly
                  value:
                    item.values?.[0]?.extracted_value ||
                    item.values?.[0]?.value ||
                    0,
                })
              ) || [],
            related_queries:
              data.related_queries?.rising?.map(
                (item: { query: string }) => item.query
              ) || [],
            trend: "stable",
          };

          return NextResponse.json(transformedData);
        } else {
          throw new Error(`SerpAPI status: ${data.search_metadata?.status}`);
        }
      } catch {
        // Fall back to mock data on error
      }
    }

    // OPTION 2: RapidAPI Google Trends
    // Uncomment and configure this section to use RapidAPI
    /*
    const rapidApiKey = process.env.RAPIDAPI_KEY;
    if (!rapidApiKey) {
      throw new Error('RAPIDAPI_KEY environment variable is not set');
    }

    const rapidApiUrl = 'https://google-trends12.p.rapidapi.com/trending';
    const response = await fetch(rapidApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-RapidAPI-Key': rapidApiKey,
        'X-RapidAPI-Host': 'google-trends12.p.rapidapi.com'
      },
      body: JSON.stringify({
        keyword: keyword,
        geo: geo || 'US',
        time: timeframe || 'past_12_months'
      })
    });

    const data = await response.json();
    return NextResponse.json(data);
    */

    // OPTION 3: Free Trends API (Vercel Compatible)
    // Using a free Google Trends proxy API
    try {
      // For now, return structured mock data that's more realistic
      // This can be replaced with a real free API when found
      const mockData = {
        interest_over_time: generateMockTimeSeriesData(
          keyword,
          "past_12_months",
          "worldwide"
        ),
        related_queries: [
          `${keyword} reviews`,
          `best ${keyword}`,
          `${keyword} amazon`,
          `${keyword} price`,
          `buy ${keyword}`,
        ],
        trend: "stable",
      };

      return NextResponse.json(mockData);
    } catch (error) {
      console.error("Trends API error:", error);
      throw new Error("Failed to fetch trends data");
    }
  } catch (error) {
    console.error("Google Trends API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Example environment variables you'll need:
// SERP_API_KEY=your_serpapi_key_here
// RAPIDAPI_KEY=your_rapidapi_key_here
// PYTHON_BACKEND_URL=http://localhost:8000

// Example Python backend with pytrends (separate service):
/*
from fastapi import FastAPI
from pytrends.request import TrendReq
import pandas as pd

app = FastAPI()

@app.post("/google-trends")
async def get_trends(request: dict):
    pytrends = TrendReq(hl='en-US', tz=360)
    
    keyword = request.get('keyword')
    timeframe = request.get('timeframe', 'today 12-m')
    geo = request.get('geo', '')
    
    pytrends.build_payload([keyword], cat=0, timeframe=timeframe, geo=geo)
    
    # Get interest over time
    interest_over_time = pytrends.interest_over_time()
    
    # Get related queries
    related_queries = pytrends.related_queries()
    
    return {
        "interest_over_time": interest_over_time.to_dict('records'),
        "related_queries": related_queries[keyword]['rising']['query'].tolist() if related_queries[keyword]['rising'] is not None else []
    }
*/
