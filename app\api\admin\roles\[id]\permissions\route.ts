import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";

// Get permissions for a specific role
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { id } = await context.params;

    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        permissions: {
          orderBy: {
            code: "asc",
          },
        },
      },
    });

    if (!role) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    return NextResponse.json(role);
  } catch (error) {
    console.error("Error fetching role permissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Update permissions for a specific role
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { id } = await context.params;
    const { permissionIds } = await request.json();

    if (!Array.isArray(permissionIds)) {
      return NextResponse.json(
        { error: "Permission IDs must be an array" },
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Validate that all permission IDs exist
    const validPermissions = await prisma.permission.findMany({
      where: {
        id: {
          in: permissionIds,
        },
      },
    });

    if (validPermissions.length !== permissionIds.length) {
      return NextResponse.json(
        { error: "One or more permission IDs are invalid" },
        { status: 400 }
      );
    }

    // Update role permissions
    const updatedRole = await prisma.role.update({
      where: { id },
      data: {
        permissions: {
          set: permissionIds.map((permissionId: string) => ({
            id: permissionId,
          })),
        },
      },
      include: {
        permissions: {
          orderBy: {
            code: "asc",
          },
        },
      },
    });

    return NextResponse.json(updatedRole);
  } catch (error) {
    console.error("Error updating role permissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Add a permission to a role
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { id } = await context.params;
    const { permissionId } = await request.json();

    if (!permissionId) {
      return NextResponse.json(
        { error: "Permission ID is required" },
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        permissions: true,
      },
    });

    if (!existingRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Check if permission exists
    const permission = await prisma.permission.findUnique({
      where: { id: permissionId },
    });

    if (!permission) {
      return NextResponse.json(
        { error: "Permission not found" },
        { status: 404 }
      );
    }

    // Check if permission is already assigned to role
    const hasPermission = existingRole.permissions.some(
      (p) => p.id === permissionId
    );

    if (hasPermission) {
      return NextResponse.json(
        { error: "Permission already assigned to role" },
        { status: 409 }
      );
    }

    // Add permission to role
    const updatedRole = await prisma.role.update({
      where: { id },
      data: {
        permissions: {
          connect: { id: permissionId },
        },
      },
      include: {
        permissions: {
          orderBy: {
            code: "asc",
          },
        },
      },
    });

    return NextResponse.json(updatedRole);
  } catch (error) {
    console.error("Error adding permission to role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Remove a permission from a role
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    const { id } = await context.params;
    const { permissionId } = await request.json();

    if (!permissionId) {
      return NextResponse.json(
        { error: "Permission ID is required" },
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        permissions: true,
      },
    });

    if (!existingRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Check if permission is assigned to role
    const hasPermission = existingRole.permissions.some(
      (p) => p.id === permissionId
    );

    if (!hasPermission) {
      return NextResponse.json(
        { error: "Permission not assigned to role" },
        { status: 404 }
      );
    }

    // Remove permission from role
    const updatedRole = await prisma.role.update({
      where: { id },
      data: {
        permissions: {
          disconnect: { id: permissionId },
        },
      },
      include: {
        permissions: {
          orderBy: {
            code: "asc",
          },
        },
      },
    });

    return NextResponse.json(updatedRole);
  } catch (error) {
    console.error("Error removing permission from role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
