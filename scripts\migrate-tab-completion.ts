/**
 * Migration script to add TabCompletion table and initialize data
 * Run this after updating the Prisma schema
 */

import { prisma } from "../lib/db/prisma";

async function main() {
  console.log("🚀 Starting tab completion migration...");

  try {
    // Get all NPD projects with their tabs
    const products = await prisma.nPDProduct.findMany({
      include: {
        tabs: true,
      },
    });

    console.log(`📊 Found ${products.length} NPD projects to process`);

    for (const product of products) {
      console.log(
        `\n🔄 Processing NPD: ${product.name} (${product.tabs.length} tabs)`
      );

      for (const tab of product.tabs) {
        // Check if completion record already exists
        const existingCompletion =
          await prisma.nPDProductTabCompletion.findUnique({
            where: {
              npdProductId_npdProductTabId: {
                npdProductId: product.id,
                npdProductTabId: tab.id,
              },
            },
          });

        if (!existingCompletion) {
          // Create new completion record with 0% completion
          await prisma.nPDProductTabCompletion.create({
            data: {
              npdProductId: product.id,
              npdProductTabId: tab.id,
              completion: 0,
            },
          });
          console.log(`  ✅ Created completion record for tab: ${tab.tabName}`);
        } else {
          console.log(
            `  ⏭️  Completion record already exists for tab: ${tab.tabName}`
          );
        }
      }
    }

    console.log("\n🎉 Tab completion migration completed successfully!");
    console.log("\n📝 Next steps:");
    console.log(
      "1. Users can now set tab completion percentages using the settings icon in the product header"
    );
    console.log(
      "2. All completion values are stored in the database and persist across sessions"
    );
    console.log(
      "3. The progress indicator now shows only manually set values (no auto-calculation)"
    );
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
