"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";
import {
  getActivityUnreadCountsForProducts,
  markAllActivityLogsAsRead,
  markMultipleActivityLogsAsRead,
  markMultipleActivityLogsAsUnread,
} from "@/actions/activity.actions";

interface ActivityUnreadCounts {
  [productId: string]: number;
}

interface UseActivityUnreadResult {
  unreadCounts: ActivityUnreadCounts;
  totalUnread: number;
  isLoading: boolean;
  error: string | null;
  markAsRead: (activityIds: string[], productId: string) => Promise<boolean>;
  markAsUnread: (activityIds: string[]) => Promise<boolean>;
  markAllAsRead: (productId: string) => Promise<boolean>;
  refetch: () => Promise<void>;
}

// Global state to prevent multiple polling instances
let globalPollingActive = false;
let globalPollingInterval: NodeJS.Timeout | null = null;
let globalProductIds: string[] = [];
let globalUnreadCounts: ActivityUnreadCounts = {};
let globalTotalUnread = 0;
const globalSubscribers: Set<
  (counts: ActivityUnreadCounts, total: number) => void
> = new Set();

// Fetch unread counts using Server Action
async function fetchGlobalUnreadCounts(): Promise<void> {
  try {
    if (globalProductIds.length === 0) {
      globalUnreadCounts = {};
      globalTotalUnread = 0;

      // Notify all subscribers
      globalSubscribers.forEach((callback) =>
        callback(globalUnreadCounts, globalTotalUnread)
      );
      return;
    }

    const data = await getActivityUnreadCountsForProducts(globalProductIds);

    globalUnreadCounts = data.unreadCounts || {};
    globalTotalUnread = data.totalUnread || 0;

    // Notify all subscribers
    globalSubscribers.forEach((callback) =>
      callback(globalUnreadCounts, globalTotalUnread)
    );
  } catch (error) {
    console.error("Failed to fetch activity unread counts:", error);
  }
}

// Start global polling
function startGlobalPolling(): void {
  if (globalPollingActive || !isPollingEnabled("ACTIVITY_READ_STATUS")) {
    return;
  }

  globalPollingActive = true;
  const interval = getPollingInterval("ACTIVITY_READ_STATUS");

  // Initial fetch
  fetchGlobalUnreadCounts();

  // Setup polling
  globalPollingInterval = setInterval(() => {
    fetchGlobalUnreadCounts();
  }, interval);
}

// Stop global polling
function stopGlobalPolling(): void {
  if (globalPollingInterval) {
    clearInterval(globalPollingInterval);
    globalPollingInterval = null;
  }
  globalPollingActive = false;
}

// Update global product IDs for polling
function updateGlobalProductIds(productIds: string[]): void {
  const newIds = [...new Set(productIds)].sort();
  const currentIds = globalProductIds.sort();

  // Only update if the IDs actually changed
  if (JSON.stringify(newIds) !== JSON.stringify(currentIds)) {
    globalProductIds = newIds;

    // Trigger immediate fetch with new product IDs
    if (globalPollingActive) {
      fetchGlobalUnreadCounts();
    }
  }
}

// Refresh global counts (for cache invalidation)
export function refreshActivityUnreadCounts(): void {
  if (globalPollingActive) {
    fetchGlobalUnreadCounts();
  }
}

/**
 * Hook for managing activity unread counts
 * @param productIds Optional array of product IDs to track. If not provided, tracks all products.
 */
export function useActivityUnread(
  productIds?: string[]
): UseActivityUnreadResult {
  const [unreadCounts, setUnreadCounts] = useState<ActivityUnreadCounts>({});
  const [totalUnread, setTotalUnread] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Stable dependency for productIds
  const productIdsRef = useRef<string[]>([]);
  const productIdsString = JSON.stringify(productIds?.sort() || []);

  // Update product IDs when they change
  useEffect(() => {
    const newProductIds = productIds || [];
    productIdsRef.current = newProductIds;

    // Update global polling with new product IDs
    updateGlobalProductIds(newProductIds);
  }, [productIdsString, productIds]);

  // Subscribe to global updates
  useEffect(() => {
    const handleUpdate = (counts: ActivityUnreadCounts, total: number) => {
      setUnreadCounts(counts);
      setTotalUnread(total);
      setIsLoading(false);
      setError(null);
    };

    globalSubscribers.add(handleUpdate);

    // Start global polling
    startGlobalPolling();

    // Set initial state from global cache
    setUnreadCounts(globalUnreadCounts);
    setTotalUnread(globalTotalUnread);
    setIsLoading(false);

    return () => {
      // Unsubscribe
      globalSubscribers.delete(handleUpdate);

      // If no more subscribers, stop global polling
      if (globalSubscribers.size === 0) {
        stopGlobalPolling();
        globalProductIds = [];
      }
    };
  }, []);

  // Mark activities as read using Server Action
  const markAsRead = useCallback(
    async (activityIds: string[], productId: string): Promise<boolean> => {
      try {
        await markMultipleActivityLogsAsRead(activityIds, productId);
        // Refresh global counts immediately
        await fetchGlobalUnreadCounts();
        return true;
      } catch (error) {
        console.error("Failed to mark activities as read:", error);
        return false;
      }
    },
    []
  );

  // Mark activities as unread using Server Action
  const markAsUnread = useCallback(
    async (activityIds: string[]): Promise<boolean> => {
      try {
        await markMultipleActivityLogsAsUnread(activityIds);
        // Refresh global counts immediately
        await fetchGlobalUnreadCounts();
        return true;
      } catch (error) {
        console.error("Failed to mark activities as unread:", error);
        return false;
      }
    },
    []
  );

  // Mark all activities as read for a product using Server Action
  const markAllAsRead = useCallback(
    async (productId: string): Promise<boolean> => {
      try {
        await markAllActivityLogsAsRead(productId);
        // Refresh global counts immediately
        await fetchGlobalUnreadCounts();
        return true;
      } catch (error) {
        console.error("Failed to mark all activities as read:", error);
        return false;
      }
    },
    []
  );

  // Manual refetch
  const refetch = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    await fetchGlobalUnreadCounts();
  }, []);

  return {
    unreadCounts,
    totalUnread,
    isLoading,
    error,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    refetch,
  };
}
