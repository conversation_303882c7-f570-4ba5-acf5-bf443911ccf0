"use client";

import { useCallback, useState, useTransition } from "react";

import {
  sendChatMessage,
  updateChatMessage,
  deleteChatMessage,
  updateChatReadStatus,
  markAllChatMessagesAsRead,
  type ChatMessage,
} from "@/actions/chat.actions";

export function useChatActions(productId: string) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Send a new chat message
  const sendMessage = useCallback(
    async (
      message: string,
      mentions: string[] = []
    ): Promise<ChatMessage | null> => {
      if (!productId || !message.trim()) return null;

      setError(null);

      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            const result = await sendChatMessage(
              productId,
              message.trim(),
              mentions
            );
            resolve(result);
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : "Failed to send message";
            setError(errorMessage);
            resolve(null);
          }
        });
      });
    },
    [productId]
  );

  // Update an existing chat message
  const editMessage = useCallback(
    async (
      messageId: string,
      message: string,
      mentions: string[] = []
    ): Promise<ChatMessage | null> => {
      if (!messageId || !message.trim()) return null;

      setError(null);

      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            const result = await updateChatMessage(
              messageId,
              message.trim(),
              mentions
            );
            resolve(result);
          } catch (error) {
            const errorMessage =
              error instanceof Error
                ? error.message
                : "Failed to update message";
            setError(errorMessage);
            resolve(null);
          }
        });
      });
    },
    []
  );

  // Delete a chat message
  const deleteMessage = useCallback(
    async (messageId: string): Promise<boolean> => {
      if (!messageId) return false;

      setError(null);

      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            await deleteChatMessage(messageId);
            resolve(true);
          } catch (error) {
            const errorMessage =
              error instanceof Error
                ? error.message
                : "Failed to delete message";
            setError(errorMessage);
            resolve(false);
          }
        });
      });
    },
    []
  );

  // Toggle read status of a message
  const toggleReadStatus = useCallback(
    async (messageId: string, isCurrentlyRead: boolean): Promise<boolean> => {
      if (!productId || !messageId) return false;

      setError(null);

      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            await updateChatReadStatus(messageId, productId, !isCurrentlyRead);
            resolve(true);
          } catch (error) {
            const errorMessage =
              error instanceof Error
                ? error.message
                : "Failed to update read status";
            setError(errorMessage);
            resolve(false);
          }
        });
      });
    },
    [productId]
  );

  // Mark all messages as read
  const markAllAsRead = useCallback(async (): Promise<boolean> => {
    if (!productId) return false;

    setError(null);

    return new Promise((resolve) => {
      startTransition(async () => {
        try {
          await markAllChatMessagesAsRead(productId);
          resolve(true);
        } catch (error) {
          const errorMessage =
            error instanceof Error
              ? error.message
              : "Failed to mark all as read";
          setError(errorMessage);
          resolve(false);
        }
      });
    });
  }, [productId]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Actions
    sendMessage,
    editMessage,
    deleteMessage,
    toggleReadStatus,
    markAllAsRead,

    // State
    isPending,
    error,
    clearError,
  };
}
