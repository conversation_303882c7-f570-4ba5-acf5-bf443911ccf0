import { useCallback } from "react";
import { getFormFieldStyles, getTableCellStyles, isFieldModified } from "@/lib/utils/form-field-styles";

/**
 * Custom hook for managing form field styling with change indicators
 * Provides a consistent API for all form components
 */
export function useFormFieldStyles(modifiedFields: Set<string>) {
  /**
   * Get styling for regular form fields (Input, Textarea, Select, etc.)
   */
  const getFieldStyles = useCallback(
    (fieldName: string, hasError?: boolean): string => {
      const isModified = isFieldModified(fieldName, modifiedFields);
      return getFormFieldStyles(isModified, hasError);
    },
    [modifiedFields]
  );

  /**
   * Get styling for table cell fields (special handling for tables)
   */
  const getCellStyles = useCallback(
    (fieldName: string, hasError?: boolean): string => {
      const isModified = isFieldModified(fieldName, modifiedFields);
      return getTableCellStyles(isModified, hasError);
    },
    [modifiedFields]
  );

  /**
   * Check if a specific field is modified
   */
  const isModified = useCallback(
    (fieldName: string): boolean => {
      return isFieldModified(fieldName, modifiedFields);
    },
    [modifiedFields]
  );

  /**
   * Get the count of modified fields
   */
  const modifiedCount = modifiedFields.size;

  /**
   * Check if any fields are modified
   */
  const hasAnyModifications = modifiedCount > 0;

  return {
    getFieldStyles,
    getCellStyles,
    isModified,
    modifiedCount,
    hasAnyModifications,
  };
}

/**
 * Simplified hook for components that only need basic field styling
 */
export function useFieldStyles(modifiedFields: Set<string>) {
  return useCallback(
    (fieldName: string): string => {
      const isModified = isFieldModified(fieldName, modifiedFields);
      return getFormFieldStyles(isModified);
    },
    [modifiedFields]
  );
}
