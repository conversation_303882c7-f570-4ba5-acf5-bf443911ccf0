import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";

interface BaseEditableCellProps {
  value: string;
  onChange: (value: string) => void;
  cellId: string;
  copiedCell: string | null;
  onCopy: (text: string, cellId: string) => void;
  isEditing: boolean;
  className?: string;
  style?: React.CSSProperties;
  isModified?: boolean;
}

// Base EditableCell for regular text fields
export const EditableCell = React.memo(
  ({
    value,
    onChange,
    cellId,
    copiedCell,
    onCopy,
    isEditing,
    multiline = false,
    className = "",
    style = {},
    isModified = false,
  }: BaseEditableCellProps & { multiline?: boolean }) => {
    const [localValue, setLocalValue] = React.useState(value);
    const [isFocused, setIsFocused] = React.useState(false);

    React.useEffect(() => {
      if (!isFocused) {
        setLocalValue(value);
      }
    }, [value, isFocused]);

    const onChangeRef = React.useRef(onChange);
    React.useEffect(() => {
      onChangeRef.current = onChange;
    }, [onChange]);

    const stableOnChange = React.useCallback((newValue: string) => {
      setLocalValue(newValue);
      onChangeRef.current(newValue);
    }, []);

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
    }, []);

    const handleBlur = React.useCallback(() => {
      setIsFocused(false);
    }, []);

    // Helper function to get styling for modified fields
    const getModifiedStyling = () => {
      if (isModified) {
        return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
      }
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
    };

    if (isEditing) {
      if (multiline) {
        return (
          <Textarea
            value={localValue}
            onChange={(e) => stableOnChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={`${getModifiedStyling()} min-h-[40px] resize-none text-xs ${className}`}
            style={{ fontSize: "12px", ...style }}
          />
        );
      } else {
        return (
          <Input
            value={localValue}
            onChange={(e) => stableOnChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={`${getModifiedStyling()} h-6 text-xs ${className}`}
            style={{ fontSize: "12px", ...style }}
          />
        );
      }
    }

    const displayValue = value || "";
    return (
      <div
        className={`px-1 py-0.5 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all h-6 flex items-center overflow-hidden ${className}`}
        onClick={() => onCopy(displayValue, cellId)}
        title={displayValue || "-"}
        style={{ fontSize: "12px", ...style }}
      >
        <div className="relative truncate w-full">
          {displayValue || "-"}
          {copiedCell === cellId && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
              Copied!
            </div>
          )}
        </div>
      </div>
    );
  }
);

EditableCell.displayName = "EditableCell";

// Ratings Cell - max one decimal, always show one decimal
export const RatingEditableCell = React.memo((props: BaseEditableCellProps) => {
  const [localValue, setLocalValue] = React.useState(props.value);
  const [isFocused, setIsFocused] = React.useState(false);

  React.useEffect(() => {
    if (!isFocused) {
      setLocalValue(props.value);
    }
  }, [props.value, isFocused]);

  const onChangeRef = React.useRef(props.onChange);
  React.useEffect(() => {
    onChangeRef.current = props.onChange;
  }, [props.onChange]);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      // Allow only numbers and one decimal point
      if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) {
        setLocalValue(inputValue);
      }
    },
    []
  );

  const handleBlur = React.useCallback(() => {
    setIsFocused(false);
    let formattedValue = localValue;

    if (formattedValue && formattedValue.trim() !== "") {
      const num = parseFloat(formattedValue);
      if (!isNaN(num)) {
        // Format to one decimal place
        formattedValue = num.toFixed(1);
        setLocalValue(formattedValue);
        onChangeRef.current(formattedValue);
      }
    }
  }, [localValue]);

  const handleFocus = React.useCallback(() => {
    setIsFocused(true);
  }, []);

  // Helper function to get styling for modified fields
  const getModifiedStyling = () => {
    if (props.isModified) {
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
    }
    return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
  };

  if (props.isEditing) {
    return (
      <Input
        value={localValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
        style={{ fontSize: "12px", ...props.style }}
        placeholder="0.0"
      />
    );
  }

  const displayValue = props.value || "";
  return (
    <div
      className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
      onClick={() => props.onCopy(displayValue, props.cellId)}
      title={displayValue || "-"}
      style={{ fontSize: "12px", ...props.style }}
    >
      <div className="relative">
        {displayValue || "-"}
        {props.copiedCell === props.cellId && (
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
            Copied!
          </div>
        )}
      </div>
    </div>
  );
});

RatingEditableCell.displayName = "RatingEditableCell";

// Number Cell with thousands separators (ASIN Sales, BSR, Review Count)
export const NumberEditableCell = React.memo((props: BaseEditableCellProps) => {
  const [localValue, setLocalValue] = React.useState(props.value);
  const [isFocused, setIsFocused] = React.useState(false);

  React.useEffect(() => {
    if (!isFocused) {
      setLocalValue(props.value);
    }
  }, [props.value, isFocused]);

  const onChangeRef = React.useRef(props.onChange);
  React.useEffect(() => {
    onChangeRef.current = props.onChange;
  }, [props.onChange]);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      // Remove commas and check if it's a valid number
      const cleanValue = inputValue.replace(/,/g, "");
      if (/^[0-9]*$/.test(cleanValue)) {
        setLocalValue(inputValue);
      }
    },
    []
  );

  const handleBlur = React.useCallback(() => {
    setIsFocused(false);
    let formattedValue = localValue;

    if (formattedValue && formattedValue.trim() !== "") {
      // Remove existing commas
      const cleanValue = formattedValue.replace(/,/g, "");
      const num = parseInt(cleanValue, 10);
      if (!isNaN(num)) {
        // Add thousands separators
        formattedValue = num.toLocaleString();
        setLocalValue(formattedValue);
        onChangeRef.current(formattedValue);
      }
    }
  }, [localValue]);

  const handleFocus = React.useCallback(() => {
    setIsFocused(true);
  }, []);

  // Helper function to get styling for modified fields
  const getModifiedStyling = () => {
    if (props.isModified) {
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
    }
    return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
  };

  if (props.isEditing) {
    return (
      <Input
        value={localValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
        style={{ fontSize: "12px", ...props.style }}
        placeholder="0"
      />
    );
  }

  const displayValue = props.value || "";
  return (
    <div
      className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
      onClick={() => props.onCopy(displayValue, props.cellId)}
      title={displayValue || "-"}
      style={{ fontSize: "12px", ...props.style }}
    >
      <div className="relative">
        {displayValue || "-"}
        {props.copiedCell === props.cellId && (
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
            Copied!
          </div>
        )}
      </div>
    </div>
  );
});

NumberEditableCell.displayName = "NumberEditableCell";

// Price/Fee Cell - always two decimals, leading zero if needed
export const CurrencyEditableCell = React.memo(
  (props: BaseEditableCellProps) => {
    const [localValue, setLocalValue] = React.useState(props.value);
    const [isFocused, setIsFocused] = React.useState(false);

    React.useEffect(() => {
      if (!isFocused) {
        setLocalValue(props.value);
      }
    }, [props.value, isFocused]);

    const onChangeRef = React.useRef(props.onChange);
    React.useEffect(() => {
      onChangeRef.current = props.onChange;
    }, [props.onChange]);

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        // Allow only numbers, one decimal point, and dollar sign at the beginning
        if (/^(\$)?[0-9]*\.?[0-9]*$/.test(inputValue)) {
          setLocalValue(inputValue);
        }
      },
      []
    );

    const handleBlur = React.useCallback(() => {
      setIsFocused(false);
      let formattedValue = localValue;

      if (formattedValue && formattedValue.trim() !== "") {
        // Remove dollar sign for processing
        const cleanValue = formattedValue.replace("$", "");
        const num = parseFloat(cleanValue);
        if (!isNaN(num)) {
          // Format to two decimal places with dollar sign
          formattedValue = `$${num.toFixed(2)}`;
          setLocalValue(formattedValue);
          onChangeRef.current(formattedValue);
        }
      }
    }, [localValue]);

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
    }, []);

    // Helper function to get styling for modified fields
    const getModifiedStyling = () => {
      if (props.isModified) {
        return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
      }
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
    };

    if (props.isEditing) {
      return (
        <Input
          value={localValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
          style={{ fontSize: "12px", ...props.style }}
          placeholder="$0.00"
        />
      );
    }

    const displayValue = props.value || "";
    return (
      <div
        className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
        onClick={() => props.onCopy(displayValue, props.cellId)}
        title={displayValue || "-"}
        style={{ fontSize: "12px", ...props.style }}
      >
        <div className="relative">
          {displayValue || "-"}
          {props.copiedCell === props.cellId && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
              Copied!
            </div>
          )}
        </div>
      </div>
    );
  }
);

CurrencyEditableCell.displayName = "CurrencyEditableCell";

// Weight Cell - max two decimals, leading zero if needed
export const WeightEditableCell = React.memo((props: BaseEditableCellProps) => {
  const [localValue, setLocalValue] = React.useState(props.value);
  const [isFocused, setIsFocused] = React.useState(false);

  React.useEffect(() => {
    if (!isFocused) {
      setLocalValue(props.value);
    }
  }, [props.value, isFocused]);

  const onChangeRef = React.useRef(props.onChange);
  React.useEffect(() => {
    onChangeRef.current = props.onChange;
  }, [props.onChange]);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      // Allow only numbers and one decimal point
      if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) {
        setLocalValue(inputValue);
      }
    },
    []
  );

  const handleBlur = React.useCallback(() => {
    setIsFocused(false);
    let formattedValue = localValue;

    if (formattedValue && formattedValue.trim() !== "") {
      const num = parseFloat(formattedValue);
      if (!isNaN(num)) {
        // Format to always show two decimal places
        formattedValue = num.toFixed(2);
        setLocalValue(formattedValue);
        onChangeRef.current(formattedValue);
      }
    }
  }, [localValue]);

  const handleFocus = React.useCallback(() => {
    setIsFocused(true);
  }, []);

  // Helper function to get styling for modified fields
  const getModifiedStyling = () => {
    if (props.isModified) {
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
    }
    return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
  };

  if (props.isEditing) {
    return (
      <Input
        value={localValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
        style={{ fontSize: "12px", ...props.style }}
        placeholder="0.00"
      />
    );
  }

  const displayValue = props.value || "";
  return (
    <div
      className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
      onClick={() => props.onCopy(displayValue, props.cellId)}
      title={displayValue || "-"}
      style={{ fontSize: "12px", ...props.style }}
    >
      <div className="relative">
        {displayValue || "-"}
        {props.copiedCell === props.cellId && (
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
            Copied!
          </div>
        )}
      </div>
    </div>
  );
});

WeightEditableCell.displayName = "WeightEditableCell";

// Country Cell - capitalize, max 3 chars
export const CountryEditableCell = React.memo(
  (props: BaseEditableCellProps) => {
    const [localValue, setLocalValue] = React.useState(props.value);
    const [isFocused, setIsFocused] = React.useState(false);

    React.useEffect(() => {
      if (!isFocused) {
        setLocalValue(props.value);
      }
    }, [props.value, isFocused]);

    const onChangeRef = React.useRef(props.onChange);
    React.useEffect(() => {
      onChangeRef.current = props.onChange;
    }, [props.onChange]);

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        // Allow only letters, max 3 characters
        if (/^[A-Za-z]{0,3}$/.test(inputValue)) {
          setLocalValue(inputValue.toUpperCase());
        }
      },
      []
    );

    const handleBlur = React.useCallback(() => {
      setIsFocused(false);
      const formattedValue = localValue.toUpperCase();
      if (formattedValue !== localValue) {
        setLocalValue(formattedValue);
      }
      onChangeRef.current(formattedValue);
    }, [localValue]);

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
    }, []);

    // Helper function to get styling for modified fields
    const getModifiedStyling = () => {
      if (props.isModified) {
        return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
      }
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
    };

    if (props.isEditing) {
      return (
        <Input
          value={localValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
          style={{ fontSize: "12px", ...props.style }}
          placeholder="USA"
          maxLength={3}
        />
      );
    }

    const displayValue = props.value || "";
    return (
      <div
        className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
        onClick={() => props.onCopy(displayValue, props.cellId)}
        title={displayValue || "-"}
        style={{ fontSize: "12px", ...props.style }}
      >
        <div className="relative">
          {displayValue || "-"}
          {props.copiedCell === props.cellId && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
              Copied!
            </div>
          )}
        </div>
      </div>
    );
  }
);

CountryEditableCell.displayName = "CountryEditableCell";

// Fulfillment Cell - 3 uppercase letters, only letters allowed
export const FulfillmentEditableCell = React.memo(
  (props: BaseEditableCellProps) => {
    const [localValue, setLocalValue] = React.useState(props.value);
    const [isFocused, setIsFocused] = React.useState(false);

    React.useEffect(() => {
      if (!isFocused) {
        setLocalValue(props.value);
      }
    }, [props.value, isFocused]);

    const onChangeRef = React.useRef(props.onChange);
    React.useEffect(() => {
      onChangeRef.current = props.onChange;
    }, [props.onChange]);

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        // Allow only letters, max 3 characters
        if (/^[A-Za-z]{0,3}$/.test(inputValue)) {
          setLocalValue(inputValue.toUpperCase());
        }
      },
      []
    );

    const handleBlur = React.useCallback(() => {
      setIsFocused(false);
      const formattedValue = localValue.toUpperCase();
      if (formattedValue !== localValue) {
        setLocalValue(formattedValue);
      }
      onChangeRef.current(formattedValue);
    }, [localValue]);

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
    }, []);

    // Helper function to get styling for modified fields
    const getModifiedStyling = () => {
      if (props.isModified) {
        return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
      }
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
    };

    if (props.isEditing) {
      return (
        <Input
          value={localValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
          style={{ fontSize: "12px", ...props.style }}
          placeholder="FBA"
          maxLength={3}
        />
      );
    }

    const displayValue = props.value || "";
    return (
      <div
        className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
        onClick={() => props.onCopy(displayValue, props.cellId)}
        title={displayValue || "-"}
        style={{ fontSize: "12px", ...props.style }}
      >
        <div className="relative">
          {displayValue || "-"}
          {props.copiedCell === props.cellId && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
              Copied!
            </div>
          )}
        </div>
      </div>
    );
  }
);

FulfillmentEditableCell.displayName = "FulfillmentEditableCell";

// Helper functions for date conversion
const convertToDateInputFormat = (dateString: string): string => {
  if (!dateString) return "";

  // If already in YYYY-MM-DD format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // If in MM/DD/YYYY format, convert to YYYY-MM-DD
  if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
    const [month, day, year] = dateString.split("/");
    return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
  }

  // If in "MMM DD, YYYY" format (e.g., "Oct 28, 2022"), convert to YYYY-MM-DD
  if (/^[A-Za-z]{3}\s+\d{1,2},\s+\d{4}$/.test(dateString)) {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split("T")[0];
    }
  }

  // Try to parse as a date and format it (fallback for other formats)
  const date = new Date(dateString);
  if (!isNaN(date.getTime())) {
    return date.toISOString().split("T")[0];
  }

  return dateString;
};

const convertFromDateInputFormat = (dateString: string): string => {
  // HTML5 date input returns YYYY-MM-DD format
  // Keep it in YYYY-MM-DD format for consistency across the app
  return dateString;
};

// Date Cell - handles date format conversion for HTML5 date input
export const DateEditableCell = React.memo((props: BaseEditableCellProps) => {
  const [localValue, setLocalValue] = React.useState(() => {
    // Convert date to YYYY-MM-DD format for HTML5 date input
    return convertToDateInputFormat(props.value);
  });
  const [isFocused, setIsFocused] = React.useState(false);

  React.useEffect(() => {
    if (!isFocused) {
      setLocalValue(convertToDateInputFormat(props.value));
    }
  }, [props.value, isFocused]);

  const onChangeRef = React.useRef(props.onChange);
  React.useEffect(() => {
    onChangeRef.current = props.onChange;
  }, [props.onChange]);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value; // This will be in YYYY-MM-DD format from HTML5 date input
      setLocalValue(inputValue);
      // Convert back to the format expected by the app (keeping YYYY-MM-DD for consistency)
      onChangeRef.current(convertFromDateInputFormat(inputValue));
    },
    []
  );

  const handleFocus = React.useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = React.useCallback(() => {
    setIsFocused(false);
  }, []);

  // Helper function to get styling for modified fields
  const getModifiedStyling = () => {
    if (props.isModified) {
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
    }
    return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
  };

  if (props.isEditing) {
    return (
      <Input
        type="date"
        value={localValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
        style={{ fontSize: "12px", ...props.style }}
      />
    );
  }

  const displayValue = props.value || "";
  return (
    <div
      className={`px-1 py-0 cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all ${props.className}`}
      onClick={() => props.onCopy(displayValue, props.cellId)}
      title={displayValue || "-"}
      style={{ fontSize: "12px", ...props.style }}
    >
      <div className="relative">
        {displayValue || "-"}
        {props.copiedCell === props.cellId && (
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
            Copied!
          </div>
        )}
      </div>
    </div>
  );
});

DateEditableCell.displayName = "DateEditableCell";

// URL EditableCell with validation (must start with https://)
export const UrlEditableCell = React.memo((props: BaseEditableCellProps) => {
  const [localValue, setLocalValue] = React.useState(props.value);
  const [isFocused, setIsFocused] = React.useState(false);
  const [isValid, setIsValid] = React.useState(true);

  React.useEffect(() => {
    if (!isFocused) {
      setLocalValue(props.value);
      setIsValid(true); // Reset validation state when not focused
    }
  }, [props.value, isFocused]);

  const onChangeRef = React.useRef(props.onChange);
  React.useEffect(() => {
    onChangeRef.current = props.onChange;
  }, [props.onChange]);

  const validateUrl = React.useCallback((url: string): boolean => {
    if (!url.trim()) return true; // Empty URLs are allowed
    return url.startsWith("https://");
  }, []);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);
      const valid = validateUrl(newValue);
      setIsValid(valid);

      // Only call onChange if valid or empty
      if (valid || !newValue.trim()) {
        onChangeRef.current(newValue);
      }
    },
    [validateUrl]
  );

  const handleFocus = React.useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = React.useCallback(() => {
    setIsFocused(false);
    // Final validation on blur - if invalid, show original value
    if (!validateUrl(localValue)) {
      setLocalValue(props.value);
      setIsValid(true);
    }
  }, [localValue, props.value, validateUrl]);

  // Helper function to get styling for modified fields
  const getModifiedStyling = () => {
    if (!isValid) {
      return "border-red-300 focus:border-red-500 bg-red-50/50 p-1";
    }
    if (props.isModified) {
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
    }
    return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
  };

  if (props.isEditing) {
    return (
      <Input
        type="url"
        value={localValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder="https://..."
        className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
        style={{ fontSize: "12px", ...props.style }}
        title={!isValid ? "URL must start with https://" : ""}
      />
    );
  }

  const displayValue = props.value || "";
  return (
    <div className="px-1 py-0 text-center border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all">
      {displayValue ? (
        <div className="relative">
          <div
            className="text-blue-600 hover:text-blue-800 cursor-pointer no-underline inline-block"
            style={{ fontSize: "12px" }}
            title="Click to copy URL and open in new tab"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              // Copy URL
              props.onCopy(displayValue, props.cellId);
              // Open in new tab
              window.open(displayValue, "_blank", "noopener,noreferrer");
            }}
          >
            🔗
          </div>
          {props.copiedCell === props.cellId && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
              Copied!
            </div>
          )}
        </div>
      ) : (
        <span className="text-gray-400" style={{ fontSize: "12px" }}>
          -
        </span>
      )}
    </div>
  );
});

UrlEditableCell.displayName = "UrlEditableCell";

// Image URL EditableCell with validation (must start with https://m.media-amazon.com/)
export const ImageUrlEditableCell = React.memo(
  (props: BaseEditableCellProps) => {
    const [localValue, setLocalValue] = React.useState(props.value);
    const [isFocused, setIsFocused] = React.useState(false);
    const [isValid, setIsValid] = React.useState(true);

    React.useEffect(() => {
      if (!isFocused) {
        setLocalValue(props.value);
        setIsValid(true); // Reset validation state when not focused
      }
    }, [props.value, isFocused]);

    const onChangeRef = React.useRef(props.onChange);
    React.useEffect(() => {
      onChangeRef.current = props.onChange;
    }, [props.onChange]);

    const validateImageUrl = React.useCallback((url: string): boolean => {
      if (!url.trim()) return true; // Empty URLs are allowed
      return url.startsWith("https://m.media-amazon.com/");
    }, []);

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        const valid = validateImageUrl(newValue);
        setIsValid(valid);

        // Only call onChange if valid or empty
        if (valid || !newValue.trim()) {
          onChangeRef.current(newValue);
        }
      },
      [validateImageUrl]
    );

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
    }, []);

    const handleBlur = React.useCallback(() => {
      setIsFocused(false);
      // Final validation on blur - if invalid, show original value
      if (!validateImageUrl(localValue)) {
        setLocalValue(props.value);
        setIsValid(true);
      }
    }, [localValue, props.value, validateImageUrl]);

    // Helper function to get styling for modified fields
    const getModifiedStyling = () => {
      if (!isValid) {
        return "border-red-300 focus:border-red-500 bg-red-50/50 p-1";
      }
      if (props.isModified) {
        return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-orange-500 ring-2 ring-orange-200";
      }
      return "border-0 bg-transparent p-1 focus:bg-white focus:border focus:border-blue-300";
    };

    if (props.isEditing) {
      return (
        <Input
          type="url"
          value={localValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder="https://m.media-amazon.com/..."
          className={`${getModifiedStyling()} h-6 text-xs ${props.className}`}
          style={{ fontSize: "12px", ...props.style }}
          title={
            !isValid
              ? "Image URL must start with https://m.media-amazon.com/"
              : ""
          }
        />
      );
    }

    // When not editing, show the image preview (reuse the existing ImageCell logic)
    const imageUrl = props.value || "";
    return (
      <div className="px-1 py-0 text-center cursor-pointer rounded border-2 border-transparent hover:border-blue-300 focus:border-blue-500 transition-all">
        {imageUrl ? (
          <div className="relative flex flex-col items-center">
            <Image
              src={imageUrl}
              alt="Product"
              width={32}
              height={32}
              className="w-8 h-8 object-cover rounded mx-auto"
              style={{ fontSize: "12px" }}
              title="Click to copy image URL"
              onClick={() => props.onCopy(imageUrl, props.cellId)}
              onError={(e) => {
                // If image fails to load, show a placeholder
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
              }}
              unoptimized
            />
            <div className="hidden text-gray-400 text-xs">📷</div>
            {props.copiedCell === props.cellId && (
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded shadow-lg z-50">
                Copied!
              </div>
            )}
          </div>
        ) : (
          <span className="text-gray-400" style={{ fontSize: "12px" }}>
            -
          </span>
        )}
      </div>
    );
  }
);

ImageUrlEditableCell.displayName = "ImageUrlEditableCell";
