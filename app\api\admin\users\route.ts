import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db/prisma";

export async function GET() {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user has admin or super admin role
    const hasAdminAccess =
      currentUser.roles.includes("ADMIN") ||
      currentUser.roles.includes("SUPER_ADMIN");

    if (!hasAdminAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get all users with their roles
    const users = await prisma.user.findMany({
      include: {
        roles: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
