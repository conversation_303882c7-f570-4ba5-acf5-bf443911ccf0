import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";

interface NavigationWarningDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  variant?: "discard" | "leave";
}

export function NavigationWarningDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  variant = "discard",
}: NavigationWarningDialogProps) {
  const getDialogContent = () => {
    switch (variant) {
      case "leave":
        return {
          title: "Unsaved Changes",
          description:
            "You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost.",
          confirmText: "Leave Page",
          cancelText: "Stay Here",
          dialogVariant: "destructive" as const,
        };
      case "discard":
      default:
        return {
          title: "Unsaved Changes",
          description:
            "You have unsaved changes. Are you sure you want to discard them?",
          confirmText: "Discard Changes",
          cancelText: "Keep Editing",
          dialogVariant: "destructive" as const,
        };
    }
  };

  const content = getDialogContent();

  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title={content.title}
      description={content.description}
      confirmText={content.confirmText}
      cancelText={content.cancelText}
      onConfirm={onConfirm}
      onCancel={onCancel}
      variant={content.dialogVariant}
    />
  );
}
