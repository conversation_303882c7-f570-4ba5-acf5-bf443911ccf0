import { jwtVerify, SignJWT } from "jose";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { isProduction } from "./constants";

const secret = new TextEncoder().encode(process.env.AUTH_SECRET);
const cookieName = "quickt-auth-token";

export interface AuthPayload {
  userId: string;
  email: string;
  name: string;
  image?: string | null;
  provider?: string | null;
  roles: string[];
}

// Create and sign a JWT token
export async function signToken(payload: AuthPayload): Promise<string> {
  const jwtPayload = {
    userId: payload.userId,
    email: payload.email,
    name: payload.name,
    image: payload.image,
    provider: payload.provider,
    roles: payload.roles,
  };

  const token = await new SignJWT(jwtPayload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .setIssuer("quickt-backend")
    .setAudience("quickt-app")
    .setSubject(payload.userId)
    .sign(secret);

  return token;
}

export async function signAuthToken(userId: string): Promise<string> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { roles: true },
  });

  if (!user) {
    throw new Error("User not found for token signing.");
  }

  const token = await signToken({
    userId: user.id,
    email: user.email,
    name: user.name,
    image: user.image,
    provider: user.provider,
    roles: user.roles.map((role: { name: string }) => role.name),
  });

  return token;
}

// Verify and decrypt token
export async function verifyAuthToken(token: string): Promise<AuthPayload> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload as unknown as AuthPayload;
  } catch {
    throw new Error("Token verification failed");
  }
}

// Set the auth cookie
export async function setAuthCookie(token: string) {
  try {
    const cookieStore = await cookies();
    cookieStore.set(cookieName, token, {
      httpOnly: true,
      sameSite: "lax",
      secure: isProduction,
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });
  } catch (error) {
    console.error("❌ Failed to set auth cookie:", error);
    throw error;
  }
}

// Set auth cookie on NextResponse (for API routes)
export function setAuthCookieOnResponse(response: NextResponse, token: string) {
  response.cookies.set(cookieName, token, {
    httpOnly: true,
    secure: isProduction,
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });
}

// Get the auth token from cookie
export async function getAuthCookie(): Promise<string | undefined> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(cookieName);

    return token?.value;
  } catch (error) {
    console.error("Failed to get auth cookie:", error);
    return undefined;
  }
}

// Remove auth token cookie
export async function removeAuthCookie() {
  try {
    const cookieStore = await cookies();

    // Use set with expired date (most reliable for serverless)
    cookieStore.set(cookieName, "", {
      expires: new Date("1970-01-01"),
      path: "/",
      maxAge: -1,
      httpOnly: true,
      secure: isProduction,
      sameSite: "lax",
    });

    // Also try delete method as fallback
    try {
      cookieStore.delete(cookieName);
    } catch {
      // Delete method may fail in serverless, ignore
    }
  } catch (error) {
    console.error("Failed to remove auth cookie:", error);
    throw error;
  }
}

// Get current user from cookie
export async function getCurrentUser(): Promise<AuthPayload | null> {
  try {
    const token = await getAuthCookie();
    if (!token) return null;

    return await verifyAuthToken(token);
  } catch (error) {
    console.error("Failed to get current user:", error);
    return null;
  }
}
