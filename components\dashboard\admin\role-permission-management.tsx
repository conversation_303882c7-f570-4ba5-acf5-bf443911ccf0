"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Edit, Trash2, Shield, Key, Users } from "lucide-react";
import Spinner from "@/components/spinner";

interface Permission {
  id: string;
  code: string;
  roles?: { id: string; name: string }[];
}

interface Role {
  id: string;
  name: string;
  permissions: { id: string; code: string }[];
  _count?: { users: number };
}

interface RolePermissionManagementProps {
  userRole: string;
}

export function RolePermissionManagement({
  userRole,
}: RolePermissionManagementProps) {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Permission form state
  const [newPermissionCode, setNewPermissionCode] = useState("");
  const [editingPermission, setEditingPermission] = useState<Permission | null>(
    null
  );
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false);

  // Role form state
  const [newRoleName, setNewRoleName] = useState("");
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);

  // Check if user is Super Admin
  const isSuperAdmin = userRole === "SUPER_ADMIN";

  useEffect(() => {
    if (isSuperAdmin) {
      fetchData();
    }
  }, [isSuperAdmin]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [permissionsRes, rolesRes] = await Promise.all([
        fetch("/api/admin/permissions"),
        fetch("/api/admin/roles"),
      ]);

      if (permissionsRes.ok && rolesRes.ok) {
        const [permissionsData, rolesData] = await Promise.all([
          permissionsRes.json(),
          rolesRes.json(),
        ]);
        setPermissions(permissionsData);
        setRoles(rolesData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  // Permission management functions
  const handleCreatePermission = async () => {
    if (!newPermissionCode.trim()) {
      toast.error("Permission code is required");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/admin/permissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ code: newPermissionCode.trim().toUpperCase() }),
      });

      if (response.ok) {
        toast.success("Permission created successfully");
        setNewPermissionCode("");
        setIsPermissionDialogOpen(false);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to create permission");
      }
    } catch {
      toast.error("Failed to create permission");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdatePermission = async () => {
    if (!editingPermission || !newPermissionCode.trim()) {
      toast.error("Permission code is required");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/admin/permissions/${editingPermission.id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            code: newPermissionCode.trim().toUpperCase(),
          }),
        }
      );

      if (response.ok) {
        toast.success("Permission updated successfully");
        setEditingPermission(null);
        setNewPermissionCode("");
        setIsPermissionDialogOpen(false);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to update permission");
      }
    } catch {
      toast.error("Failed to update permission");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeletePermission = async (permission: Permission) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/permissions/${permission.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Permission deleted successfully");
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to delete permission");
      }
    } catch {
      toast.error("Failed to delete permission");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Role management functions
  const handleCreateRole = async () => {
    if (!newRoleName.trim()) {
      toast.error("Role name is required");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/admin/roles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: newRoleName.trim().toUpperCase(),
          permissionIds: selectedPermissions,
        }),
      });

      if (response.ok) {
        toast.success("Role created successfully");
        setNewRoleName("");
        setSelectedPermissions([]);
        setIsRoleDialogOpen(false);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to create role");
      }
    } catch {
      toast.error("Failed to create role");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateRole = async () => {
    if (!editingRole || !newRoleName.trim()) {
      toast.error("Role name is required");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/roles/${editingRole.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: newRoleName.trim().toUpperCase(),
          permissionIds: selectedPermissions,
        }),
      });

      if (response.ok) {
        toast.success("Role updated successfully");
        setEditingRole(null);
        setNewRoleName("");
        setSelectedPermissions([]);
        setIsRoleDialogOpen(false);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to update role");
      }
    } catch {
      toast.error("Failed to update role");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteRole = async (role: Role) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/roles/${role.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Role deleted successfully");
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to delete role");
      }
    } catch {
      toast.error("Failed to delete role");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper functions
  const openPermissionDialog = (permission?: Permission) => {
    if (permission) {
      setEditingPermission(permission);
      setNewPermissionCode(permission.code);
    } else {
      setEditingPermission(null);
      setNewPermissionCode("");
    }
    setIsPermissionDialogOpen(true);
  };

  const openRoleDialog = (role?: Role) => {
    if (role) {
      setEditingRole(role);
      setNewRoleName(role.name);
      setSelectedPermissions(role.permissions.map((p) => p.id));
    } else {
      setEditingRole(null);
      setNewRoleName("");
      setSelectedPermissions([]);
    }
    setIsRoleDialogOpen(true);
  };

  const togglePermissionSelection = (permissionId: string) => {
    setSelectedPermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // Group permissions by category
  const groupedPermissions = permissions.reduce((acc, permission) => {
    const category = permission.code.split("_")[0];
    if (!acc[category]) acc[category] = [];
    acc[category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  if (!isSuperAdmin) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Only Super Administrators can manage roles and permissions.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner loading={true} size={12} />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Permissions Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Permissions Management
              </CardTitle>
              <CardDescription>
                Manage system permissions. Permissions define what actions users
                can perform.
              </CardDescription>
            </div>
            <Dialog
              open={isPermissionDialogOpen}
              onOpenChange={setIsPermissionDialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={() => openPermissionDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Permission
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingPermission
                      ? "Edit Permission"
                      : "Create Permission"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingPermission
                      ? "Update the permission code. Use UPPERCASE with underscores."
                      : "Create a new permission. Use UPPERCASE with underscores (e.g., PRODUCT_VIEW)."}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="permission-code">Permission Code</Label>
                    <Input
                      id="permission-code"
                      value={newPermissionCode}
                      onChange={(e) =>
                        setNewPermissionCode(e.target.value.toUpperCase())
                      }
                      placeholder="PRODUCT_VIEW"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsPermissionDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={
                      editingPermission
                        ? handleUpdatePermission
                        : handleCreatePermission
                    }
                    disabled={isSubmitting}
                  >
                    {isSubmitting && <Spinner loading={true} size={8} />}
                    {editingPermission ? "Update" : "Create"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(groupedPermissions).map(
              ([category, categoryPermissions]) => (
                <div key={category}>
                  <h3 className="font-semibold text-lg mb-3 capitalize">
                    {category.toLowerCase().replace("_", " ")} Permissions
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {categoryPermissions.map((permission) => (
                      <div
                        key={permission.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div>
                          <Badge variant="secondary">{permission.code}</Badge>
                          {permission.roles && permission.roles.length > 0 && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Used by {permission.roles.length} role(s)
                            </p>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openPermissionDialog(permission)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Delete Permission
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete the permission
                                  &quot;{permission.code}&quot;? This will
                                  remove it from all roles and cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() =>
                                    handleDeletePermission(permission)
                                  }
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>

      {/* Roles Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Roles Management
              </CardTitle>
              <CardDescription>
                Manage user roles and their permissions. Roles group permissions
                together.
              </CardDescription>
            </div>
            <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => openRoleDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Role
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingRole ? "Edit Role" : "Create Role"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingRole
                      ? "Update the role name and permissions."
                      : "Create a new role and assign permissions."}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="role-name">Role Name</Label>
                    <Input
                      id="role-name"
                      value={newRoleName}
                      onChange={(e) =>
                        setNewRoleName(e.target.value.toUpperCase())
                      }
                      placeholder="MARKETING"
                    />
                  </div>

                  <div>
                    <Label>Permissions</Label>
                    <div className="mt-2 space-y-4 max-h-60 overflow-y-auto border rounded-lg p-4">
                      {Object.entries(groupedPermissions).map(
                        ([category, categoryPermissions]) => (
                          <div key={category}>
                            <h4 className="font-medium mb-2 capitalize">
                              {category.toLowerCase().replace("_", " ")}
                            </h4>
                            <div className="grid grid-cols-2 gap-2 ml-4">
                              {categoryPermissions.map((permission) => (
                                <div
                                  key={permission.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={permission.id}
                                    checked={selectedPermissions.includes(
                                      permission.id
                                    )}
                                    onCheckedChange={() =>
                                      togglePermissionSelection(permission.id)
                                    }
                                  />
                                  <Label
                                    htmlFor={permission.id}
                                    className="text-sm font-normal cursor-pointer"
                                  >
                                    {permission.code}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Selected: {selectedPermissions.length} permissions
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsRoleDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={editingRole ? handleUpdateRole : handleCreateRole}
                    disabled={isSubmitting}
                  >
                    {isSubmitting && <Spinner loading={true} size={8} />}
                    {editingRole ? "Update" : "Create"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roles.map((role) => (
              <div key={role.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className="text-sm">
                    {role.name}
                  </Badge>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openRoleDialog(role)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled={Boolean(
                            role._count?.users && role._count.users > 0
                          )}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Role</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete the role &quot;
                            {role.name}&quot;? This cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteRole(role)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {role.permissions.length} permissions
                  </p>
                  {role._count?.users !== undefined && (
                    <p className="text-sm text-muted-foreground">
                      {role._count.users} user(s) assigned
                    </p>
                  )}

                  <div className="flex flex-wrap gap-1 mt-2">
                    {role.permissions.slice(0, 3).map((permission) => (
                      <Badge
                        key={permission.id}
                        variant="secondary"
                        className="text-xs"
                      >
                        {permission.code}
                      </Badge>
                    ))}
                    {role.permissions.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{role.permissions.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
