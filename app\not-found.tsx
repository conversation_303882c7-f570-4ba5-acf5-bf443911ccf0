"use client";
import { APP_NAME } from "@/lib/constants";
import Image from "next/image";
import { Button } from "@/components/ui/button";

const NotFoundPage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Image
        src="/images/logo.svg"
        width={0}
        height={0}
        alt={`${APP_NAME} Logo`}
        priority={true}
        style={{ width: "auto", height: "48px" }}
      />
      <div className="text-2xl font-bold mt-4">Page Not Found</div>
      <div className="text-gray-500 mt-2">
        The page you are looking for does not exist.
      </div>
      <Button
        variant="outline"
        className="mt-4"
        onClick={() => (window.location.href = "/")}
      >
        Go to Home
      </Button>
    </div>
  );
};

export default NotFoundPage;
