"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useNPDContext } from "@/context/npd-context";
import { useAuth } from "@/context/auth-context";
import { usePermissions } from "@/hooks/use-permissions";
import { deleteNPD } from "@/actions/npd.actions";
import { requestProductDeletion } from "@/actions/npd-deletion.actions";
import { BRANDS } from "@/lib/constants/brands";
import {
  PRODUCT_STAGES,
  type ProductStage,
} from "@/lib/constants/product-stages";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>it<PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  SettingsIcon,
  TrashIcon,
  ArrowLeftIcon,
  SaveIcon,
  EditIcon,
  Package,
  PackageIcon,
  Users,
} from "lucide-react";
import { toast } from "sonner";
import { useNavigationGuard } from "@/context/navigation-guard-context";

// Import shared form components to match new NPD page
import {
  TextField,
  SelectField,
  StageSelectField,
  UserSelectionField,
} from "@/components/dashboard/forms/shared/form-fields";
import {
  createProductSchema,
  validateForm as validateFormSchema,
  type FormErrors,
} from "@/components/dashboard/forms/shared/form-validation";
import { useUsers } from "@/components/dashboard/forms/shared/hooks/use-users";
import { PageHeader } from "@/components/dashboard/shared";
import Spinner from "@/components/spinner";

interface ProductSettingsContentProps {
  slug: string;
}

interface ProductSettingsForm {
  name: string;
  brand: string;
  stage: string;
  description: string;
  imageUrl: string;
  slug: string;
  subscribedUserIds: string[];
}

export function ProductSettingsContent({ slug }: ProductSettingsContentProps) {
  const router = useRouter();
  const {
    selectedProduct,
    isLoadingSelectedProduct,
    selectedProductError,
    selectProductBySlug,
    updateProductSettings,
  } = useNPDContext();

  const { user } = useAuth();
  const { isAdmin, isSuperAdmin } = usePermissions();
  const { users, isLoading: isLoadingUsers } = useUsers();

  const [formData, setFormData] = useState<ProductSettingsForm>({
    name: "",
    brand: "",
    stage: "",
    description: "",
    imageUrl: "",
    slug: "",
    subscribedUserIds: [],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [localLoading, setLocalLoading] = useState(true);
  const [currentSlug, setCurrentSlug] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDeleteRequestDialog, setShowDeleteRequestDialog] = useState(false);
  const [deleteRequestReason, setDeleteRequestReason] = useState("");
  const [slugValidation, setSlugValidation] = useState<{
    isValid: boolean;
    isChecking: boolean;
    message: string;
  }>({ isValid: true, isChecking: false, message: "" });

  const { setHasUnsavedChanges: setGlobalUnsavedChanges } =
    useNavigationGuard();

  // Debounce timer ref
  const debounceTimerRef = React.useRef<NodeJS.Timeout | null>(null);

  // Load product data when component mounts or slug changes
  useEffect(() => {
    if (slug && slug !== currentSlug) {
      setCurrentSlug(slug);
      setLocalLoading(true);
      selectProductBySlug(slug);
    }
  }, [slug, currentSlug, selectProductBySlug]);

  // Clear local loading when we have the correct product or error
  useEffect(() => {
    if (selectedProduct && selectedProduct.slug === currentSlug) {
      setLocalLoading(false);
    } else if (selectedProductError && currentSlug) {
      setLocalLoading(false);
    }
  }, [selectedProduct, selectedProductError, currentSlug]);

  // Update form data when product loads
  useEffect(() => {
    if (selectedProduct) {
      const subscribedUserIds =
        selectedProduct.subscriptions?.map((sub) => sub.userId) || [];
      setFormData({
        name: selectedProduct.name,
        brand: selectedProduct.brand,
        stage: selectedProduct.stage,
        description: selectedProduct.description || "",
        imageUrl: selectedProduct.imageUrl || "",
        slug: selectedProduct.slug,
        subscribedUserIds,
      });
      setHasUnsavedChanges(false);
    }
  }, [selectedProduct]);

  // Set global unsaved changes state
  useEffect(() => {
    setGlobalUnsavedChanges(hasUnsavedChanges);
  }, [hasUnsavedChanges, setGlobalUnsavedChanges]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Handle input changes with validation
  const handleInputChange = (
    field: keyof ProductSettingsForm,
    value: string | string[]
  ) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);
    setHasUnsavedChanges(checkForChanges(newFormData));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  // Function to check if current form data differs from original product data
  const checkForChanges = (newFormData: ProductSettingsForm) => {
    if (!selectedProduct) return false;

    const originalSubscribedIds =
      selectedProduct.subscriptions?.map((sub) => sub.userId) || [];
    const currentSubscribedIds = newFormData.subscribedUserIds || [];

    const formChanged =
      newFormData.name !== selectedProduct.name ||
      newFormData.brand !== selectedProduct.brand ||
      newFormData.stage !== selectedProduct.stage ||
      newFormData.slug !== selectedProduct.slug ||
      newFormData.description !== (selectedProduct.description || "") ||
      newFormData.imageUrl !== (selectedProduct.imageUrl || "");

    // Check if subscription changes were made
    const subscriptionChanged =
      originalSubscribedIds.length !== currentSubscribedIds.length ||
      originalSubscribedIds.some((id) => !currentSubscribedIds.includes(id)) ||
      currentSubscribedIds.some((id) => !originalSubscribedIds.includes(id));

    return formChanged || subscriptionChanged;
  };

  // Prepare options for select fields
  const brandOptions = BRANDS.map((brand) => ({
    value: brand,
    label: brand,
  }));

  const stageOptions = PRODUCT_STAGES.map((stage) => ({
    value: stage.value,
    label: stage.label,
  }));

  // Handle slug input changes with validation
  const handleSlugChange = (value: string) => {
    handleInputChange("slug", value);

    // If slug is being changed, validate its uniqueness with debounce
    if (value !== selectedProduct?.slug) {
      // Clear previous timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set loading state immediately
      setSlugValidation({
        isValid: true,
        isChecking: true,
        message: "Checking availability...",
      });

      // Start new timer
      debounceTimerRef.current = setTimeout(() => {
        validateSlugUniqueness(value);
      }, 500);
    } else if (value === selectedProduct?.slug) {
      // Reset validation if slug is back to original
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      setSlugValidation({ isValid: true, isChecking: false, message: "" });
    }
  };

  // Function to validate slug uniqueness
  const validateSlugUniqueness = async (slug: string) => {
    if (!slug.trim()) {
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message: "Slug is required",
      });
      return;
    }

    // Basic slug format validation
    const slugPattern = /^[a-z0-9-]+$/;
    if (!slugPattern.test(slug)) {
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message:
          "Slug can only contain lowercase letters, numbers, and hyphens",
      });
      return;
    }

    try {
      // Check if slug exists by trying to fetch it
      const response = await fetch(
        `/api/products/validate-slug?slug=${encodeURIComponent(
          slug
        )}&currentId=${selectedProduct?.id}`
      );
      const data = await response.json();

      if (data.exists) {
        setSlugValidation({
          isValid: false,
          isChecking: false,
          message: "This slug is already taken. Please choose a different one.",
        });
      } else {
        setSlugValidation({
          isValid: true,
          isChecking: false,
          message: "Slug is available",
        });
      }
    } catch (error) {
      console.error("Error validating slug:", error);
      setSlugValidation({
        isValid: false,
        isChecking: false,
        message: "Error checking slug availability",
      });
    }
  };

  const handleSave = async () => {
    if (!selectedProduct) return;

    // Validate form before saving using shared validation
    const validationErrors = validateFormSchema(createProductSchema, formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors as FormErrors);
      toast.error("Please fix the validation errors before saving");
      return;
    }

    // Check slug validation
    if (!slugValidation.isValid && formData.slug !== selectedProduct.slug) {
      toast.error("Please fix the slug error before saving");
      return;
    }

    setErrors({});

    setIsLoading(true);
    try {
      // Determine what has changed
      const updates: Parameters<typeof updateProductSettings>[1] = {};

      // Check for basic field changes
      if (formData.brand !== selectedProduct.brand) {
        updates.brand = formData.brand;
      }

      if (formData.stage !== selectedProduct.stage) {
        updates.stage = formData.stage as ProductStage;
      }

      if (formData.name !== selectedProduct.name) {
        updates.name = formData.name;
      }

      if (formData.slug !== selectedProduct.slug) {
        updates.slug = formData.slug;
      }

      if (formData.description !== (selectedProduct.description || "")) {
        updates.description = formData.description;
      }

      if (formData.imageUrl !== (selectedProduct.imageUrl || "")) {
        updates.imageUrl = formData.imageUrl;
      }

      // Handle subscription changes
      const originalSubscribedIds =
        selectedProduct.subscriptions?.map((sub) => sub.userId) || [];
      const currentSubscribedIds = formData.subscribedUserIds || [];

      // Find users to add (in current but not in original)
      const usersToAdd = currentSubscribedIds.filter(
        (id) => !originalSubscribedIds.includes(id)
      );
      if (usersToAdd.length > 0) {
        updates.usersToAdd = usersToAdd;
      }

      // Find users to remove (in original but not in current)
      const usersToRemove = originalSubscribedIds.filter(
        (id) => !currentSubscribedIds.includes(id)
      );
      if (usersToRemove.length > 0) {
        updates.usersToRemove = usersToRemove;
      }

      // Only call the update if there are actual changes
      if (Object.keys(updates).length > 0) {
        await updateProductSettings(selectedProduct.id, updates);
      }

      setHasUnsavedChanges(false);
      setIsEditMode(false); // Exit edit mode after successful save
      toast.success("Product settings saved successfully", {
        description: "All changes have been applied to the product",
      });
    } catch (error) {
      console.error("Failed to save product settings:", error);

      // Check if it's a slug uniqueness error
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (
        errorMessage.includes("slug") &&
        errorMessage.includes("already exists")
      ) {
        toast.error("Slug already exists", {
          description: errorMessage,
        });
        // Also update the slug validation state to show the error
        setSlugValidation({
          isValid: false,
          isChecking: false,
          message: "This slug is already taken. Please choose a different one.",
        });
      } else {
        toast.error("Failed to save settings", {
          description:
            "Please try again or contact support if the issue persists",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to leave this page?"
      );
      if (!confirmed) return;
    }
    router.back();
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to discard them?"
      );
      if (!confirmed) return;

      // Reset form data to original product values
      if (selectedProduct) {
        const originalSubscribedIds =
          selectedProduct.subscriptions?.map((sub) => sub.userId) || [];
        setFormData({
          name: selectedProduct.name,
          brand: selectedProduct.brand,
          stage: selectedProduct.stage,
          description: selectedProduct.description || "",
          imageUrl: selectedProduct.imageUrl || "",
          slug: selectedProduct.slug,
          subscribedUserIds: originalSubscribedIds,
        });

        // Reset slug validation
        setSlugValidation({ isValid: true, isChecking: false, message: "" });
      }
      setHasUnsavedChanges(false);
    }

    // Exit edit mode
    setIsEditMode(false);
  };

  // Toggle edit mode
  const handleToggleEditMode = () => {
    if (isEditMode && hasUnsavedChanges) {
      const confirmed = window.confirm(
        "You have unsaved changes. Are you sure you want to exit edit mode?"
      );
      if (!confirmed) return;

      // Reset to original values if cancelling
      handleCancel();
    } else {
      setIsEditMode(!isEditMode);
    }
  };

  const handleDelete = async () => {
    if (!selectedProduct?.id) {
      toast.error("Product not found");
      return;
    }

    setIsLoading(true);

    try {
      const result = await deleteNPD(selectedProduct.id);

      if (result.success) {
        toast.success(result.message);
        setShowDeleteDialog(false);

        // Clear unsaved changes state since product is being deleted
        setHasUnsavedChanges(false);
        setGlobalUnsavedChanges(false);

        // Navigate back to NPD list after successful deletion
        router.push("/dashboard/npd");
      }
    } catch (error) {
      console.error("Error deleting NPD:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to delete NPD. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRequest = async () => {
    if (!selectedProduct?.id) {
      toast.error("Product not found");
      return;
    }

    setIsLoading(true);

    try {
      const result = await requestProductDeletion(
        selectedProduct.id,
        deleteRequestReason
      );

      if (result.success) {
        toast.success(result.message);
        setShowDeleteRequestDialog(false);
        setDeleteRequestReason("");
      }
    } catch (error) {
      console.error("Error requesting product deletion:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to send deletion request. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can delete NPD
  const canDeleteNPD = () => {
    if (!selectedProduct || !user) return false;

    // Owner, admin, or super admin can delete
    return selectedProduct.userId === user.userId || isAdmin || isSuperAdmin;
  };

  // Loading state - include local loading to prevent showing errors too quickly
  if (isLoadingSelectedProduct || localLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <div className="mb-2">
            <Spinner loading={true} size={12} />
          </div>
          <p className="text-muted-foreground">Loading product settings...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (selectedProductError) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <PackageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Product not found</h1>
          <p className="text-muted-foreground mb-4">{selectedProductError}</p>
          <Button onClick={() => router.push("/dashboard/products")}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  // No product selected
  if (!selectedProduct) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <SettingsIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">No product selected</h1>
          <p className="text-muted-foreground mb-4">
            Please select a product to view its settings
          </p>
          <Button onClick={() => router.push("/dashboard/products")}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col">
      <PageHeader
        title="Product Settings"
        description={`Manage settings for ${selectedProduct.name}`}
        onBack={handleBack}
        actionButton={
          <div className="flex gap-2">
            {!isEditMode ? (
              <Button onClick={handleToggleEditMode} disabled={isLoading}>
                <EditIcon className="mr-2 h-4 w-4" />
                Edit Product
              </Button>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={
                    isLoading ||
                    (!slugValidation.isValid &&
                      formData.slug !== selectedProduct?.slug)
                  }
                >
                  {isLoading ? (
                    <Spinner loading={true} size={8} />
                  ) : (
                    <SaveIcon className="mr-2 h-4 w-4" />
                  )}
                  Save Changes
                </Button>
              </>
            )}
          </div>
        }
      >
        {hasUnsavedChanges && (
          <div className="flex items-center gap-2 mt-1">
            <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse" />
            <span className="text-xs text-amber-600 dark:text-amber-400">
              Unsaved changes
            </span>
          </div>
        )}
      </PageHeader>

      {/* Settings Content */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <div className="p-6 space-y-6">
          {/* Basic Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Information
              </CardTitle>
              <CardDescription>
                Fill in the basic details for your NPD product. Required fields
                are marked with an asterisk (*).
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextField
                    label="Product Name"
                    value={formData.name}
                    onChange={(value) => handleInputChange("name", value)}
                    error={errors.name}
                    required
                    placeholder="Enter product name"
                    className={
                      !isEditMode ? "opacity-60 pointer-events-none" : ""
                    }
                  />

                  <SelectField
                    label="Brand"
                    value={formData.brand}
                    onChange={(value) => handleInputChange("brand", value)}
                    error={errors.brand}
                    required
                    placeholder="Select a brand"
                    options={brandOptions}
                    className={
                      !isEditMode ? "opacity-60 pointer-events-none" : ""
                    }
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <StageSelectField
                    label="Development Stage"
                    value={formData.stage}
                    onChange={(value) => handleInputChange("stage", value)}
                    error={errors.stage}
                    required
                    placeholder="Select development stage"
                    options={stageOptions}
                    className={
                      !isEditMode ? "opacity-60 pointer-events-none" : ""
                    }
                  />

                  <TextField
                    label="Product Image"
                    value={formData.imageUrl}
                    onChange={(value) => handleInputChange("imageUrl", value)}
                    error={errors.imageUrl}
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    className={
                      !isEditMode ? "opacity-60 pointer-events-none" : ""
                    }
                  />
                </div>

                <TextField
                  label="Description"
                  value={formData.description}
                  onChange={(value) => handleInputChange("description", value)}
                  error={errors.description}
                  placeholder="One line description of the product"
                  className={
                    !isEditMode ? "opacity-60 pointer-events-none" : ""
                  }
                />

                {/* Slug field - only show in edit mode for settings */}
                {isEditMode && (
                  <TextField
                    label="Product Slug"
                    value={formData.slug}
                    onChange={handleSlugChange}
                    error={
                      !slugValidation.isValid
                        ? slugValidation.message
                        : undefined
                    }
                    required
                    placeholder="product-slug"
                    className="opacity-100"
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* User Subscriptions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Subscribe Users
              </CardTitle>
              <CardDescription>
                Manage which users are subscribed to this NPD product.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSelectionField
                label="Subscribe Users"
                selectedUserIds={formData.subscribedUserIds || []}
                onChange={(userIds: string[]) =>
                  handleInputChange("subscribedUserIds", userIds)
                }
                users={users}
                isLoading={isLoadingUsers}
                error={errors.subscribedUserIds}
                className={!isEditMode ? "opacity-60 pointer-events-none" : ""}
              />
            </CardContent>
          </Card>

          {/* Danger Zone Card */}
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <TrashIcon className="h-5 w-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  {canDeleteNPD()
                    ? "You have permission to archive this NPD. Archived NPDs can be restored by administrators."
                    : "You can request NPD deletion. Administrators and the NPD owner will be notified."}
                </div>

                {canDeleteNPD() ? (
                  <AlertDialog
                    open={showDeleteDialog}
                    onOpenChange={setShowDeleteDialog}
                  >
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={isLoading}
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        Archive Product
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Archive Product?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will archive the product &quot;
                          {selectedProduct.name}&quot;. The product will be
                          hidden from users but can be restored by
                          administrators.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="space-y-3">
                        <div className="text-sm text-muted-foreground">
                          What happens when you archive:
                        </div>
                        <ul className="list-disc list-inside text-sm space-y-1 ml-4 text-muted-foreground">
                          <li>Product becomes invisible to all users</li>
                          <li>All data is preserved (tabs, activity, chat)</li>
                          <li>Product can be restored by administrators</li>
                          <li>URL slug is reserved to prevent conflicts</li>
                        </ul>
                        <div className="text-sm font-medium text-green-600 dark:text-green-400">
                          No data will be permanently lost.
                        </div>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel disabled={isLoading}>
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          disabled={isLoading}
                          className="bg-destructive text-white hover:bg-destructive/90"
                        >
                          {isLoading ? (
                            <>
                              <Spinner loading={true} size={8} />
                              <span className="ml-2">Archiving...</span>
                            </>
                          ) : (
                            "Archive Product"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                ) : (
                  <AlertDialog
                    open={showDeleteRequestDialog}
                    onOpenChange={setShowDeleteRequestDialog}
                  >
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={isLoading}
                      >
                        <TrashIcon className="mr-2 h-4 w-4" />
                        Request Deletion
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Request Product Deletion
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          Send a deletion request for &quot;
                          {selectedProduct.name}&quot; to administrators and the
                          product owner.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="deleteReason">
                            Reason (optional)
                          </Label>
                          <Textarea
                            id="deleteReason"
                            value={deleteRequestReason}
                            onChange={(e) =>
                              setDeleteRequestReason(e.target.value)
                            }
                            placeholder="Why should this product be deleted?"
                            rows={3}
                          />
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Notifications will be sent to:
                        </div>
                        <ul className="list-disc list-inside text-sm space-y-1 ml-4 text-muted-foreground">
                          <li>All administrators</li>
                          <li>The product owner (if different from you)</li>
                        </ul>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel disabled={isLoading}>
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteRequest}
                          disabled={isLoading}
                          className="bg-destructive text-white hover:bg-destructive/90"
                        >
                          {isLoading ? (
                            <>
                              <Spinner loading={true} size={8} />
                              <span className="ml-2">Sending...</span>
                            </>
                          ) : (
                            "Send Request"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
