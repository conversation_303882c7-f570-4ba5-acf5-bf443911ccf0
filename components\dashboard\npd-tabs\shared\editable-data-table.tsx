import React from "react";
import { TABLE_STYLES } from "./tabs-table-styles";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { LabelWithChangeIndicator } from "@/components/ui/change-indicators";
import { Plus, X, HelpCircle, Lock } from "lucide-react";

interface EditableDataRow {
  id: string;
  label: string;
  value: string | null | undefined;
  placeholder?: string;
  type?: "input" | "textarea" | "multi-input" | "select";
  rows?: number;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
  isChanged?: boolean;
  isEditing?: boolean;
  helpText?: string;
  isPermissionRestricted?: boolean; // Whether field is disabled due to permission restrictions
  // For multi-input type
  multiValues?: string[];
  onMultiChange?: (values: string[]) => void;
  onAddRow?: () => void;
  onRemoveRow?: (index: number) => void;
  // For select type
  options?: { value: string; label: string }[];
}

interface EditableDataTableProps {
  data: EditableDataRow[];
  className?: string;
}

export function EditableDataTable({
  data,
  className = "",
}: EditableDataTableProps) {
  return (
    <div className={`${TABLE_STYLES.container} ${className}`}>
      <div className={TABLE_STYLES.scrollContainer}>
        <table className={TABLE_STYLES.table}>
          <tbody className={TABLE_STYLES.bodyContainer}>
            {data.map((row, index) => (
              <tr
                key={row.id}
                className={`${TABLE_STYLES.bodyRow.base} ${
                  index % 2 === 0
                    ? TABLE_STYLES.bodyRow.even
                    : TABLE_STYLES.bodyRow.odd
                } ${index === 0 ? TABLE_STYLES.bodyRow.first : ""} ${
                  index === data.length - 1 ? TABLE_STYLES.bodyRow.last : ""
                }`}
              >
                <td
                  className={`${TABLE_STYLES.bodyCell} font-bold px-3 py-2 align-middle`}
                  style={{ width: "200px", minWidth: "200px" }}
                >
                  <div className="flex items-center gap-1">
                    <Label htmlFor={row.id}>
                      <LabelWithChangeIndicator
                        isChanged={row.isChanged || false}
                        isEditing={row.isEditing || false}
                      >
                        {row.label}
                      </LabelWithChangeIndicator>
                    </Label>
                    {row.helpText && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-xs">
                            <p className="text-sm">{row.helpText}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    {row.isPermissionRestricted && row.disabled && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Lock className="h-3 w-3 text-amber-600 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-xs">
                            <p className="text-sm">
                              You don&apos;t have permission to edit this field.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </td>
                <td className={`${TABLE_STYLES.bodyCell} px-3 py-2`}>
                  {row.type === "multi-input" ? (
                    <div className="space-y-2">
                      {(row.multiValues || [""]).map((value, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <Input
                            value={value}
                            onChange={(e) => {
                              const newValues = [...(row.multiValues || [""])];
                              newValues[idx] = e.target.value;
                              row.onMultiChange?.(newValues);
                            }}
                            placeholder={`${row.placeholder} ${idx + 1}`}
                            disabled={row.disabled && row.isEditing}
                            readOnly={!row.isEditing}
                            className={`flex-1 ${
                              row.isEditing
                                ? row.isChanged
                                  ? `bg-background border-orange-400 focus-visible:border-orange-500 focus-visible:ring-2 focus-visible:ring-orange-200 focus-visible:ring-offset-2 ${
                                      row.className || ""
                                    }` // Modified: explicit orange styling
                                  : "border-input bg-background hover:border-ring focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2" // Unmodified: explicit default styling
                                : "!bg-transparent !border-transparent !border-0 text-foreground cursor-default focus:ring-0 focus:border-transparent focus-visible:ring-0 !shadow-none"
                            } ${
                              row.disabled && row.isEditing
                                ? "cursor-not-allowed [&:disabled]:text-foreground [&:disabled]:opacity-100"
                                : ""
                            }`}
                          />
                          {row.isEditing &&
                            (row.multiValues || []).length > 1 && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => row.onRemoveRow?.(idx)}
                                className="px-2"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                        </div>
                      ))}
                      {row.isEditing && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={row.onAddRow}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Feature
                        </Button>
                      )}
                    </div>
                  ) : row.type === "textarea" ? (
                    <Textarea
                      id={row.id}
                      value={row.value || ""}
                      onChange={(e) => row.onChange(e.target.value)}
                      placeholder={row.placeholder}
                      disabled={row.disabled && row.isEditing}
                      readOnly={!row.isEditing}
                      rows={row.rows || 3}
                      className={`w-full resize-y ${
                        row.isEditing
                          ? row.isChanged
                            ? `bg-background border-orange-400 focus-visible:border-orange-500 focus-visible:ring-2 focus-visible:ring-orange-200 focus-visible:ring-offset-2 ${
                                row.className || ""
                              }` // Modified: explicit orange styling
                            : "border-input bg-background hover:border-ring focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2" // Unmodified: explicit default styling
                          : "!bg-transparent !border-transparent !border-0 text-foreground cursor-default focus:ring-0 focus:border-transparent focus-visible:ring-0 !shadow-none"
                      } ${
                        row.disabled && row.isEditing
                          ? "cursor-not-allowed [&:disabled]:text-foreground [&:disabled]:opacity-100"
                          : ""
                      }`}
                    />
                  ) : row.type === "select" ? (
                    row.isEditing ? (
                      <Select
                        value={row.value || ""}
                        onValueChange={row.onChange}
                        disabled={row.disabled}
                      >
                        <SelectTrigger
                          className={`w-full ${
                            row.isChanged
                              ? `bg-background border-orange-400 focus-visible:border-orange-500 focus-visible:ring-2 focus-visible:ring-orange-200 focus-visible:ring-offset-2 ${
                                  row.className || ""
                                }` // Modified: explicit orange styling
                              : "border-input bg-background hover:border-ring focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2" // Unmodified: explicit default styling
                          } ${
                            row.disabled
                              ? "cursor-not-allowed [&:disabled]:text-foreground [&:disabled]:opacity-100"
                              : ""
                          }`}
                        >
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {row.options?.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="w-full py-2 px-3 text-muted-foreground">
                        {row.value || "Not selected"}
                      </div>
                    )
                  ) : (
                    <Input
                      id={row.id}
                      value={row.value || ""}
                      onChange={(e) => row.onChange(e.target.value)}
                      placeholder={row.placeholder}
                      disabled={row.disabled && row.isEditing}
                      readOnly={!row.isEditing}
                      className={`w-full ${
                        row.isEditing
                          ? row.isChanged
                            ? `bg-background border-orange-400 focus-visible:border-orange-500 focus-visible:ring-2 focus-visible:ring-orange-200 focus-visible:ring-offset-2 ${
                                row.className || ""
                              }` // Modified: explicit orange styling
                            : "border-input bg-background hover:border-ring focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2" // Unmodified: explicit default styling
                          : "!bg-transparent !border-transparent !border-0 text-foreground cursor-default focus:ring-0 focus:border-transparent focus-visible:ring-0 !shadow-none"
                      } ${
                        row.disabled && row.isEditing
                          ? "cursor-not-allowed [&:disabled]:text-foreground [&:disabled]:opacity-100"
                          : ""
                      }`}
                    />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
