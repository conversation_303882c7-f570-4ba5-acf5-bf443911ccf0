/**
 * Hook for user autocomplete functionality
 */

import { useState, useEffect, useCallback } from "react";

export interface User {
  id: string;
  name: string;
  email: string;
}

export function useUserAutocomplete() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch("/api/users/autocomplete");
      
      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }
      
      const data = await response.json();
      setUsers(data.users || []);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch users");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const searchUsers = useCallback((query: string): User[] => {
    if (!query.trim()) return users;
    
    const lowercaseQuery = query.toLowerCase();
    return users.filter(user => 
      user.name.toLowerCase().includes(lowercaseQuery) ||
      user.email.toLowerCase().includes(lowercaseQuery)
    );
  }, [users]);

  const getUserByName = useCallback((name: string): User | undefined => {
    return users.find(user => 
      user.name.toLowerCase() === name.toLowerCase()
    );
  }, [users]);

  const getUsersByNames = useCallback((names: string[]): User[] => {
    return names.map(name => getUserByName(name)).filter(Boolean) as User[];
  }, [getUserByName]);

  return {
    users,
    loading,
    error,
    searchUsers,
    getUserByName,
    getUsersByNames,
    refetch: fetchUsers,
  };
}
