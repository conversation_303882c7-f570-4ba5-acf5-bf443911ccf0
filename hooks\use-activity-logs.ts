"use client";

import { useEffect, useState, useCallback } from "react";
import { getActivityLogs } from "@/actions/activity-log.actions";

interface ActivityLog {
  id: string;
  action: string;
  description: string;
  user?: {
    name: string;
    email: string;
    image?: string;
  };
  changes?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  createdAt: string;
}

export function useActivityLogs(tabId: string) {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLogs = useCallback(async () => {
    if (!tabId || tabId === "no-tabs") {
      setLogs([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const result = await getActivityLogs(tabId);

      if (result.success && result.data) {
        setLogs(
          result.data.map((log) => ({
            id: log.id,
            action: log.action,
            description: log.description,
            user: log.user
              ? {
                  name: log.user.name,
                  email: log.user.email,
                  image: log.user.image || undefined,
                }
              : undefined,
            changes: log.changes as Record<string, unknown>,
            metadata: log.metadata as Record<string, unknown>,
            createdAt: log.createdAt.toISOString(),
          }))
        );
      } else {
        setError(result.error || "Failed to fetch activity logs");
        setLogs([]);
      }
    } catch {
      setError("Failed to fetch activity logs");
      setLogs([]);
    } finally {
      setIsLoading(false);
    }
  }, [tabId]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  return { logs, isLoading, error, refetch: fetchLogs };
}
