"use client";

import { useRoleUpdates } from "@/hooks/use-role-updates";
import { useAuth } from "@/context/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function RoleUpdatesProvider() {
  const { user } = useAuth();
  const router = useRouter();

  // Hook that listens for role updates
  useRoleUpdates();

  // Redirect to pending approval if user loses all roles
  useEffect(() => {
    if (typeof window === "undefined") return;

    if (user && (!user.roles || user.roles.length === 0)) {
      // Check if currently on dashboard page
      if (window.location.pathname.startsWith("/dashboard")) {
        router.push("/pending-approval");
      }
    }
  }, [user, router]);

  return null; // This component doesn't render anything
}
