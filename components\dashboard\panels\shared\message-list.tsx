"use client";

import React, { useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { MessageItem } from "./message-item";
import { ArrowUp, MessageCircle } from "lucide-react";

interface User {
  id: string;
  name: string;
  email?: string;
}

interface Message {
  id: string;
  content?: string; // For activity messages
  message?: string; // For chat messages
  createdAt: string;
  updatedAt?: string;
  user: User;
  isRead: boolean;
}

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  hasMore: boolean;
  currentUserId?: string;
  editingMessageId: string | null;
  editText: string;
  onLoadMore: () => void;
  onEditStart: (messageId: string, content: string) => void;
  onEditSave: () => void;
  onEditCancel: () => void;
  onEditTextChange: (text: string) => void;
  onDelete: (messageId: string) => void;
  onToggleRead: (messageId: string, isCurrentlyRead?: boolean) => void;
  formatTime: (dateString: string) => string;
  onMentionClick?: (username: string) => void;
  users?: User[];
}

export function MessageList({
  messages,
  isLoading,
  hasMore,
  currentUserId,
  editingMessageId,
  editText,
  onLoadMore,
  onEditStart,
  onEditSave,
  onEditCancel,
  onEditTextChange,
  onDelete,
  onToggleRead,
  formatTime,
  onMentionClick,
  users,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages.length, scrollToBottom]);

  if (isLoading && messages.length === 0) {
    return (
      <div className="flex-1 min-h-0 overflow-y-auto p-4">
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-16 w-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 min-h-0 flex flex-col items-center justify-center p-4">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No messages yet</h3>
          <p className="text-sm text-muted-foreground">
            Start the conversation by sending the first message.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 min-h-0 overflow-y-auto">
      <div className="p-4 space-y-2">
        {/* Load More Button */}
        {hasMore && (
          <div className="text-center pb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onLoadMore}
              disabled={isLoading}
              className="text-xs gap-1.5"
            >
              <ArrowUp className="h-3 w-3" />
              Load older messages
            </Button>
          </div>
        )}

        {/* Messages */}
        {messages.map((message) => (
          <MessageItem
            key={message.id}
            message={message}
            currentUserId={currentUserId}
            isEditing={editingMessageId === message.id}
            editText={editText}
            onEditStart={onEditStart}
            onEditSave={onEditSave}
            onEditCancel={onEditCancel}
            onEditTextChange={onEditTextChange}
            onDelete={onDelete}
            onToggleRead={onToggleRead}
            formatTime={formatTime}
            onMentionClick={onMentionClick}
            users={users}
          />
        ))}

        {/* Loading indicator for new messages */}
        {isLoading && messages.length > 0 && (
          <div className="flex justify-center py-2">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        )}
      </div>

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
}
