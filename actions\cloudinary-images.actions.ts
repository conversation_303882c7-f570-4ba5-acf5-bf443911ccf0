"use server";

import { v2 as cloudinary } from "cloudinary";
import { getCurrentUser } from "@/lib/auth";

// Configure Cloudinary using credentials from .env
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

type ImageUploadResult = {
  success: boolean;
  message: string;
  uploadedUrls?: string[];
  error?: string;
};

// Function to upload images to Cloudinary
async function uploadImagesToCloudinary(
  images: File[],
  folderPath: string
): Promise<string[]> {
  const uploadedUrls: string[] = [];

  for (const image of images) {
    const formData = new FormData();
    formData.append("file", image);
    formData.append(
      "upload_preset",
      process.env.CLOUDINARY_UPLOAD_PRESET || ""
    );
    formData.append("folder", folderPath);

    try {
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${process.env.CLOUDINARY_CLOUD_NAME}/image/upload`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (response.ok) {
        const data = await response.json();
        uploadedUrls.push(data.secure_url);
      } else {
        console.error(
          `Error uploading image ${image.name}:`,
          response.statusText
        );
      }
    } catch (error) {
      console.error(`Error uploading image ${image.name}:`, error);
    }
  }

  return uploadedUrls;
}

// Function to move deleted images to "removed" folder
async function moveImagesToRemoved(
  imageUrls: string[],
  originalFolderPath: string
): Promise<void> {
  for (const imageUrl of imageUrls) {
    try {
      // Extract public ID from URL
      const urlParts = imageUrl.split("/");
      const publicIdWithExtension = urlParts.slice(-1)[0];
      const publicId = publicIdWithExtension.split(".")[0];
      const currentPath = `${originalFolderPath}/${publicId}`;
      const newPath = `${originalFolderPath}/removed/${publicId}`;

      await cloudinary.uploader.rename(currentPath, newPath);
    } catch (error) {
      console.error(`Error moving image ${imageUrl} to removed folder:`, error);
    }
  }
}

// Generic function to handle image updates for any tab type
export async function updateTabImages(
  productSlug: string,
  tabType: string, // e.g., "sourcing-brief", "overview"
  newImages: File[],
  deletedImages: string[]
): Promise<ImageUploadResult> {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return {
        success: false,
        message: "User is not authenticated.",
      };
    }

    const folderPath = `products/${productSlug}/${tabType}`;

    // Upload new images
    const uploadedUrls = await uploadImagesToCloudinary(newImages, folderPath);

    // Move deleted images to removed folder
    if (deletedImages.length > 0) {
      await moveImagesToRemoved(deletedImages, folderPath);
    }

    return {
      success: true,
      message: "Images updated successfully!",
      uploadedUrls,
    };
  } catch (error) {
    console.error("Error updating images:", error);
    return {
      success: false,
      message: "Failed to update images. Please try again.",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Specific function for sourcing brief (backward compatibility)
export async function updateSourcingBriefImages(
  productSlug: string,
  newImages: File[],
  deletedImages: string[]
): Promise<ImageUploadResult> {
  return updateTabImages(
    productSlug,
    "sourcing-brief",
    newImages,
    deletedImages
  );
}

// New function for overview images
export async function updateOverviewImages(
  productSlug: string,
  newImages: File[],
  deletedImages: string[]
): Promise<ImageUploadResult> {
  return updateTabImages(productSlug, "overview", newImages, deletedImages);
}
