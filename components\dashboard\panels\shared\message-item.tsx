"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, Edit2, Trash2, Eye, EyeOff } from "lucide-react";
import { MessageContent } from "./message-content";

interface User {
  id: string;
  name: string;
  email?: string;
  imageUrl?: string;
}

interface Message {
  id: string;
  content?: string; // For activity messages
  message?: string; // For chat messages
  createdAt: string;
  updatedAt?: string;
  user: User;
  isRead: boolean;
}

interface User {
  id: string;
  name: string;
  email?: string;
}

interface MessageItemProps {
  message: Message;
  currentUserId?: string;
  isEditing: boolean;
  editText: string;
  onEditStart: (messageId: string, content: string) => void;
  onEditSave: () => void;
  onEditCancel: () => void;
  onEditTextChange: (text: string) => void;
  onDelete: (messageId: string) => void;
  onToggleRead: (messageId: string, isCurrentlyRead?: boolean) => void;
  formatTime: (dateString: string) => string;
  onMentionClick?: (username: string) => void;
  users?: User[];
}

export function MessageItem({
  message,
  currentUserId,
  isEditing,
  editText,
  onEditStart,
  onEditSave,
  onEditCancel,
  onEditTextChange,
  onDelete,
  onToggleRead,
  formatTime,
  onMentionClick,
  users,
}: MessageItemProps) {
  const isOwnMessage = currentUserId === message.user.id;

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      onEditSave();
    } else if (e.key === "Escape") {
      onEditCancel();
    }
  };

  return (
    <div
      id={`message-${message.id}`}
      className={`group p-3 transition-colors ${
        isOwnMessage ? "flex justify-end" : ""
      }`}
    >
      <div
        className={`flex gap-3 max-w-[80%] rounded-lg transition-colors ${
          isOwnMessage ? "flex-row-reverse" : ""
        } ${
          message.isRead
            ? "bg-background hover:bg-muted/30"
            : "bg-blue-50/50 dark:bg-blue-950/20 hover:bg-blue-100/50 dark:hover:bg-blue-950/30"
        } ${
          isOwnMessage
            ? "bg-primary/10 border border-primary/20 hover:bg-primary/15"
            : ""
        }`}
        style={{ padding: "12px" }}
      >
        <Avatar className="h-8 w-8 flex-shrink-0">
          <AvatarImage src={message.user.imageUrl} />
          <AvatarFallback className="text-xs">
            {message.user.name
              .split(" ")
              .map((n) => n[0])
              .join("")
              .toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span
              className={`text-sm font-medium ${
                isOwnMessage ? "text-primary" : "text-foreground"
              }`}
            >
              {message.user.name}
            </span>
            <span
              className={`text-xs ${
                isOwnMessage ? "text-primary/70" : "text-muted-foreground"
              }`}
            >
              {formatTime(message.createdAt)}
            </span>
            {message.updatedAt && message.updatedAt !== message.createdAt && (
              <span
                className={`text-xs ${
                  isOwnMessage ? "text-primary/70" : "text-muted-foreground"
                }`}
              >
                (edited)
              </span>
            )}
            {!message.isRead && (
              <div
                className={`w-2 h-2 rounded-full flex-shrink-0 ${
                  isOwnMessage ? "bg-primary/50" : "bg-primary"
                }`}
              />
            )}
          </div>

          {isEditing ? (
            <div className="space-y-2">
              <Input
                value={editText}
                onChange={(e) => onEditTextChange(e.target.value)}
                onKeyDown={handleKeyPress}
                className="text-sm"
                autoFocus
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={onEditSave}>
                  Save
                </Button>
                <Button size="sm" variant="outline" onClick={onEditCancel}>
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <MessageContent
              content={message.content || message.message || ""}
              onMentionClick={onMentionClick}
              users={users}
            />
          )}
        </div>

        <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => onToggleRead(message.id, message.isRead)}
              >
                {message.isRead ? (
                  <>
                    <EyeOff className="mr-2 h-4 w-4" />
                    Mark as unread
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Mark as read
                  </>
                )}
              </DropdownMenuItem>
              {isOwnMessage && (
                <>
                  <DropdownMenuItem
                    onClick={() =>
                      onEditStart(
                        message.id,
                        message.content || message.message || ""
                      )
                    }
                  >
                    <Edit2 className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onDelete(message.id)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
