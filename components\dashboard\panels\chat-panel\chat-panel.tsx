"use client";

import React, { useState, useRef, useEffect } from "react";
import { useChatData } from "@/hooks/use-chat-data";
import { useChatActions } from "@/hooks/use-chat-actions";
import { useAuth } from "@/hooks/use-auth";
import { useChatMentions } from "@/hooks/use-chat-mentions";
import { Button } from "@/components/ui/button";
import { MessageCircle, CheckCheck } from "lucide-react";
import { MessageList } from "../shared/message-list";
import { MessageInput } from "../shared/message-input";
import { formatTime } from "../shared/utils";
import { useSearchParams } from "next/navigation";

interface ProductChatPanelProps {
  productId: string;
  productName: string;
  isActive?: boolean;
  pendingMention?: string | null;
  onMentionHandled?: () => void;
}

export function ProductChatPanel({
  productId,
  productName,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isActive = true,
  pendingMention,
  onMentionHandled,
}: ProductChatPanelProps) {
  const { user: currentUser } = useAuth();
  const { users } = useChatMentions();
  const searchParams = useSearchParams();

  // Data hook for fetching messages and unread counts
  const {
    messages,
    isLoading,
    error: dataError,
    hasMore,
    loadMore,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    unreadCount,
    addMessageToState,
    updateMessageInState,
    removeMessageFromState,
  } = useChatData(productId);

  // Actions hook for mutations
  const {
    sendMessage,
    editMessage,
    deleteMessage,
    toggleReadStatus,
    markAllAsRead,
    isPending,
    error: actionError,
  } = useChatActions(productId);

  // Combined error state
  const error = dataError || actionError;

  const [newMessage, setNewMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editText, setEditText] = useState("");

  const inputRef = useRef<{
    focus: () => void;
    blur: () => void;
    setSelectionRange: (start: number) => void;
    selectionStart: number;
    selectionEnd: number;
    value: string;
  }>(null);

  // Handle pending mention from side panel
  React.useEffect(() => {
    if (pendingMention) {
      setNewMessage(`@${pendingMention} `);

      // Focus the input after a short delay
      setTimeout(() => {
        const focusInput = () => {
          if (inputRef.current) {
            inputRef.current.focus();
            const length = inputRef.current.value.length;
            inputRef.current.setSelectionRange(length);
            return true;
          }
          return false;
        };

        // Try immediately
        if (!focusInput()) {
          // If that fails, try again after another delay
          setTimeout(focusInput, 100);
        }
      }, 100);

      // Clear the pending mention
      onMentionHandled?.();
    }
  }, [pendingMention, onMentionHandled]);

  // Handle scrolling to specific message from notification
  useEffect(() => {
    const messageId = searchParams.get("messageId");
    if (messageId && messages.length > 0) {
      // Check if we've already highlighted this message in this session
      const highlightKey = `highlighted-${messageId}`;
      const alreadyHighlighted = sessionStorage.getItem(highlightKey);

      if (!alreadyHighlighted) {
        // Mark as highlighted for this session
        sessionStorage.setItem(highlightKey, "true");

        // Wait for messages to render, then scroll to the specific message
        setTimeout(() => {
          const messageElement = document.getElementById(
            `message-${messageId}`
          );
          if (messageElement) {
            messageElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });

            // Add a temporary highlight effect
            messageElement.classList.add(
              "bg-yellow-100",
              "dark:bg-yellow-900/30",
              "transition-colors",
              "duration-300"
            );

            // Remove highlight after 3 seconds
            setTimeout(() => {
              messageElement.classList.remove(
                "bg-yellow-100",
                "dark:bg-yellow-900/30"
              );
            }, 3000);

            // Clean up URL parameter after highlighting
            const url = new URL(window.location.href);
            url.searchParams.delete("messageId");
            window.history.replaceState({}, "", url.pathname + url.search);
          }
        }, 500);
      } else {
        // Just scroll without highlighting if already highlighted
        setTimeout(() => {
          const messageElement = document.getElementById(
            `message-${messageId}`
          );
          if (messageElement) {
            messageElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }, 500);
      }
    }
  }, [searchParams, messages]);

  const handleSendMessage = async (mentions: string[] = []) => {
    if (!newMessage.trim() || isSending || isPending) return;

    setIsSending(true);
    // Ensure mentions is always an array
    const mentionsArray = Array.isArray(mentions) ? mentions : [];
    const result = await sendMessage(newMessage, mentionsArray);

    if (result) {
      // Optimistically add the message to the UI
      addMessageToState(result);
      setNewMessage("");
      inputRef.current?.focus();
    }

    setIsSending(false);
  };

  const handleEditMessage = (messageId: string, currentText: string) => {
    setEditingMessageId(messageId);
    setEditText(currentText);
  };

  const handleSaveEdit = async () => {
    if (!editingMessageId || !editText.trim() || isPending) return;

    const result = await editMessage(editingMessageId, editText);

    if (result) {
      // Optimistically update the message in the UI
      updateMessageInState(result);
      setEditingMessageId(null);
      setEditText("");
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditText("");
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (isPending) return;

    const confirmed = window.confirm(
      "Are you sure you want to delete this message?"
    );
    if (confirmed) {
      const success = await deleteMessage(messageId);
      if (success) {
        // Optimistically remove the message from the UI
        removeMessageFromState(messageId);
      }
    }
  };

  if (error) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-shrink-0 border-b p-4 bg-background">
          <h3 className="font-semibold flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            Chat
          </h3>
        </div>
        <div className="flex-1 min-h-0 flex flex-col items-center justify-center p-4">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Failed to load chat</p>
            <p className="text-xs text-muted-foreground mb-4">{error}</p>
            <p className="text-xs text-gray-500">Product ID: {productId}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b p-4 bg-background">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Chat
            </h3>
            <p className="text-xs text-muted-foreground">{productName}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={markAllAsRead}
            className="h-8 px-2 text-xs gap-1.5 hover:bg-muted/70 hover:text-foreground hover:shadow-sm cursor-pointer"
            title="Mark all as read"
          >
            <CheckCheck className="h-3 w-3" />
            Mark all read
          </Button>
        </div>
      </div>

      {/* Messages List */}
      <MessageList
        messages={messages}
        isLoading={isLoading}
        hasMore={hasMore}
        currentUserId={currentUser?.userId}
        editingMessageId={editingMessageId}
        editText={editText}
        onLoadMore={loadMore}
        onEditStart={handleEditMessage}
        onEditSave={handleSaveEdit}
        onEditCancel={handleCancelEdit}
        onEditTextChange={setEditText}
        onDelete={handleDeleteMessage}
        onToggleRead={(messageId, isCurrentlyRead) =>
          toggleReadStatus(messageId, isCurrentlyRead || false)
        }
        formatTime={formatTime}
        onMentionClick={(username) => {
          setNewMessage((prev) => prev + `@${username} `);
          inputRef.current?.focus();
        }}
        users={users}
      />

      {/* Message Input */}
      <MessageInput
        ref={inputRef}
        value={newMessage}
        onChange={setNewMessage}
        onSend={handleSendMessage}
        isSending={isSending || isPending}
        placeholder="Type a message... Use @ to mention someone"
        users={users}
      />
    </div>
  );
}
