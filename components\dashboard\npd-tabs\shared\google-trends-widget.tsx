"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { TrendingUp, AlertCircle, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface GoogleTrendsDataPoint {
  date: string;
  value: number;
  formattedDate: string;
}

interface GoogleTrendsData {
  keyword: string;
  data: GoogleTrendsDataPoint[];
  fiveYearData?: GoogleTrendsDataPoint[];
  relatedQueries: string[];
  averageInterest: number;
  trend: "rising" | "falling" | "stable";
  peakInterest: number;
  region: string;
  timeframe: string;
  isRealData?: boolean;
  dataSource?: string;
}

interface GoogleTrendsWidgetProps {
  keyword: string;
  className?: string;
  isEditing?: boolean;
  onDataFetched?: (data: GoogleTrendsData) => void;
  onKeywordChange?: (keyword: string) => void;
  storedData?: GoogleTrendsData | null;
}

type TimePeriod =
  | "past_hour"
  | "past_4_hours"
  | "past_day"
  | "past_7_days"
  | "past_30_days"
  | "past_90_days"
  | "past_12_months"
  | "past_5_years";
type Region =
  | "worldwide"
  | "US"
  | "GB"
  | "CA"
  | "AU"
  | "DE"
  | "FR"
  | "JP"
  | "IN";
type Category =
  | "all"
  | "business"
  | "entertainment"
  | "health"
  | "shopping"
  | "sports"
  | "technology";

type SearchType = "web" | "image" | "news" | "youtube" | "froogle";

export function GoogleTrendsWidget({
  keyword,
  className = "",
  isEditing = false,
  onDataFetched,
  onKeywordChange,
  storedData,
}: GoogleTrendsWidgetProps) {
  const [trendsData, setTrendsData] = useState<GoogleTrendsData | null>(
    storedData || null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<Region>("worldwide");

  // Fixed settings to conserve API usage
  const timePeriod: TimePeriod = "past_12_months";
  const category: Category = "all";
  const searchType: SearchType = "web";

  // Generate realistic data based on time period and keyword
  const generateRealisticTrendsData = (
    searchKeyword: string,
    period: TimePeriod,
    selectedRegion: Region
  ): GoogleTrendsData => {
    const now = new Date();
    let dataPoints: GoogleTrendsDataPoint[] = [];

    // Generate different data patterns based on time period
    switch (period) {
      case "past_hour":
        // Generate hourly data for past hour (60 data points)
        for (let i = 59; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 60 * 1000);
          const baseValue = 50 + Math.sin(i / 10) * 20 + Math.random() * 15;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            }),
          });
        }
        break;

      case "past_4_hours":
        // Generate data every 4 minutes for past 4 hours
        for (let i = 59; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 4 * 60 * 1000);
          const baseValue = 45 + Math.sin(i / 8) * 25 + Math.random() * 20;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            }),
          });
        }
        break;

      case "past_day":
        // Generate hourly data for past 24 hours
        for (let i = 23; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 60 * 60 * 1000);
          const baseValue = 40 + Math.sin(i / 3) * 30 + Math.random() * 20;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleTimeString("en-US", {
              hour: "2-digit",
            }),
          });
        }
        break;

      case "past_7_days":
        // Generate daily data for past 7 days
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const baseValue = 55 + Math.sin(i / 2) * 25 + Math.random() * 15;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleDateString("en-US", {
              weekday: "short",
            }),
          });
        }
        break;

      case "past_30_days":
        // Generate daily data for past 30 days
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const baseValue = 50 + Math.sin(i / 5) * 30 + Math.random() * 20;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            }),
          });
        }
        break;

      case "past_90_days":
        // Generate weekly data for past 90 days
        for (let i = 12; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000);
          const baseValue = 45 + Math.sin(i / 3) * 35 + Math.random() * 15;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            }),
          });
        }
        break;

      case "past_12_months":
        // Generate monthly data for past 12 months (your original pattern)
        const monthlyData = [
          { value: 65, offset: 11 },
          { value: 68, offset: 10 },
          { value: 72, offset: 9 },
          { value: 69, offset: 8 },
          { value: 66, offset: 7 },
          { value: 63, offset: 6 },
          { value: 58, offset: 5 },
          { value: 61, offset: 4 },
          { value: 70, offset: 3 },
          { value: 75, offset: 2 },
          { value: 78, offset: 1 },
          { value: 85, offset: 0 },
        ];

        dataPoints = monthlyData.map((item) => {
          const date = new Date(
            now.getFullYear(),
            now.getMonth() - item.offset,
            1
          );
          return {
            date: date.toISOString(),
            value: item.value + (Math.random() - 0.5) * 5, // Add slight variation
            formattedDate: date.toLocaleDateString("en-US", {
              month: "short",
              year: "numeric",
            }),
          };
        });
        break;

      case "past_5_years":
        // Generate yearly data for past 5 years
        for (let i = 4; i >= 0; i--) {
          const date = new Date(now.getFullYear() - i, now.getMonth(), 1);
          const baseValue = 40 + Math.sin(i) * 40 + Math.random() * 20;
          dataPoints.push({
            date: date.toISOString(),
            value: Math.round(Math.max(0, Math.min(100, baseValue))),
            formattedDate: date.getFullYear().toString(),
          });
        }
        break;

      default:
        // Fallback to 12 months
        const fallbackMonthlyData = [
          { value: 65, offset: 11 },
          { value: 68, offset: 10 },
          { value: 72, offset: 9 },
          { value: 69, offset: 8 },
          { value: 66, offset: 7 },
          { value: 63, offset: 6 },
          { value: 58, offset: 5 },
          { value: 61, offset: 4 },
          { value: 70, offset: 3 },
          { value: 75, offset: 2 },
          { value: 78, offset: 1 },
          { value: 85, offset: 0 },
        ];
        dataPoints = fallbackMonthlyData.map((item) => {
          const date = new Date(
            now.getFullYear(),
            now.getMonth() - item.offset,
            1
          );
          return {
            date: date.toISOString(),
            value: item.value,
            formattedDate: date.toLocaleDateString("en-US", {
              month: "short",
              year: "numeric",
            }),
          };
        });
    }

    // Apply keyword-based variations
    const keywordMultiplier = searchKeyword.toLowerCase().includes("insoles")
      ? 1.1
      : searchKeyword.toLowerCase().includes("shoes")
      ? 0.9
      : 1.0;

    // Apply region-based variations
    const regionMultiplier =
      selectedRegion === "US"
        ? 1.2
        : selectedRegion === "GB"
        ? 0.8
        : selectedRegion === "worldwide"
        ? 1.0
        : 0.9;

    // Apply multipliers
    dataPoints = dataPoints.map((point) => ({
      ...point,
      value: Math.round(
        Math.max(
          0,
          Math.min(100, point.value * keywordMultiplier * regionMultiplier)
        )
      ),
    }));

    const values = dataPoints.map((d) => d.value);
    const averageInterest = Math.round(
      values.reduce((a, b) => a + b, 0) / values.length
    );
    const peakInterest = Math.max(...values);

    // Determine trend based on first vs last quarter
    const firstQuarter =
      values.slice(0, Math.ceil(values.length / 4)).reduce((a, b) => a + b, 0) /
      Math.ceil(values.length / 4);
    const lastQuarter =
      values.slice(-Math.ceil(values.length / 4)).reduce((a, b) => a + b, 0) /
      Math.ceil(values.length / 4);
    const trend: "rising" | "falling" | "stable" =
      lastQuarter > firstQuarter + 5
        ? "rising"
        : lastQuarter < firstQuarter - 5
        ? "falling"
        : "stable";

    return {
      keyword: searchKeyword,
      data: dataPoints,
      relatedQueries: [
        `${searchKeyword} reviews`,
        `best ${searchKeyword}`,
        `${searchKeyword} amazon`,
        `${searchKeyword} walmart`,
        `${searchKeyword} cvs`,
      ],
      averageInterest,
      trend,
      peakInterest,
      region:
        selectedRegion === "worldwide"
          ? "Worldwide"
          : selectedRegion.toUpperCase(),
      timeframe: period
        .replace("_", " ")
        .replace(/\b\w/g, (l) => l.toUpperCase()),
    };
  };

  // Real Google Trends API integration
  const fetchRealGoogleTrendsData = async (searchKeyword: string) => {
    try {
      // Fetch 12-month data
      const response12m = await fetch("/api/google-trends", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          keyword: searchKeyword,
          timeframe: "past_12_months",
          geo: selectedRegion === "worldwide" ? "" : selectedRegion,
          category: category === "all" ? 0 : category,
          searchType: searchType,
        }),
      });

      // Fetch 5-year data
      const response5y = await fetch("/api/google-trends", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          keyword: searchKeyword,
          timeframe: "past_5_years",
          geo: selectedRegion === "worldwide" ? "" : selectedRegion,
          category: category === "all" ? 0 : category,
          searchType: searchType,
        }),
      });

      if (!response12m.ok) {
        throw new Error(`API Error (12m): ${response12m.status}`);
      }
      if (!response5y.ok) {
        throw new Error(`API Error (5y): ${response5y.status}`);
      }

      const apiData12m = await response12m.json();
      const apiData5y = await response5y.json();

      // Transform API response to our format
      const transformedData: GoogleTrendsData = {
        keyword: searchKeyword,
        data: apiData12m.interest_over_time.map(
          (item: { date: string; value: number }) => {
            // Use the original date string directly as the formatted date
            const formattedDate = item.date;
            return {
              date: item.date,
              value: item.value,
              formattedDate: formattedDate,
            };
          }
        ),
        fiveYearData: apiData5y.interest_over_time.map(
          (item: { date: string; value: number }) => {
            // Use the original date string directly as the formatted date
            const formattedDate = item.date;
            return {
              date: item.date,
              value: item.value,
              formattedDate: formattedDate,
            };
          }
        ),
        relatedQueries: apiData12m.related_queries || [],
        averageInterest: Math.round(
          apiData12m.interest_over_time.reduce(
            (sum: number, item: { date: string; value: number }) =>
              sum + item.value,
            0
          ) / apiData12m.interest_over_time.length
        ),
        trend: apiData12m.trend || "stable",
        peakInterest: Math.max(
          ...apiData12m.interest_over_time.map(
            (item: { date: string; value: number }) => item.value
          )
        ),
        region: "Worldwide",
        timeframe: "Past 12 months",
      };

      return transformedData;
    } catch (error) {
      console.error("Real Google Trends API error:", error);
      throw error;
    }
  };

  // Manual fetch function for Google Trends data
  const fetchTrendsData = async () => {
    if (!keyword.trim()) {
      setError("Please enter a keyword first");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Try real API first, fallback to mock data if it fails
      let data: GoogleTrendsData;

      try {
        data = await fetchRealGoogleTrendsData(keyword);

        // Add a flag to indicate this is real data
        data.isRealData = true;
        data.dataSource = "SerpAPI";
      } catch {
        // Fallback to mock data
        await new Promise((resolve) => setTimeout(resolve, 800));
        data = generateRealisticTrendsData(keyword, timePeriod, selectedRegion);

        // Add 5-year mock data
        data.fiveYearData = generateRealisticTrendsData(
          keyword,
          "past_5_years",
          selectedRegion
        ).data;

        // Add a flag to indicate this is mock data
        data.isRealData = false;
        data.dataSource = "Mock Data";
      }

      setTrendsData(data);

      // Notify parent component about the fetched data
      if (onDataFetched) {
        onDataFetched(data);
      }
    } catch (err) {
      setError("Failed to fetch Google Trends data. Please try again.");
      console.error("Google Trends API error:", err);
    } finally {
      setLoading(false);
    }
  };

  // Update stored data when prop changes
  useEffect(() => {
    if (storedData) {
      setTrendsData(storedData);
    }
  }, [storedData]);

  // Use trends data directly for chart
  const chartData = trendsData?.data || [];

  // Use real 5-year data if available, otherwise generate mock data
  const fiveYearData = trendsData?.fiveYearData || [];

  if (!keyword.trim() && !isEditing) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Google Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Enter a keyword to view Google Trends data
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <span className="text-base font-semibold">Google Trends:</span>
            {isEditing ? (
              <div className="flex items-center gap-2">
                <Input
                  value={keyword}
                  onChange={(e) => onKeywordChange?.(e.target.value)}
                  placeholder="Enter keyword..."
                  className="max-w-xs"
                />
                <Select
                  value={selectedRegion}
                  onValueChange={(value: Region) => setSelectedRegion(value)}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="worldwide">Worldwide</SelectItem>
                    <SelectItem value="US">United States</SelectItem>
                    <SelectItem value="GB">United Kingdom</SelectItem>
                    <SelectItem value="CA">Canada</SelectItem>
                    <SelectItem value="AU">Australia</SelectItem>
                    <SelectItem value="DE">Germany</SelectItem>
                    <SelectItem value="FR">France</SelectItem>
                    <SelectItem value="JP">Japan</SelectItem>
                    <SelectItem value="IN">India</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <span className="text-base font-normal">
                {keyword || "No keyword entered"}
                {keyword && (
                  <span className="text-sm text-muted-foreground ml-2">
                    (
                    {selectedRegion === "worldwide"
                      ? "Worldwide"
                      : selectedRegion}
                    )
                  </span>
                )}
              </span>
            )}
          </div>
          {isEditing && (
            <Button
              onClick={fetchTrendsData}
              disabled={loading || !keyword.trim()}
              size="sm"
              className="flex items-center gap-2 ml-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <TrendingUp className="h-4 w-4" />
              )}
              {loading ? "Fetching..." : "Fetch Google Trends"}
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading trends data...</span>
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!trendsData && !loading && !error && (
          <div className="text-center py-8 text-muted-foreground">
            {isEditing
              ? "Enter a keyword above and click 'Fetch Google Trends' to view data"
              : "No Google Trends data available"}
          </div>
        )}

        {trendsData && !loading && !error && (
          <>
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium mb-3">
                  Interest over past 12 months
                </h4>
                <div className="h-64 w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{ top: 20, right: 50, left: 20, bottom: 40 }}
                    >
                      <CartesianGrid
                        strokeDasharray="2 2"
                        stroke="#d1d5db"
                        strokeWidth={1}
                        horizontal={true}
                        vertical={false}
                      />
                      <XAxis
                        dataKey="formattedDate"
                        tick={{
                          fontSize: 10,
                          fill: "var(--foreground)",
                        }}
                        tickLine={false}
                        axisLine={false}
                        interval={0}
                        ticks={(() => {
                          const indices = [0]; // Always include first
                          const step = Math.max(
                            1,
                            Math.floor((chartData.length - 1) / 4)
                          );
                          for (
                            let i = step;
                            i < chartData.length - 1;
                            i += step
                          ) {
                            indices.push(i);
                          }
                          indices.push(chartData.length - 1); // Always include last
                          return indices
                            .map((i) => chartData[i]?.formattedDate)
                            .filter(Boolean);
                        })()}
                        minTickGap={40}
                        textAnchor="middle"
                        height={40}
                      />
                      <YAxis
                        tick={{
                          fontSize: 11,
                          fill: "var(--foreground)",
                        }}
                        tickLine={false}
                        axisLine={false}
                        domain={[0, 100]}
                        ticks={[0, 25, 50, 75, 100]}
                        tickCount={5}
                      />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            const actualValue = payload.find(
                              (p) => p.dataKey === "value"
                            )?.value;
                            return (
                              <div className="bg-background border rounded-lg p-2 shadow-lg text-xs">
                                <p className="font-medium text-foreground">
                                  {label}
                                </p>
                                <p className="text-blue-600 dark:text-blue-400">
                                  Interest: {actualValue}
                                </p>
                              </div>
                            );
                          }
                          return null;
                        }}
                        cursor={{
                          stroke: "hsl(var(--muted-foreground))",
                          strokeWidth: 1,
                          strokeDasharray: "2 2",
                        }}
                      />
                      {/* Main data line (sharp edges, no dots, thicker) */}
                      <Line
                        type="linear"
                        dataKey="value"
                        stroke="#2563eb"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{
                          r: 4,
                          fill: "#2563eb",
                          stroke: "hsl(var(--background))",
                          strokeWidth: 2,
                        }}
                        connectNulls={true}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* 5-Year Chart */}
            <div>
              <h4 className="text-sm font-medium mb-3">
                Interest over past 5 years
              </h4>
              <div className="h-64 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={fiveYearData}
                    margin={{ top: 20, right: 50, left: 20, bottom: 40 }}
                  >
                    <CartesianGrid
                      strokeDasharray="2 2"
                      stroke="#d1d5db"
                      strokeWidth={1}
                      horizontal={true}
                      vertical={false}
                    />
                    <XAxis
                      dataKey="formattedDate"
                      tick={{
                        fontSize: 11,
                        fill: "var(--foreground)",
                      }}
                      tickLine={false}
                      axisLine={false}
                      interval={0}
                      ticks={(() => {
                        const indices = [0]; // Always include first
                        const step = Math.max(
                          1,
                          Math.floor((fiveYearData.length - 1) / 3)
                        );
                        for (
                          let i = step;
                          i < fiveYearData.length - 1;
                          i += step
                        ) {
                          indices.push(i);
                        }
                        indices.push(fiveYearData.length - 1); // Always include last
                        return indices
                          .map((i) => fiveYearData[i]?.formattedDate)
                          .filter(Boolean);
                      })()}
                      minTickGap={60}
                      textAnchor="middle"
                      height={40}
                    />
                    <YAxis
                      tick={{
                        fontSize: 11,
                        fill: "var(--foreground)",
                      }}
                      tickLine={false}
                      axisLine={false}
                      domain={[0, 100]}
                      ticks={[0, 25, 50, 75, 100]}
                      tickCount={5}
                    />
                    <Tooltip
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const actualValue = payload.find(
                            (p) => p.dataKey === "value"
                          )?.value;
                          return (
                            <div className="bg-background border rounded-lg p-2 shadow-lg text-xs">
                              <p className="font-medium text-foreground">
                                {label}
                              </p>
                              <p className="text-green-600 dark:text-green-400">
                                Interest: {actualValue}
                              </p>
                            </div>
                          );
                        }
                        return null;
                      }}
                      cursor={{
                        stroke: "hsl(var(--muted-foreground))",
                        strokeWidth: 1,
                        strokeDasharray: "2 2",
                      }}
                    />
                    {/* Main data line (sharp edges, no dots, thicker) */}
                    <Line
                      type="linear"
                      dataKey="value"
                      stroke="#10b981"
                      strokeWidth={3}
                      dot={false}
                      activeDot={{
                        r: 4,
                        fill: "#10b981",
                        stroke: "hsl(var(--background))",
                        strokeWidth: 2,
                      }}
                      connectNulls={true}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
