"use client";

import { useCallback, useState, useTransition } from "react";
import {
  updateActivityReadStatus,
  markAllActivityLogsAsRead,
} from "@/actions/activity.actions";

export function useActivityActions(productId: string) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Toggle read status of an activity log
  const toggleReadStatus = useCallback(
    async (activityId: string, isCurrentlyRead: boolean): Promise<boolean> => {
      if (!productId || !activityId) return false;

      setError(null);
      
      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            await updateActivityReadStatus(activityId, productId, !isCurrentlyRead);
            resolve(true);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to update read status";
            setError(errorMessage);
            resolve(false);
          }
        });
      });
    },
    [productId]
  );

  // Mark all activity logs as read
  const markAllAsRead = useCallback(
    async (): Promise<boolean> => {
      if (!productId) return false;

      setError(null);
      
      return new Promise((resolve) => {
        startTransition(async () => {
          try {
            await markAllActivityLogsAsRead(productId);
            resolve(true);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to mark all as read";
            setError(errorMessage);
            resolve(false);
          }
        });
      });
    },
    [productId]
  );

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Actions
    toggleReadStatus,
    markAllAsRead,
    
    // State
    isPending,
    error,
    clearError,
  };
}
