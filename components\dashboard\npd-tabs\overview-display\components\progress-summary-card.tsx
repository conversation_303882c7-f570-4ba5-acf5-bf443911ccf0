"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  AlertTriangle,
  Circle,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  History,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { DeletionRequestDialog } from "@/components/ui/deletion-request-dialog";
import { toast } from "sonner";
import { sendDeletionRequestNotification } from "@/actions/overview-notifications.actions";

interface MilestoneVersion {
  id: string;
  title: string;
  status: "completed" | "in-progress" | "pending" | "blocked";
  dueDate?: string;
  description?: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
}

interface Milestone {
  id: string;
  title: string;
  status: "completed" | "in-progress" | "pending" | "blocked";
  dueDate?: string;
  description?: string;
  user?: string;
  editedAt?: string;
  editedBy?: string;
  isEdited?: boolean;
  versions?: MilestoneVersion[];
}

interface ProgressSummary {
  overallProgress: number;
  milestones: Milestone[];
}

interface ProgressSummaryCardProps {
  data?: ProgressSummary;
  isEditing: boolean;
  onUpdate: (data: ProgressSummary) => void;
  onUserMention?: (username: string) => void;
  productId?: string;
  productName?: string;
  productSlug?: string;
  tabId?: string;
}

const defaultData: ProgressSummary = {
  overallProgress: 0,
  milestones: [],
};

const statusIcons = {
  completed: <CheckCircle className="h-4 w-4 text-green-500" />,
  "in-progress": <Clock className="h-4 w-4" />,
  pending: <Circle className="h-4 w-4" />,
  blocked: <AlertTriangle className="h-4 w-4" />,
};

const statusColors = {
  completed: "bg-green-100 text-green-800 border-green-200",
  "in-progress": "bg-blue-100 text-blue-800 border-blue-200",
  pending: "bg-gray-100 text-gray-800 border-gray-200",
  blocked: "bg-red-100 text-red-800 border-red-200",
};

export function ProgressSummaryCard({
  data = defaultData,
  isEditing,
  onUpdate,
  onUserMention,
  productId,
  productName,
  productSlug,
  tabId,
}: ProgressSummaryCardProps) {
  const [localData, setLocalData] = useState<ProgressSummary>(data);
  const [isManualProgress, setIsManualProgress] = useState(false);
  const [expandedVersions, setExpandedVersions] = useState<Set<string>>(
    new Set()
  );
  const [showDeletionDialog, setShowDeletionDialog] = useState(false);
  const [milestoneToDelete, setMilestoneToDelete] = useState<Milestone | null>(
    null
  );
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { user } = useAuth();

  const handleUserMention = (username: string) => {
    if (onUserMention) {
      onUserMention(username);
    }
  };

  // Calculate automatic progress based on completed milestones
  const calculateAutoProgress = (milestones: Milestone[]) => {
    if (milestones.length === 0) return 0;
    const completedCount = milestones.filter(
      (m) => m.status === "completed"
    ).length;
    return Math.round((completedCount / milestones.length) * 100);
  };

  const updateLocalData = (updates: Partial<ProgressSummary>) => {
    const newData = { ...localData, ...updates };

    // If milestones are updated and progress is not manual, auto-calculate progress
    if (updates.milestones && !isManualProgress) {
      newData.overallProgress = calculateAutoProgress(updates.milestones);
    }

    setLocalData(newData);
    onUpdate(newData);
  };

  // Handle manual progress change
  const handleManualProgressChange = (value: number) => {
    setIsManualProgress(true);
    updateLocalData({ overallProgress: value });
  };

  // Reset to auto-calculation
  const resetToAutoProgress = () => {
    setIsManualProgress(false);
    const autoProgress = calculateAutoProgress(localData.milestones);
    updateLocalData({ overallProgress: autoProgress });
  };

  const addMilestone = () => {
    const newMilestone: Milestone = {
      id: Date.now().toString(),
      title: "",
      status: "pending",
      description: "",
      user: user?.name || "", // Automatically populate with current user's name
      isEdited: false, // Explicitly set as not edited for new entries
    };
    updateLocalData({
      milestones: [...localData.milestones, newMilestone],
    });
  };

  const updateMilestone = (id: string, updates: Partial<Milestone>) => {
    const updatedMilestones = localData.milestones.map((milestone) =>
      milestone.id === id ? { ...milestone, ...updates } : milestone
    );
    updateLocalData({ milestones: updatedMilestones });
  };

  const removeMilestone = (id: string) => {
    const milestone = localData.milestones.find((m) => m.id === id);
    if (!milestone || !user) return;

    const isOwnEntry = milestone.user === user.name;

    if (isOwnEntry) {
      // User can delete their own entry
      const filteredMilestones = localData.milestones.filter(
        (m) => m.id !== id
      );
      updateLocalData({ milestones: filteredMilestones });
    } else {
      // Show deletion request dialog for others' entries
      setMilestoneToDelete(milestone);
      setShowDeletionDialog(true);
    }
  };

  const handleSendDeletionRequest = async () => {
    if (
      !milestoneToDelete ||
      !productId ||
      !productName ||
      !productSlug ||
      !tabId ||
      !milestoneToDelete.user
    ) {
      return;
    }

    const result = await sendDeletionRequestNotification(
      milestoneToDelete.user,
      "milestone",
      milestoneToDelete.title,
      productId,
      productName,
      productSlug,
      tabId,
      milestoneToDelete.id
    );

    // Reset state
    setMilestoneToDelete(null);

    // Show result message
    if (result.success) {
      toast.success("Deletion request sent", {
        description: result.message,
      });
    } else {
      toast.error("Failed to send deletion request", {
        description: result.message,
      });
    }
  };

  const toggleVersions = (milestoneId: string) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(milestoneId)) {
      newExpanded.delete(milestoneId);
    } else {
      newExpanded.add(milestoneId);
    }
    setExpandedVersions(newExpanded);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-500" />
            Progress Summary
          </CardTitle>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
          >
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="space-y-6">
          {/* Overall Progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {localData.overallProgress}%
                </span>
                {isEditing && localData.milestones.length > 0 && (
                  <Badge
                    variant={isManualProgress ? "secondary" : "default"}
                    className="text-xs"
                  >
                    {isManualProgress ? "Manual" : "Auto"}
                  </Badge>
                )}
              </div>
            </div>
            {isEditing ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={localData.overallProgress}
                    onChange={(e) =>
                      handleManualProgressChange(parseInt(e.target.value) || 0)
                    }
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">%</span>
                  {isManualProgress && localData.milestones.length > 0 && (
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={resetToAutoProgress}
                      className="text-xs"
                    >
                      Auto-calc
                    </Button>
                  )}
                </div>
                {localData.milestones.length > 0 && (
                  <p className="text-xs text-muted-foreground">
                    {isManualProgress
                      ? "Progress set manually. Click 'Auto-calc' to calculate from milestones."
                      : `Auto-calculated from milestones (${
                          localData.milestones.filter(
                            (m) => m.status === "completed"
                          ).length
                        }/${localData.milestones.length} completed)`}
                  </p>
                )}
              </div>
            ) : (
              <Progress value={localData.overallProgress} className="h-2" />
            )}
          </div>

          {/* Milestones */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Milestones</h4>
              {isEditing && (
                <Button size="sm" variant="outline" onClick={addMilestone}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              )}
            </div>

            <div className="space-y-2">
              {localData.milestones.map((milestone) => (
                <div
                  key={milestone.id}
                  id={`entry-milestone-${milestone.id}`}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  {statusIcons[milestone.status]}

                  {isEditing ? (
                    <div className="flex-1 space-y-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <Input
                          placeholder="Milestone title"
                          value={milestone.title}
                          onChange={(e) =>
                            updateMilestone(milestone.id, {
                              title: e.target.value,
                            })
                          }
                        />
                        <Select
                          value={milestone.status}
                          onValueChange={(value: Milestone["status"]) =>
                            updateMilestone(milestone.id, { status: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="in-progress">
                              In Progress
                            </SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="blocked">Blocked</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          type="date"
                          value={milestone.dueDate || ""}
                          onChange={(e) =>
                            updateMilestone(milestone.id, {
                              dueDate: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        {/* Version toggle icon on the left */}
                        {milestone.versions &&
                          milestone.versions.length > 0 && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => toggleVersions(milestone.id)}
                              className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
                            >
                              {expandedVersions.has(milestone.id) ? (
                                <ChevronDown className="h-3 w-3" />
                              ) : (
                                <ChevronRight className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        <span className="font-medium">{milestone.title}</span>
                        {milestone.isEdited && (
                          <Badge variant="secondary" className="text-xs">
                            edited
                          </Badge>
                        )}
                        <Badge className={statusColors[milestone.status]}>
                          {milestone.status.replace("-", " ")}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        {milestone.dueDate && (
                          <span>
                            Due:{" "}
                            {new Date(milestone.dueDate).toLocaleDateString()}
                          </span>
                        )}
                        {milestone.user && (
                          <button
                            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm bg-blue-50 px-1 py-0.5 rounded-sm"
                            onClick={() =>
                              milestone.user &&
                              handleUserMention(milestone.user)
                            }
                          >
                            @{milestone.user}
                          </button>
                        )}
                      </div>

                      {/* Previous versions */}
                      {milestone.versions &&
                        milestone.versions.length > 0 &&
                        expandedVersions.has(milestone.id) && (
                          <div className="mt-3 pt-3 border-t border-dashed">
                            <div className="flex items-center gap-2 mb-2">
                              <History className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground font-medium">
                                Previous Versions
                              </span>
                            </div>
                            <div className="space-y-2 ml-4">
                              {[...milestone.versions]
                                .reverse()
                                .map((version) => (
                                  <div
                                    key={version.id}
                                    className="p-2 bg-muted/30 rounded text-sm"
                                  >
                                    <div className="font-medium text-muted-foreground">
                                      {version.title}
                                    </div>
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                                      <span>by @{version.user}</span>
                                      {version.dueDate && (
                                        <>
                                          <span>•</span>
                                          <span>
                                            due{" "}
                                            {new Date(
                                              version.dueDate
                                            ).toLocaleDateString()}
                                          </span>
                                        </>
                                      )}
                                      <Badge variant="outline">
                                        {version.status.replace("-", " ")}
                                      </Badge>
                                    </div>
                                    {version.description && (
                                      <div className="text-xs text-muted-foreground mt-1">
                                        {version.description}
                                      </div>
                                    )}
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}
                    </div>
                  )}

                  {isEditing && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeMilestone(milestone.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              {localData.milestones.length === 0 && !isEditing && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No milestones added yet
                </p>
              )}
            </div>
          </div>
        </CardContent>
      )}

      {/* Deletion Request Dialog */}
      <DeletionRequestDialog
        open={showDeletionDialog}
        onOpenChange={setShowDeletionDialog}
        creatorUsername={milestoneToDelete?.user || ""}
        entryType="milestone"
        entryTitle={milestoneToDelete?.title || ""}
        onSendRequest={handleSendDeletionRequest}
        onCancel={() => setMilestoneToDelete(null)}
      />
    </Card>
  );
}
