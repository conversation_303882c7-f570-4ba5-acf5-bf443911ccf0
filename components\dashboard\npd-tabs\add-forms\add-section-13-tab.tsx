"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tab<PERSON>eader, LoadingButton } from "../shared";
import { BarChart3, Download, ArrowRightLeft } from "lucide-react";
import { getTabOrder } from "@/lib/constants/tab-order";
import { toast } from "sonner";

interface TabData {
  tabName: string;
  order: number;
  fields: Record<string, unknown>;
}

interface AddSection13TabProps {
  productName: string;
  onBack: () => void;
  onSave: (tabData: TabData) => Promise<void>;
}

export function AddSection13Tab({
  productName,
  onBack,
  onSave,
}: AddSection13TabProps) {
  const [isLoading, setIsLoading] = useState(false);

  const formData = {
    transposedData: [],
    user: null,
    editedAt: new Date().toISOString(),
    editedBy: null,
    isEdited: false,
    versions: [],
  };

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    e?.preventDefault();
    setIsLoading(true);

    try {
      await onSave({
        tabName: "1.3",
        order: getTabOrder("1.3"),
        fields: formData,
      });
      // Success toast will be shown after page reload via URL parameters
    } catch (error) {
      console.error("Error creating section 1.3 tab:", error);
      toast.error("Failed to create section 1.3 tab. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <TabHeader
        title="Add 1.3 Tab"
        description="Create a transposed competitor analysis tab for {productName}"
        productName={productName}
        onBack={onBack}
        actionButton={
          <LoadingButton
            isLoading={isLoading}
            onClick={handleSubmit}
            loadingText="Creating..."
            defaultText="Create Section 1.3"
          />
        }
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Tab Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Section 1.3 Tab
              </CardTitle>
              <CardDescription>
                This tab will provide a transposed view of competitor analysis
                data from tab 1.1, making it easier to compare competitors
                across different metrics.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <ArrowRightLeft className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-medium">Data Transposition</h3>
                    <p className="text-sm text-muted-foreground">
                      Converts row-based competitor data to column-based view
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <Download className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium">Auto Import</h3>
                    <p className="text-sm text-muted-foreground">
                      Automatically imports data from tab 1.1 when available
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-8 w-8 text-purple-600" />
                  <div>
                    <h3 className="font-medium">Competitor Comparison</h3>
                    <p className="text-sm text-muted-foreground">
                      Easy side-by-side comparison of competitor metrics
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <BarChart3 className="h-8 w-8 text-orange-600" />
                  <div>
                    <h3 className="font-medium">Editable Analysis</h3>
                    <p className="text-sm text-muted-foreground">
                      Edit and customize imported data for deeper analysis
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What you'll get */}
          <Card>
            <CardHeader>
              <CardTitle>What you&apos;ll get</CardTitle>
              <CardDescription>
                Your section 1.3 tab will include these features that you can
                use and customize after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Transposed Data View</Badge>
                  <span className="text-sm text-muted-foreground">
                    Fields become rows, competitors become columns for easy
                    comparison
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Auto Import</Badge>
                  <span className="text-sm text-muted-foreground">
                    Automatically imports competitor data from tab 1.1 when
                    available
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Manual Import Button</Badge>
                  <span className="text-sm text-muted-foreground">
                    Import or refresh data from tab 1.1 at any time in edit mode
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Editable Cells</Badge>
                  <span className="text-sm text-muted-foreground">
                    Edit individual competitor data points with visual change
                    tracking
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Starting Fields</Badge>
                  <span className="text-sm text-muted-foreground">
                    Brand, Price, ASIN Sales, ASIN Revenue, and BSR (more can be
                    added later)
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
