"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AuthModal } from "@/components/auth-modal";
import Spinner from "@/components/spinner";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";

export default function HomePage() {
  const { user, isLoading } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  return (
    <div
      className="flex flex-col h-screen bg-cover bg-center bg-no-repeat text-foreground"
      style={{
        backgroundImage:
          "url('https://images.unsplash.com/photo-1715635845783-a184542d95e5?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
      }}
    >
      {/* Main Content */}
      <div className="flex flex-col items-center justify-center flex-1 bg-muted/30">
        {/* Hero Section */}
        <div className="text-center max-w-2xl">
          <h1 className="text-4xl font-bold sm:text-5xl text-primary bg-muted/10 px-4 py-2 rounded-lg shadow-md">
            Welcome to Quick Ticket
          </h1>
          <p className="mt-4 text-lg">
            Simplify your support process with our efficient ticket system.
            <br />
            Track, manage, and resolve issues faster than ever.
          </p>
        </div>

        {/* Call-to-Action Buttons */}
        <div className="mt-8">
          {isLoading ? (
            <Button disabled>
              <Spinner loading={true} />
            </Button>
          ) : user ? (
            <Button asChild>
              <Link href="/dashboard">Get Started</Link>
            </Button>
          ) : (
            <Button onClick={() => setIsAuthModalOpen(true)}>Sign In</Button>
          )}
        </div>

        {/* Features Section */}
        <div className="m-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 max-w-5xl">
          <Card>
            <CardContent>
              <h3 className="text-xl font-semibold">
                Efficient Ticket Management
              </h3>
              <p className="mt-2 text-muted-foreground">
                Organize and prioritize support tickets with ease.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent>
              <h3 className="text-xl font-semibold">Real-Time Updates</h3>
              <p className="mt-2 text-muted-foreground">
                Stay informed with instant notifications and updates.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent>
              <h3 className="text-xl font-semibold">Customizable Workflows</h3>
              <p className="mt-2 text-muted-foreground">
                Tailor the system to fit your team&apos;s unique needs.
              </p>
            </CardContent>
          </Card>
        </div>

        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
        />
      </div>
    </div>
  );
}
