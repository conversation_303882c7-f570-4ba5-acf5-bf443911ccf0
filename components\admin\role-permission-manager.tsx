"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Save, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import Spinner from "@/components/spinner";
// Import removed - using API endpoints instead

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}

interface Permission {
  id: string;
  code: string;
}

export function RolePermissionManager() {
  const { user: currentUser } = useAuth();
  const [roles, setRoles] = useState<Role[]>([]);
  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      const [rolesResponse, permissionsResponse] = await Promise.all([
        fetch("/api/admin/roles"),
        fetch("/api/admin/permissions"),
      ]);

      if (rolesResponse.ok && permissionsResponse.ok) {
        const rolesData = await rolesResponse.json();
        const permissionsData = await permissionsResponse.json();

        setRoles(rolesData || []);
        setAllPermissions(permissionsData.permissions || permissionsData || []);
      } else {
        console.error("Failed to fetch roles or permissions");
        if (!rolesResponse.ok) {
          const rolesError = await rolesResponse.text();
          console.error("Roles API error:", rolesError);
        }
        if (!permissionsResponse.ok) {
          const permissionsError = await permissionsResponse.text();
          console.error("Permissions API error:", permissionsError);
        }
        toast.error("Failed to load data. Check console for details.");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Error loading roles and permissions");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleRoleSelect = async (roleId: string) => {
    const role = (roles || []).find((r) => r.id === roleId);
    if (!role) return;

    try {
      const response = await fetch(`/api/admin/roles/${roleId}/permissions`);
      if (response.ok) {
        const roleWithPermissions = await response.json();

        setSelectedRole(roleWithPermissions);
        const permissionIds = (roleWithPermissions.permissions || []).map(
          (p: Permission) => p.id
        );
        setSelectedPermissions(permissionIds);
      } else {
        console.error(
          "Failed to load role permissions, status:",
          response.status
        );
        const errorText = await response.text();
        console.error("Error response:", errorText);
        toast.error("Failed to load role permissions");
      }
    } catch (error) {
      console.error("Error fetching role permissions:", error);
      toast.error("Error loading role permissions");
    }
  };

  const handlePermissionToggle = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions((prev) => [...prev, permissionId]);
    } else {
      setSelectedPermissions((prev) =>
        prev.filter((id) => id !== permissionId)
      );
    }
  };

  const handleSave = async () => {
    if (!selectedRole) return;

    try {
      setSaving(true);

      const response = await fetch(
        `/api/admin/roles/${selectedRole.id}/permissions`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            permissionIds: selectedPermissions,
          }),
        }
      );

      if (response.ok) {
        toast.success("Role permissions updated successfully");
        await fetchData(); // Refresh data

        // Update selected role with new permissions
        const updatedRole = await response.json();
        setSelectedRole(updatedRole);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update permissions");
      }
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Error updating permissions");
    } finally {
      setSaving(false);
    }
  };

  // Filter roles based on current user permissions
  const availableRoles = (roles || []).filter((role) => {
    // Super admins can see all roles
    if (currentUser?.roles?.includes("SUPER_ADMIN")) {
      return true;
    }
    // Regular admins cannot see SUPER_ADMIN role
    return role.name !== "SUPER_ADMIN";
  });

  // Group permissions by category for better organization
  const groupedPermissions = (allPermissions || []).reduce(
    (groups, permission) => {
      const category = permission.code.split("_")[0]; // First part before underscore
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(permission);
      return groups;
    },
    {} as Record<string, Permission[]>
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Role Permission Management</CardTitle>
          <CardDescription>Manage permissions for each role</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Spinner loading={true} size={12} />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Role Permission Management</CardTitle>
        <CardDescription>
          Select a role and manage its permissions. Changes are saved to the
          database.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Select onValueChange={handleRoleSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Select a role to manage" />
              </SelectTrigger>
              <SelectContent>
                {availableRoles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    {role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchData}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        {selectedRole && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">
                  Permissions for {selectedRole.name || "Unknown Role"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {selectedPermissions.length} of {allPermissions.length}{" "}
                  permissions selected
                </p>
                {/* Debug info */}
                <div className="text-xs text-muted-foreground mt-1">
                  Debug: Selected role ID: {selectedRole.id}, Selected
                  permissions: [{selectedPermissions.join(", ")}]
                </div>
              </div>
              <Button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center gap-2"
              >
                {saving ? (
                  <Spinner loading={true} size={8} />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Save Changes
              </Button>
            </div>

            <div className="grid gap-6">
              {Object.entries(groupedPermissions).map(
                ([category, permissions]) => (
                  <div key={category} className="space-y-3">
                    <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">
                      {category.replace("_", " ")} Permissions
                    </h4>
                    <div className="grid gap-3 pl-4">
                      {permissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="flex items-center space-x-3"
                        >
                          <Checkbox
                            id={permission.id}
                            checked={selectedPermissions.includes(
                              permission.id
                            )}
                            onCheckedChange={(checked) => {
                              handlePermissionToggle(
                                permission.id,
                                checked as boolean
                              );
                            }}
                          />
                          <label
                            htmlFor={permission.id}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1"
                          >
                            {permission.code}
                          </label>
                          <Badge variant="outline" className="text-xs">
                            {permission.code.replace(/_/g, " ").toLowerCase()}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
