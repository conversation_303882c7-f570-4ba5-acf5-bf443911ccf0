"use client";

import { useCallback, useState, useEffect, useRef } from "react";
import {
  getChatMessages,
  getChatUnreadCount,
  type ChatMessage,
  type ChatMessagesResponse,
  type ChatUnreadData,
} from "@/actions/chat.actions";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";

export function useChatData(productId: string, enabled: boolean = true) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [unreadData, setUnreadData] = useState<ChatUnreadData>({
    unreadCount: 0,
    totalCount: 0,
    readCount: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for polling
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch chat messages
  const fetchMessages = useCallback(
    async (before?: string, append: boolean = false) => {
      if (!productId || !enabled) {
        setMessages([]);
        setIsLoading(false);
        return;
      }

      try {
        setError(null);
        if (!append) setIsLoading(true);
        else setIsLoadingMore(true);

        const result: ChatMessagesResponse = await getChatMessages(
          productId,
          50,
          before
        );

        if (append) {
          setMessages((prev) => [...prev, ...result.messages]);
        } else {
          setMessages(result.messages);
        }

        setHasMore(result.hasMore);
      } catch (error) {
        console.error("Error fetching chat messages:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch messages";
        setError(errorMessage);
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [productId, enabled]
  );

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    if (!productId || !enabled) {
      setUnreadData({ unreadCount: 0, totalCount: 0, readCount: 0 });
      return;
    }

    try {
      const result: ChatUnreadData = await getChatUnreadCount(productId);
      setUnreadData(result);
    } catch (error) {
      console.error("Error fetching unread count:", error);
      // Don't set error for unread count failures, just log them
    }
  }, [productId, enabled]);

  // Load more messages (pagination)
  const loadMore = useCallback(() => {
    if (isLoadingMore || !hasMore || messages.length === 0) return;

    const oldestMessage = messages[messages.length - 1];
    if (oldestMessage) {
      fetchMessages(oldestMessage.createdAt, true);
    }
  }, [fetchMessages, isLoadingMore, hasMore, messages]);

  // Refresh all data
  const refresh = useCallback(() => {
    fetchMessages();
    fetchUnreadCount();
  }, [fetchMessages, fetchUnreadCount]);

  // Setup polling for real-time updates
  useEffect(() => {
    if (!productId || !enabled || !isPollingEnabled("NOTE_READ_STATUS")) return;

    const interval = getPollingInterval("NOTE_READ_STATUS");

    const poll = () => {
      // Only poll unread count, not messages (to avoid disrupting user reading)
      fetchUnreadCount();
    };

    pollTimeoutRef.current = setTimeout(function pollInterval() {
      poll();
      pollTimeoutRef.current = setTimeout(pollInterval, interval);
    }, interval);

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, [productId, enabled, fetchUnreadCount]);

  // Initial fetch with delay to prevent blocking product switching
  useEffect(() => {
    if (!enabled) return;

    // Add a small delay to prevent blocking product switching
    const timer = setTimeout(() => {
      refresh();
    }, 150); // 150ms delay (slightly more than activity to stagger)

    return () => clearTimeout(timer);
  }, [refresh, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, []);

  // Update a message in the local state (optimistic updates)
  const updateMessageInState = useCallback((updatedMessage: ChatMessage) => {
    setMessages((prev) =>
      prev.map((msg) => (msg.id === updatedMessage.id ? updatedMessage : msg))
    );
  }, []);

  // Add a new message to the local state (optimistic updates)
  const addMessageToState = useCallback((newMessage: ChatMessage) => {
    setMessages((prev) => [...prev, newMessage]);
    // Update unread count
    setUnreadData((prev) => ({
      ...prev,
      totalCount: prev.totalCount + 1,
      readCount: prev.readCount + 1, // New message is automatically read by sender
    }));
  }, []);

  // Remove a message from the local state (optimistic updates)
  const removeMessageFromState = useCallback((messageId: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
    // Update counts
    setUnreadData((prev) => ({
      ...prev,
      totalCount: Math.max(0, prev.totalCount - 1),
      // readCount might need adjustment, but we'll let the next poll handle it
    }));
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Data
    messages,
    unreadCount: unreadData.unreadCount,
    totalCount: unreadData.totalCount,
    readCount: unreadData.readCount,

    // Loading states
    isLoading,
    isLoadingMore,
    hasMore,
    error,

    // Actions
    refresh,
    loadMore,
    clearError,

    // Optimistic update helpers
    updateMessageInState,
    addMessageToState,
    removeMessageFromState,
  };
}
