"use client";

import { useCallback, useState, useEffect, useRef } from "react";
import { getActivityUnreadCount } from "@/actions/activity.actions";
import { getChatUnreadCount } from "@/actions/chat.actions";
import { getPollingInterval, isPollingEnabled } from "@/lib/constants/polling";

interface UnreadCounts {
  activityUnreadCount: number;
  chatUnreadCount: number;
}

export function useUnreadCounts(productId: string, enabled: boolean = true) {
  const [unreadCounts, setUnreadCounts] = useState<UnreadCounts>({
    activityUnreadCount: 0,
    chatUnreadCount: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch only unread counts (lightweight)
  const fetchUnreadCounts = useCallback(async () => {
    if (!productId || !enabled) {
      setUnreadCounts({ activityUnreadCount: 0, chatUnreadCount: 0 });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch both unread counts in parallel (lightweight calls)
      const [activityResult, chatResult] = await Promise.all([
        getActivityUnreadCount(productId),
        getChatUnreadCount(productId),
      ]);

      setUnreadCounts({
        activityUnreadCount: activityResult.unreadCount,
        chatUnreadCount: chatResult.unreadCount,
      });
    } catch (error) {
      console.error("Error fetching unread counts:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch unread counts";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [productId, enabled]);

  // Setup polling for real-time updates (only for unread counts)
  useEffect(() => {
    if (!productId || !enabled || !isPollingEnabled("ACTIVITY_READ_STATUS"))
      return;

    const interval = getPollingInterval("ACTIVITY_READ_STATUS");

    const poll = () => {
      // Only poll unread counts, not full data
      fetchUnreadCounts();
    };

    pollTimeoutRef.current = setTimeout(function pollInterval() {
      poll();
      pollTimeoutRef.current = setTimeout(pollInterval, interval);
    }, interval);

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, [productId, enabled, fetchUnreadCounts]);

  // Initial fetch with small delay to prevent blocking product switching
  useEffect(() => {
    if (!enabled) return;

    // Small delay for unread counts (lightweight operation)
    const timer = setTimeout(() => {
      fetchUnreadCounts();
    }, 50); // 50ms delay

    return () => clearTimeout(timer);
  }, [fetchUnreadCounts, enabled]);

  return {
    ...unreadCounts,
    isLoading,
    error,
    refresh: fetchUnreadCounts,
  };
}
