import Image from "next/image";
import Link from "next/link";
import { APP_NAME } from "@/lib/constants";
import Menu from "./menu";

const Header = () => {
  return (
    <header className="w-full border-b">
      <div className="wrapper flex-between">
        <div className="flex-start">
          <Link href="/" className="flex-start">
            <Image
              src="/images/logo.svg"
              alt={`${APP_NAME} Logo`}
              height={0}
              width={0}
              priority={true}
              style={{ width: "auto", height: "32px" }}
            />
            <span className="hidden md:block font-bold text-2xl ml-2">
              {APP_NAME}
            </span>
          </Link>
        </div>
        <Menu showNotifications={true} />
      </div>
    </header>
  );
};

export default Header;
