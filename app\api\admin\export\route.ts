import { NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";
import { getCurrentUser } from "@/lib/auth";
import type {
  Permission,
  Role,
  User,
  NPDProduct,
  NPDProductTab,
  NPDProductTabHistory,
  Notification,
  NPDProductActivityLog,
  NPDProductActivityReadStatus,
  NPDProductChat,
  NPDProductChatReadStatus,
  NPDProductSubscription,
} from "@/lib/generated/prisma";

interface ExportData {
  metadata: {
    exportedAt: string;
    version: string;
    totalRecords: number;
    models: Record<string, number>;
  };
  data: {
    permissions: Permission[];
    roles: (Role & {
      permissions: { id: string }[];
      users: { id: string }[];
    })[];
    users: (User & { roles: { id: string }[] })[];
    npdProducts: (NPDProduct & { user: { id: string } | null })[];
    npdProductTabs: NPDProductTab[];
    npdProductTabHistory: NPDProductTabHistory[];
    notifications: Notification[];
    npdProductActivityLogs: NPDProductActivityLog[];
    npdProductActivityReadStatus: NPDProductActivityReadStatus[];
    npdProductChats: NPDProductChat[];
    npdProductChatReadStatus: NPDProductChatReadStatus[];
    npdProductSubscriptions: NPDProductSubscription[];
  };
}

export async function GET() {
  try {
    // Check authentication and admin privileges
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    const hasAdminRole =
      user.roles.includes("ADMIN") || user.roles.includes("SUPER_ADMIN");

    if (!hasAdminRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Export all data in proper order (respecting foreign key dependencies)
    const [
      permissions,
      roles,
      users,
      npdProducts,
      npdProductTabs,
      npdProductTabHistory,
      notifications,
      npdProductActivityLogs,
      npdProductActivityReadStatus,
      npdProductChats,
      npdProductChatReadStatus,
      npdProductSubscriptions,
    ] = await Promise.all([
      // Base models first
      prisma.permission.findMany(),
      prisma.role.findMany({
        include: {
          permissions: { select: { id: true } },
          users: { select: { id: true } },
        },
      }),
      prisma.user.findMany({
        include: {
          roles: { select: { id: true } },
        },
      }),
      prisma.nPDProduct.findMany({
        include: {
          user: { select: { id: true } },
        },
      }),

      // Dependent models
      prisma.nPDProductTab.findMany(),
      prisma.nPDProductTabHistory.findMany(),
      prisma.notification.findMany(),
      prisma.nPDProductActivityLog.findMany(),
      prisma.nPDProductActivityReadStatus.findMany(),
      prisma.nPDProductChat.findMany(),
      prisma.nPDProductChatReadStatus.findMany(),
      prisma.nPDProductSubscription.findMany(),
    ]);

    // Calculate totals
    const totalRecords =
      permissions.length +
      roles.length +
      users.length +
      npdProducts.length +
      npdProductTabs.length +
      npdProductTabHistory.length +
      notifications.length +
      npdProductActivityLogs.length +
      npdProductActivityReadStatus.length +
      npdProductChats.length +
      npdProductChatReadStatus.length +
      npdProductSubscriptions.length;

    const exportData: ExportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        version: "1.0.0",
        totalRecords,
        models: {
          permissions: permissions.length,
          roles: roles.length,
          users: users.length,
          npdProducts: npdProducts.length,
          npdProductTabs: npdProductTabs.length,
          npdProductTabHistory: npdProductTabHistory.length,
          notifications: notifications.length,
          npdProductActivityLogs: npdProductActivityLogs.length,
          npdProductActivityReadStatus: npdProductActivityReadStatus.length,
          npdProductChats: npdProductChats.length,
          npdProductChatReadStatus: npdProductChatReadStatus.length,
          npdProductSubscriptions: npdProductSubscriptions.length,
        },
      },
      data: {
        permissions,
        roles,
        users,
        npdProducts,
        npdProductTabs,
        npdProductTabHistory,
        notifications,
        npdProductActivityLogs,
        npdProductActivityReadStatus,
        npdProductChats,
        npdProductChatReadStatus,
        npdProductSubscriptions,
      },
    };

    // Return as downloadable JSON file
    const response = new NextResponse(JSON.stringify(exportData, null, 2), {
      headers: {
        "Content-Type": "application/json",
        "Content-Disposition": `attachment; filename="database-export-${
          new Date().toISOString().split("T")[0]
        }.json"`,
      },
    });

    return response;
  } catch (error) {
    console.error("Export error:", error);
    return NextResponse.json(
      { error: "Failed to export database" },
      { status: 500 }
    );
  }
}
